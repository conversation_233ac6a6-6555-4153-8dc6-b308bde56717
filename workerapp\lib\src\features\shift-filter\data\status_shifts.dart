

import 'package:work_link/src/features/shifts/data/shift_category.dart';

final List<ShiftCategoryStatus> statusShifts = [
  ShiftCategoryStatus(status: 'NEW', category: 'New Shifts'),
  ShiftCategoryStatus(
      status: 'AWAITING_AUTHORIZATION', category: 'Awaiting Authorization'),
  ShiftCategoryStatus(status: 'APPLIED', category: 'Applied Shifts'),
  ShiftCategoryStatus(status: 'BOOKED', category: 'Booked Shifts'),
  ShiftCategoryStatus(status: 'AUTHORIZED', category: 'Authorized Shifts'),
  ShiftCategoryStatus(status: 'IN_QUERY', category: 'Queried Shifts'),
  ShiftCategoryStatus(status: 'CANCELLED', category: 'Closed Shifts'),
];
