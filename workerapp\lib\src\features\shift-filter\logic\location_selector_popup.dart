import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/models/custom_exception.dart';
import 'package:work_link/src/models/shift_location_agency.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

class ApiFilter {
  final bool getDefault;
  final int? id;

  ApiFilter({this.getDefault = true, this.id});
}

final _locProvider =
    AutoDisposeFutureProvider.family<dynamic, ApiFilter>((ref, locFilter) {
  final api = ref.read(dataRepoProvider);

  final _api = locFilter.getDefault
      ? api.getShiftLocations()
      : api.getMappedClientShiftLocations(locFilter.id!);

  return _api;
});

locationSelector(
  BuildContext context,
  ApiFilter locationApiFilter,
) async {
  return await showMaterialModalBottomSheet(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(30),
        topRight: Radius.circular(30),
      ),
    ),
    context: context,
    useRootNavigator: true,
    builder: (context) => Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: SingleChildScrollView(
        controller: ModalScrollController.of(context),
        child: Consumer(
          builder: (context1, watch, child) {
            final api =watch.watch(_locProvider(locationApiFilter));

            return RelativeBuilder(builder: (context, height, width, sy, sx) {
              return Container(
                height: 300,
                width: width,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: const Radius.circular(20.0),
                    topRight: const Radius.circular(20.0),
                  ),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    //  mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.all(15.0),
                        child: Text('Select Location')
                            .textColor(Colors.black54)
                            .fontSize(sx(25)),
                      ),
                      api.when(
                        data: (data) {
                          final List<ShiftLocationAgency> wa = data;

                          return wa.isEmpty
                              ? Center(
                                  child: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 50),
                                  child: Text('no matched locations found')
                                      .textColor(Colors.grey),
                                ))
                              : ListView.separated(
                                  physics: NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  itemBuilder: (ctx, index) {
                                    var item = wa[index];

                                    return ListTile(
                                      onTap: () => Navigator.pop(context, {
                                        "id": item.id!,
                                        "name": item.name!,
                                        "clientId": item.clientId,
                                        "client": item.client!,
                                      }),
                                      leading: CircleAvatar(
                                        child: Icon(Icons.location_pin),
                                      ),
                                      tileColor: tileColor,
                                      title: Text(item.name!)
                                          .fontWeight(FontWeight.w600),
                                      subtitle: Text(item.client!),
                                    );
                                  },
                                  separatorBuilder: (ctx, x) =>
                                      Divider(height: 10),
                                  itemCount: wa.length,
                                );
                        },
                        loading: () => Center(
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: CircularProgressIndicator(),
                          ),
                        ),
                        error: (e, st) {
                          if (e is CustomException) {
                            return ErrorPage(
                              error: e.message,
                              stackTrace: st,onTryAgain: () => watch.refresh(_locProvider(locationApiFilter)),
                            );
                          } else {
                            return ErrorPage(
                              error: 'failed to get locations',
                              stackTrace: st,onTryAgain: () => watch.refresh(_locProvider(locationApiFilter)),
                            );
                          }
                        },
                      ),
                    ],
                  ),
                ),
              );
            });
          },
        ),
      ),
    ),
  );
}
