import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ndialog/ndialog.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/models/shift/Shift.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/features/shifts/data/shift_category.dart';
import 'package:work_link/src/features/shifts/logic/select_agency_input.dart';
import 'package:work_link/src/features/shifts/logic/select_agency_input_booking.dart';
import 'package:work_link/src/features/shifts/logic/shift_reason_input.dart';
import 'package:work_link/src/features/shifts/views/claim_expenses.dart';
import 'package:work_link/src/features/shifts/views/view_single_shift.dart';
import '../../../utils/constants.dart';
import '../../car_pooling/car_pooling_page.dart';
import 'view_transport.dart';
import 'package:work_link/src/models/custom_exception.dart';
import 'package:work_link/src/models/shift/ShiftsPageResp.dart';

import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../../utils/color_constants.dart';
import '../../../models/shift_expense_claim/ShiftExpenseClaim.dart';
import '../../../providers/data_repositories/ShiftAPIs.dart';
import 'trailing_header.dart';

final _shiftProvider =
AutoDisposeFutureProviderFamily<ShiftsPageResp?, String>(
        (ref, status) {
      final _shift = ref.watch(shiftRepoProvider);
      final _profile = ref.watch(loginResponseProvider);

      return _shift.getShiftByStatus(
          status: status, workerId: _profile?.workerId ?? 1);
    });

class ShiftView extends ConsumerWidget {
  List<ShiftExpenseClaim> claims = [];

  ShiftView({Key? key, required this.status}) : super(key: key);

  final ShiftCategoryStatus status;

  final formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context,   watch) {
    final shiftRepo =watch.watch(shiftRepoProvider);
    final transportRepo =watch.watch(transportRepoProvider);
    final _profile =watch.watch(loginResponseProvider);
    final dialog =watch.watch(dialogProvider);

    Widget trailingWidgets(ShiftCategoryStatus status, Shift shift) {
      var w;

      Future releaseDialog(){
        return showDialog<bool?>(
            context: context,
            builder: (ctx) {
              return AlertDialog(
                contentPadding : const EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 0.0),
                content: Container(
                  height: 200,
                  child: Column(
                    children: [
                      Container(
                          height: 40,
                          width: double.infinity,
                          color: welcomeTextColor,
                          child: Padding(
                            padding: const EdgeInsets.only(top:10.0),
                            child: Text('RELEASE SHIFT',textAlign: TextAlign.center,style: TextStyle(fontSize: 14,color: Colors.white,fontWeight: FontWeight.w500),),
                          )),
                      Padding(
                          padding: const EdgeInsets.only(top:35.0,bottom: 30, left: 20, right: 20),
                          child:   Text('This action will release the shift for payment. Do you wish to proceed?')),

                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [


                          ElevatedButton.icon(
                            label: Text('Yes'),
                            icon: Icon(Icons.done),

                            style: ElevatedButton.styleFrom(
                              backgroundColor: welcomeTextColor,
                            ),
                            onPressed: () async {
                              // TODO process
                              Navigator.pop(context, true);
                              final result = await ProgressDialog.future(
                                context,
                                dismissable: false,
                                future: shiftRepo.releaseShift(
                                    shiftId: shift.id!
                                ),
                                message:
                                Text("Releasing shift..").textColor(textColor),
                                title: Text("Release Shift").textColor(textColor),
                                //backgroundColor: Colors.white70,
                                onProgressError: (err) {
                                  print(err);

                                  Navigator.pop(context);
                                },
                                onProgressFinish: (er) {
                                  dialog.showFloatingFlushbar(
                                    context: context,
                                    title: 'Released',
                                    message:
                                    'Shift has been released successfully.',
                                  );},
                                onProgressCancel: () => Navigator.pop(context),
                              );

                            },
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left:15.0),
                            child: ElevatedButton.icon(
                              icon: Icon(Icons.cancel_outlined),
                              label: Text('No'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: denyRed,
                              ),
                              onPressed: () {
                                // TODO process
                                Navigator.pop(context, false);
                              },
                            ),
                          ),
                        ],)
                    ],
                  ),
                ),

              );
            });
      }


      Future authorisationReminderDialog(){
        return showDialog<bool?>(
            context: context,
            builder: (ctx) {
              return AlertDialog(
                contentPadding : const EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 0.0),
                content:SingleChildScrollView(
                  child:  Container(
                    child: Column(
                      children: [
                        Container(
                            height: 40,
                            width: double.infinity,
                            color: welcomeTextColor,
                            child: Padding(
                              padding: const EdgeInsets.only(top:10.0),
                              child: Text('REMIND AGENCY',textAlign: TextAlign.center,style: TextStyle(fontSize: 14,color: Colors.white,fontWeight: FontWeight.w500),),
                            )),
                        Padding(
                            padding: const EdgeInsets.only(top:35.0,bottom: 30, left: 20, right: 20),
                            child:   Text('This action will send a reminder notification to ${shift.client} '
                                '& ${shift.agency} to authorize this timesheet. Do you wish to proceed?')),

                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [


                            ElevatedButton.icon(
                              label: Text('Yes'),
                              icon: Icon(Icons.done),

                              style: ElevatedButton.styleFrom(
                                backgroundColor: welcomeTextColor,
                              ),
                              onPressed: () async {

                                Navigator.pop(context, true);

                                ProgressDialog.future(
                                  context,
                                  dismissable: false,
                                  future: shiftRepo.remindAuthorisation(
                                      shiftId: shift.id!
                                  ),
                                  message: Text("Sending reminder..").textColor(textColor),
                                  title: Text("Remind Authorisation").textColor(textColor),
                                  onProgressError: (err) {
                                    print(err);
                                    dialog.showFloatingFlushbar(
                                        warning: true,
                                        context: context,
                                        title: 'Failed',
                                        message: err.message
                                    );
                                  },
                                  onProgressFinish: (er) {
                                    dialog.showFloatingFlushbar(
                                      context: context,
                                      title: 'Sent',
                                      message:
                                      'Reminder sent successfully',
                                    );
                                  },
                                  onProgressCancel:() => Navigator.pop(context),
                                );



                              },
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left:15.0),
                              child: ElevatedButton.icon(
                                icon: Icon(Icons.cancel_outlined),
                                label: Text('No'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: denyRed,
                                ),
                                onPressed: () {
                                  // TODO process
                                  Navigator.pop(context, false);
                                },
                              ),
                            ),
                          ],)
                      ],
                    ),
                  ),
                ),

              );
            });
      }


      Future<List<ShiftExpenseClaim>> getExpenseClaims() async {
        claims = (await getShiftClaims([shift.id!]))!;
        return claims;
      }

      Future claimDialog(){
        return showDialog<bool?>(
            context: context,
            builder: (ctx) {

              return AlertDialog(
                contentPadding : const EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 0.0),
                content: Container(
                  height: 200,
                  child: Column(
                    children: [
                      Container(
                          height: 40,
                          width: double.infinity,
                          color: welcomeTextColor,
                          child: Padding(
                            padding: const EdgeInsets.only(top:10.0),
                            child: Text('CLAIM EXPENSES',textAlign: TextAlign.center,style: TextStyle(fontSize: 14,color: Colors.white,fontWeight: FontWeight.w500),),
                          )),
                      Padding(
                          padding: const EdgeInsets.only(top:35.0,bottom: 30, left: 20, right: 20),
                          child:   Text('Do you wish to claim expenses?')),

                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [


                          ElevatedButton.icon(
                            label: Text('Yes'),
                            icon: Icon(Icons.done),

                            style: ElevatedButton.styleFrom(
                              backgroundColor: welcomeTextColor,
                            ),
                            onPressed: () async {
                              Navigator.pop(context);
                              routeTo(context, ClaimExpenses(shift: shift, status: status, claims: claims!));


                            },
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left:15.0),
                            child: ElevatedButton.icon(
                              icon: Icon(Icons.cancel_outlined),
                              label: Text('No'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: denyRed,
                              ),
                              onPressed: () async {
                                Navigator.pop(context);

                                await releaseDialog();
                              },
                            ),
                          ),
                        ],)
                    ],
                  ),
                ),

              );
            });
      }


      _carPoolingDialog( ){
          return showDialog<bool?>(
              context: context,
              builder: (ctx) {
                return AlertDialog(
                  content: SingleChildScrollView(
                    child: Column(
                      children: [
                        Icon(Icons.mode_standby, color: welcomeTextColorDark,),
                        SizedBox(height: defaultPadding),
                        Text('This shift allows carpooling' ),
                        SizedBox(height: defaultPadding),
                        ElevatedButton.icon(
                          label: Text('See nearby users'),
                          icon: Icon(Icons.travel_explore),
                          onPressed: () async {
                            // Navigator.of(context).pop();
                            Navigator.push(context, MaterialPageRoute(builder: (context) =>  CarPoolingPage(shiftId:shift.id!)));
                          },
                        ),
                      ],
                    ),
                  ),

                );
              });
        }



      switch (status.status) {
        case 'NEW':
          w = Expanded(
            flex: 5,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                    child: GestureDetector(
                      child: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                      onTap: () {
                        if(shift.bookingType?.toLowerCase() == "transport"){
                          routeTo(context, ViewTransport(shift: shift, status: status));
                        }else{
                          routeTo(context, ViewShift(shift: shift, status: status));
                        }
                      },                         // routeTo(context, ViewTransport(shift: shift, status: status)),
                    ), flex: 4),


                Expanded(
                    child: (shift.requireApplicationByWorkers == null && shift.bookingType.toString() != "TRANSPORT")
                        ? IconButton(
                      icon: Tooltip(
                        message: "Book",
                        child: Icon(Icons.add_box_outlined,
                          color: welcomeTextColor),
                      ),
                      onPressed: () async {
                        // book a shift
                        final result = await ProgressDialog.future(
                          context,
                          dismissable: false,
                          future: shiftRepo.bookAShift(
                            shiftId: shift.id!,
                            workerId: _profile!.workerId ?? 1,
                            agencyId: _profile!.agentId ?? 1,
                          ),
                          message:
                          Text("booking a shift..").textColor(textColor),
                          title: Text("Book Shift").textColor(textColor),
                          //backgroundColor: Colors.white70,
                          onProgressError: (err) {
                            print(err);

                            Navigator.pop(context);
                          },
                          onProgressCancel: () => Navigator.pop(context),
                        );

                        // check result
                        if (result is bool) {
                          // added ok
                          dialog.showFloatingFlushbar(
                            context: context,
                            title: 'Book Shift',
                            message: 'Shift has been booked successfully.',
                          );
                        }

                        // err
                        else {
                          // added ok
                          dialog.showFloatingFlushbar(
                            context: context,
                            title: 'Book Shift',
                            message: result.message,
                            warning: true,
                          );
                        }
                      },
                    )
                        : !(shift.requireApplicationByWorkers??true)
                        ? IconButton(
                      icon: Tooltip(
                        message: "Book",
                        child: Icon(Icons.add_box_outlined,
                          color: welcomeTextColor),
                      ),
                      onPressed: () async {
                        // book a shift
                        selectAgencyInputBooking(context, formKey, 'Book', shift);

                      },
                    )
                        :IconButton(
                      icon: Icon(Icons.add_box_outlined,
                          color: inputBgColor),
                      onPressed: () async {

                      },
                    ), flex: 4),


                Expanded(
                    child: shift.requireApplicationByWorkers == null && shift.bookingType != 'TRANSPORT' ?
                    Text('\t\t\t\t-\t\t\t\t\t') :
                    shift.requireApplicationByWorkers ?? false ?
                    IconButton(
                      icon: Tooltip(
                        message: "Apply",
                        child: Icon(Icons.business_center_rounded, color: welcomeTextColor),
                      ),
                      onPressed: (){
                        selectAgencyInput(context, formKey, 'Apply', shift);
                      },
                    ):
                    shift.bookingType=="TRANSPORT" ?
                    IconButton(
                      icon: Tooltip(
                        message: "Apply",
                        child: Icon(Icons.business_center_rounded, color: welcomeTextColor),
                      ),
                      onPressed: () async {

                        final result = await ProgressDialog.future(
                          context,
                          dismissable: false,
                          future: transportRepo.applyATransport(shift.workerSpecId!, _profile!.workerId!,),
                          message: Text("applying for a transport job...").textColor(textColor),
                          title: Text("Applying Transport").textColor(textColor),
                          onProgressFinish: (e){
                            dialog.showFloatingFlushbar(
                              context: context,
                              title: 'Transport Apply',
                              message: 'Transport has been applied successfully.',
                            );
                          },
                          onProgressError: (err) {
                            dialog.showFloatingFlushbar(
                              context: context,
                              title: 'Error Applying',
                              message: err.message,
                              warning: true,
                            );
                          },
                        );




                      },
                    ):
                    IconButton(
                      icon: Tooltip(
                        message: "Apply",
                        child: Icon(Icons.business_center_rounded,
                          color: inputBgColor),
                      ),
                      onPressed: ()   {
                      },
                    ), flex: 4),






              ],
            ),
          );
          break;

        case 'CANCELLED':
          w = Expanded(
            flex: 5,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                    child: IconButton(
                      icon: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                      onPressed: () =>
                          routeTo(context, ViewShift(shift: shift, status: status)),
                    ), flex: 6),
                Expanded(
                    child: Text(shift.shiftStatus ?? '---').fontSize(11), flex: 6),
              ],
            ),
          );
          break;

        case 'AWAITING_AUTHORIZATION':
          w = Expanded(
            flex: 5,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                //  Spacer(),
                Expanded(
                    child:  GestureDetector(
                      child: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                      onTap: () {
                        if(shift.bookingType=="TRANSPORT"){
                          routeTo(context, ViewTransport(shift: shift, status: status));
                        }else{
                          routeTo(context, ViewShift(shift: shift, status: status));
                        }
                      },
                    ),
                    flex: 6),
                Expanded(
                    child: GestureDetector(
                        child: Column(
                          children: [
                            Tooltip(
                              message: "Send reminder",
                              child: Icon(Icons.notifications, color:
                              (shift.lastAuthorisationReminder!=null && shift.lastAuthorisationReminder!<60)?
                              Colors.black26:
                              secondaryAccent
                              ),
                            ),
                            if(shift.lastAuthorisationReminder!=null && shift.lastAuthorisationReminder!<60)
                              Text((24-(shift.lastAuthorisationReminder??0)).toString()+"hrs", style: TextStyle(fontSize: 10),)
                          ],
                        ),
                        onTap: () {
                          if (!(shift.lastAuthorisationReminder != null &&
                              shift.lastAuthorisationReminder! <
                                  24)) {
                            authorisationReminderDialog();
                          }else{
                            dialog.showFloatingFlushbar(
                              warning: true,
                              context: context,
                              title: 'Please wait',
                              message:
                              'Please wait an hour before sending another request',
                            );
                          };
                        }
                    ),
                    flex: 6),


              ],
            ),
          );
          break;

        case 'BOOKED':
          w = Expanded(
            flex: 5,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                    child:IconButton(
                      icon: Tooltip(
                        message: "Carpooling",
                        child: Icon(Icons.mode_standby, color: welcomeTextColor),
                      ),
                      onPressed: () {
                        _carPoolingDialog();
                      },
                    ) , flex: 6),                Expanded(
                    child:IconButton(
                      icon: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                      onPressed: () {
                        if(shift.bookingType=="TRANSPORT"){
                          routeTo(context, ViewTransport(shift: shift, status: status));
                        }else{
                          routeTo(context, ViewShift(shift: shift, status: status));
                        }
                      },
                    ) , flex: 6),
                // Expanded(
                //   child:  IconButton(
                //     icon: Tooltip(
                //         message: "Cancel",
                //         child: Icon(Icons.cancel, color: secondaryAccent),
                //       ),
                //     onPressed: () =>
                //         shiftReasonInput(context, formKey, 'Cancel', shift),
                //   ), flex: 6,),
              ],
            ),
          );
          break;

        case 'APPLIED':
          w = Expanded(
            flex: 5,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [

                Expanded(
                    child: IconButton(
                      icon: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                      onPressed: () {
                        if(shift.bookingType?.toLowerCase() == "transport"){
                          routeTo(context, ViewTransport(shift: shift, status: status));
                        }else{
                          routeTo(context, ViewShift(shift: shift, status: status));
                        }
                      }
                      ,
                    ), flex: 6),
                Expanded(
                    child: IconButton(
                      icon: Tooltip(
                        message: "Cancel",
                        child: Icon(Icons.cancel_outlined, color: secondaryAccent),
                      ),
                      onPressed: () =>
                          shiftReasonInput(context, formKey, 'Cancel', shift),
                    ), flex: 6),

              ],
            ),
          );
          break;

        case 'AUTHORIZED':
          w = Expanded(
            flex: 5,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                    child:  IconButton(
                      icon: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                      onPressed: () {
                        if(shift.bookingType?.toLowerCase() == "transport"){
                          routeTo(context, ViewTransport(shift: shift, status: status));
                        }else{
                          routeTo(context, ViewShift(shift: shift, status: status));
                        }
                      },
                    ), flex: 4),
                Expanded(
                    child: (shift.released != true && shift.isAgencyBilled != true && shift.bookingType?.toLowerCase()!='transport') ?
                    IconButton(
                      icon: Tooltip(
                        message: "Query",
                        child: Icon(Icons.help, color: secondaryAccent),
                      ),
                      onPressed: () => shiftReasonInput(
                          context, formKey, 'Query', shift,
                          isQuery: true),
                    ):
                    IconButton(
                      icon: Icon(Icons.help, color: Colors.grey),
                      onPressed: () async {

                      },
                    ), flex: 4),
                Expanded(
                    child: shift.released != true
                        ? IconButton(
                      icon: Icon(Icons.check_box, color: welcomeTextColor),
                      onPressed: () async {

                        List? agencyExpenseRates = shift.agencyId!=null?
                        await ProgressDialog.future(
                          context,
                          dismissable: false,
                          future: getAgencyExpenseRates(shift.agencyId!).then((value) => value ?? []),
                          title: Text("Checking Shift"),
                          message: Text("Processing"),
                          onProgressError: (err) {
                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text("An error occurred"),));
                          },
                        ): [];

                        if(agencyExpenseRates!=null&& agencyExpenseRates.length!=0){
                          await claimDialog();
                        }else{
                          await releaseDialog();
                        }

                      },
                    )
                        :
                    IconButton(
                      icon: Tooltip(
                        message: "Release",
                        child: Icon(Icons.check_box, color: Colors.grey),
                      ),
                      onPressed: () async {

                      },
                    ), flex: 4),








              ],
            ),
          );
          break;

        case 'IN_QUERY':
          w = Expanded(
            flex: 5,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                    child:  IconButton(
                      icon: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                      onPressed: () =>
                          routeTo(context, ViewShift(shift: shift, status: status)),
                    ), flex: 12),

              ],
            ),
          );
          break;

        default:
          w = SizedBox.shrink();
      }



      return w;
    }

    return watch.watch(_shiftProvider(status.status)).when(
      data: (ShiftsPageResp? value) {
        if (value != null) {
          final shifts = value.content;

          if (shifts == null) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  'failed to get ${status.status} shifts',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    color: Colors.grey,
                  ),
                ),
              ),
            );
          }

          return shifts.isEmpty
              ? Center(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                'no shifts available matching ${status.status} status shifts',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
              ),
            ),
          )
              : Column(
            children: [

              Expanded(
                child: SingleChildScrollView(
                  child: RefreshIndicator(
                      color: welcomeTextColor,
                      onRefresh: () async {
                        await watch.refresh(_shiftProvider(status.status));
                      },
                      child: Column(
                          children: [
                            SizedBox(height: 10,),
                            Row(
                              children: [
                                Expanded(
                                  child: SizedBox(),
                                  flex: 7,),
                                Expanded(
                                  child: trailingHeading(status),
                                  flex: 5,),
                              ],
                            ),

                            SizedBox(
                              height: MediaQuery.of(context).size.height-206,
                              child: ListView.separated(
                                // padding: EdgeInsets.all(8),
                                itemBuilder: (context, index) {
                                  final Shift shift = shifts[index]!;


                                  return Row(
                                    children: [
                                      Expanded(
                                        flex: 7,
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(10),
                                          child: ListTile(
                                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
                                            title: Column(
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                    padding: EdgeInsets.all(5),
                                                    margin: EdgeInsets.only(bottom:1),
                                                    decoration: BoxDecoration(
                                                      color: shift.bookingType=="TRAINING"? orangeAccent :shift.bookingType=="TRANSPORT"? secondaryAccent:shiftTeal,
                                                      borderRadius: BorderRadius.all( Radius.circular(defaultBorderRadius*2),
                                                      ),
                                                    ),
                                                    child:Text(shift.bookingType?.toCapitalized()??"Shift", style: TextStyle(fontSize: 12, color: Colors.white),)
                                                ),
                                                Text((shift.start!=null ? dateFormat.format(shift.start!): '')+" | "+ ((shift.start!=null?timeFormat.format(shift.start!):''))+(shift.end!=null?"-":"")+((shift.end!=null?timeFormat.format(shift.end!):'')))
                                              ],
                                            ),
                                            subtitle: Text(shift.shiftLocation ?? 'Location') ,
                                            // trailing: Text('Start\n${shift.shiftStartTime!}', style: TextStyle(fontSize: 13),).fontWeight(FontWeight.w600),
                                          ),
                                        ),
                                      ),
                                      trailingWidgets(status, shift),
                                    ],
                                  );
                                },
                                separatorBuilder: (_, x) => SizedBox(height: 0),
                                itemCount: shifts.length,
                              ),
                            )
                          ]
                      )
                  ),
                )
              ),
              Center(
                child: Padding(
                  padding: const EdgeInsets.only(top: 10, bottom: 20),
                  child: Text(
                    'pull down to refresh',
                    style: TextStyle(
                      fontStyle: FontStyle.italic,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ),
            ],
          );
        }

        // err
        else {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                'failed to get ${status.status} shifts',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
              ),
            ),
          );
        }
      },
      loading: () => Center(
        child: CircularProgressIndicator(),
      ),
      error: (e, st) => Center(
        child: ErrorPage(
          error: e is CustomException ? e.message : e.toString(),
          stackTrace: st,onTryAgain: () => watch.refresh(_shiftProvider(status.status)),
        ),
      ),
    );
  }
}
