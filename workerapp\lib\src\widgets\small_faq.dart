import 'package:flutter/material.dart';
import 'package:work_link/src/utils/color_constants.dart';

import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

import 'FAQ.dart';

class SmallFaqWidget extends StatefulWidget {
  SmallFaqWidget({Key? key, this.limitFaqs}):super(key: key);
  bool? limitFaqs;
  @override
  SmallFaqWidgetState createState() => SmallFaqWidgetState();
}

class SmallFaqWidgetState extends State<SmallFaqWidget> {

  int activeQs =0;


  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  Widget build(BuildContext context) {
    refresh(){
      activeQs = 0;
    }

    Widget faqItem(text,int indexItem){
      return InkWell(
        onTap: (){
          refresh();
          activeQs=indexItem;
          setState(() {});
        },
        child: Padding(
          padding: const EdgeInsets.only(left:10.0,top: 10,right: 10),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                color: welcomeTextColor,
                height: 18,width: 18,
                child: Center(
                    child: Text("Q",style: TextStyle(color: Colors.white,fontSize: 14),)
                ),
              ),
              Expanded(
                  child: Padding(
                      padding: const EdgeInsets.only(left: 10.0),
                      child: Text(text,style: px18TextStyleNormal,))
              ),
            ],
          ),
        ),
      );
    }
    return    Container(
      padding: EdgeInsets.symmetric(vertical: 5),
        decoration: BoxDecoration(
            color: Color(0xFFBDBDBD).withOpacity(.2),
            border: Border.all(
              color: Color(0xFFBDBDBD).withOpacity(.2),
            ),
            borderRadius: BorderRadius.all(Radius.circular(5))
        ),
        child: Column(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment:MainAxisAlignment.start ,
              children: [


                faqItem(Faq1, 1),
                if(activeQs == 1)  Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    children: [


                      Padding(
                        padding: const EdgeInsets.only(top:0.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              color: Colors.green,
                              height: 18,width: 18,child: Center(child:
                            Text("A",style: TextStyle(color: Colors.white,fontSize: 14),)),),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text(FaqA1,style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top:8.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top:8.0,left: 8),
                              child: Container(
                                height: 6,width: 6,
                                decoration: BoxDecoration(
                                    color:   Colors.pink,
                                    border: Border.all(
                                      color: Colors.pink,
                                    ),
                                    borderRadius: BorderRadius.all(Radius.circular(20))
                                ),),
                            ),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text("View full shift details",style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top:5.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top:8.0,left: 8),
                              child: Container(
                                height: 6,width: 6,
                                decoration: BoxDecoration(
                                    color:   Colors.pink,
                                    border: Border.all(
                                      color: Colors.pink,
                                    ),
                                    borderRadius: BorderRadius.all(Radius.circular(20))
                                ),),
                            ),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text("Search for NEW shifts",style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top:5.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top:8.0,left: 8),
                              child: Container(
                                height: 6,width: 6,
                                decoration: BoxDecoration(
                                    color:   Colors.pink,
                                    border: Border.all(
                                      color: Colors.pink,
                                    ),
                                    borderRadius: BorderRadius.all(Radius.circular(20))
                                ),),
                            ),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text("Book or Apply into a shift",style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top:5.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top:8.0,left: 8),
                              child: Container(
                                height: 6,width: 6,
                                decoration: BoxDecoration(
                                    color:   Colors.pink,
                                    border: Border.all(
                                      color: Colors.pink,
                                    ),
                                    borderRadius: BorderRadius.all(Radius.circular(20))
                                ),),
                            ),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text("View shifts Waiting to be authorised (electronic signature on timesheet",style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top:5.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top:8.0,left: 8),
                              child: Container(
                                height: 6,width: 6,
                                decoration: BoxDecoration(
                                    color:   Colors.pink,
                                    border: Border.all(
                                      color: Colors.pink,
                                    ),
                                    borderRadius: BorderRadius.all(Radius.circular(20))
                                ),),
                            ),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text("Release timesheets for them to be processed for payment by your agency",style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top:5.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top:8.0,left: 8),
                              child: Container(
                                height: 6,width: 6,
                                decoration: BoxDecoration(
                                    color:   Colors.pink,
                                    border: Border.all(
                                      color: Colors.pink,
                                    ),
                                    borderRadius: BorderRadius.all(Radius.circular(20))
                                ),),
                            ),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text("Query timesheets",style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top:5.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top:8.0,left: 8),
                              child: Container(
                                height: 6,width: 6,
                                decoration: BoxDecoration(
                                    color:   Colors.pink,
                                    border: Border.all(
                                      color: Colors.pink,
                                    ),
                                    borderRadius: BorderRadius.all(Radius.circular(20))
                                ),),
                            ),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text("View pay advices (gross pay) and payslips (download option available)",style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top:5.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top:8.0,left: 8),
                              child: Container(
                                height: 6,width: 6,
                                decoration: BoxDecoration(
                                    color:   Colors.pink,
                                    border: Border.all(
                                      color: Colors.pink,
                                    ),
                                    borderRadius: BorderRadius.all(Radius.circular(20))
                                ),),
                            ),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text("Update personal contact information and profile",style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top:5.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top:8.0,left: 8),
                              child: Container(
                                height: 6,width: 6,
                                decoration: BoxDecoration(
                                    color:   Colors.pink,
                                    border: Border.all(
                                      color: Colors.pink,
                                    ),
                                    borderRadius: BorderRadius.all(Radius.circular(20))
                                ),),
                            ),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text("View your profile and registration information with your agencies including total worked shifts and hours",style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                faqItem(Faq2, 2),
                if(activeQs == 2)  Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    children: [


                      Padding(
                        padding: const EdgeInsets.only(top:0.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              color: Colors.green,
                              height: 18,width: 18,
                              child: Center(
                                  child: Text("A",style: TextStyle(color: Colors.white,fontSize: 14),)),
                            ),
                            Expanded(
                                child: Padding(
                                    padding: const EdgeInsets.only(left: 10.0),
                                    child: Text(FaqA2,style: px14TextStyleNormal,)
                                )
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top:0.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              color: Colors.green,
                              height: 18,width: 18,
                              child: Center(
                                  child: Text("A",style: TextStyle(color: Colors.white,fontSize: 14),)),
                            ),
                            Expanded(
                                child: Padding(
                                    padding: const EdgeInsets.only(left: 10.0),
                                    child: Text(FaqA2b,style: px14TextStyleNormal,)
                                )
                            ),
                          ],
                        ),
                      ),

                    ],
                  ),
                ),


                faqItem(Faq3, 3),
                if(activeQs == 3)  Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    children: [


                      Padding(
                        padding: const EdgeInsets.only(top:0.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              color: Colors.green,
                              height: 18,width: 18,child: Center(child:
                            Text("A",style: TextStyle(color: Colors.white,fontSize: 14),)),),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text(FaqA3,style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),

                    ],
                  ),
                ),

                faqItem(Faq8, 8),
                if(activeQs == 8)  Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    children: [


                      Padding(
                        padding: const EdgeInsets.only(top:0.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              color: Colors.green,
                              height: 18,width: 18,child: Center(child:
                            Text("A",style: TextStyle(color: Colors.white,fontSize: 16),)),),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text(FaqA8b,style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top:0.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              color: Colors.green,
                              height: 18,width: 18,child: Center(child:
                            Text("A",style: TextStyle(color: Colors.white,fontSize: 16),)),),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text(FaqA8c,style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),

                    ],
                  ),
                ),




                if(widget.limitFaqs==true)GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) =>  Faq()),
                    );
                  },
                  child: Padding(
                    padding: EdgeInsets.all(defaultPadding),
                    child: Text("See more FAQs >", style: TextStyle(color: denyRed), ),
                  ),
                ),








              ],
            ),
            if(widget.limitFaqs!=true)Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment:MainAxisAlignment.start ,
              children: [



                faqItem(Faq4, 4),
                if(activeQs == 4)  Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    children: [


                      Padding(
                        padding: const EdgeInsets.only(top:0.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              color: Colors.green,
                              height: 18,width: 18,child: Center(child:
                            Text("A",style: TextStyle(color: Colors.white,fontSize: 16),)),),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Text(FaqA4,style: px14TextStyleNormal,))),
                          ],
                        ),
                      ),

                    ],
                  ),
                ),


                faqItem(Faq5, 5),
                if(activeQs == 5)  Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    children: [


                      Padding(
                        padding: const EdgeInsets.only(top:0.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              color: Colors.green,
                              height: 18,width: 18,child: Center(child:
                            Text("A",style: TextStyle(color: Colors.white,fontSize: 16),)),),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Column(
                                  children: [
                                    Text(FaqA5,style: px14TextStyleNormal,),
                                    Padding(
                                      padding: const EdgeInsets.only(top:8.0),
                                      child: Text(FaqA61,style: px14TextStyleNormalRed,),
                                    )
                                  ],
                                ))),


                          ],
                        ),
                      ),

                    ],
                  ),
                ),
                faqItem(Faq6, 6),
                if(activeQs == 6)  Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    children: [

                      Padding(
                        padding: const EdgeInsets.only(top:0.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              color: Colors.green,
                              height: 18,width: 18,child: Center(child:
                            Text("A",style: TextStyle(color: Colors.white,fontSize: 16),)),),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Column(
                                  children: [
                                    Text(FaqA6,style: px14TextStyleNormal,),
                                    Padding(
                                      padding: const EdgeInsets.only(top:8.0),
                                      child: Text(FaqA71,style: px14TextStyleNormalRed,),
                                    )
                                  ],
                                ))),
                          ],
                        ),
                      ),

                    ],
                  ),
                ),
                faqItem(Faq7, 7),
                if(activeQs == 7) Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    children: [


                      Padding(
                        padding: const EdgeInsets.only(top:0.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              color: Colors.green,
                              height: 18,width: 18,child: Center(child:
                            Text("A",style: TextStyle(color: Colors.white,fontSize: 16),)),),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Column(
                                  children: [
                                    Text(FaqA8,style: px14TextStyleNormal,),
                                    /* Padding(
                                                    padding: const EdgeInsets.only(top:8.0),
                                                    child: Text(FaqA8,style: px14TextStyleNormal,),
                                                  )*/
                                  ],
                                ))),
                          ],
                        ),
                      ),



                    ],
                  ),
                ),
                faqItem(Faq9, 9),
                if(activeQs == 9) Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    children: [


                      Padding(
                        padding: const EdgeInsets.only(top:0.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              color: Colors.green,
                              height: 18,width: 18,child: Center(child:
                            Text("A",style: TextStyle(color: Colors.white,fontSize: 14),)),),
                            Expanded(child: Padding(
                                padding: const EdgeInsets.only(left: 10.0),
                                child: Column(
                                  children: [
                                    Text(FaqA9,style: px14TextStyleNormal,),

                                  ],
                                ))),
                          ],
                        ),
                      ),



                    ],
                  ),
                ),


















              ],
            )
          ],
        )
    );
  }


}
