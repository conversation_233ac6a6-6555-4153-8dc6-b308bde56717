
import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import '../../../widgets/ApiCallingWithoutProgressIndicator.dart';
import '../../../widgets/CustomProgressDialog.dart';
import '../../../utils/UserPreference.dart';
import '../../../utils/constants.dart';
import '../../../widgets/app_appbar.dart';
import '../model/ClientModel.dart';
import 'client_directors.dart';


class ClientViewNew extends StatefulWidget {

  @override
  ClientViewNewState createState() => ClientViewNewState();
}

class ClientViewNewState extends State<ClientViewNew> {
  TextEditingController searchController = new TextEditingController();
  List<Content>? agencyClientList = [];
  bool isSearching =false;
  Timer? _timer;

  final _formKey = GlobalKey<FormState>();
  ClientModel? wp;



  getProfileData(workerId) async {
    try {
      CustomProgressLoader.showLoader(context);
      print("api call wp+++++"+workerId);
      Response? response = await ApiCalling()
          .apiCall(context, "$dataService/api/v1/worker-clients/"+workerId+"/0/300", "get");
      CustomProgressLoader.cancelLoader(context);
      print("response++" + response.toString());
      if (response != null) {
        if (response.statusCode == 200) {
          wp = ClientModel.fromJson(response.data);
          setState(() {

          });
        }
      }
    } catch (e) {

      print("issue shubh" + e.toString());
      return null;
    }
  }


  SharedPreferences? prefs;

  init() async {
     prefs = await SharedPreferences.getInstance();
  String? workerId=  await prefs!.getString(UserPreference.WORKER_ID);
     getProfileData(workerId);
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    init();
  }




  Widget build(BuildContext context) {
    MediaQueryData mediaQuery = MediaQuery.of(context);
    return Scaffold(
        appBar:    AppBarDefault(context , "Clients",leading:
    Container(
      height: 30,
      width: 30,
      child: Padding(
        padding: const EdgeInsets.only(left: 15.0,right: 15),
        child: InkWell(
          child:  Icon(Icons.arrow_back_ios,color: welcomeTextColor,),
          onTap: () {
            routeBack(context);
          },
        ),
      ),
    ) ),
        body: wp!=null?Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            // Padding(
            //     padding: EdgeInsets.only(
            //         left: 10.0, right: 10, top: 10, bottom: 5),
            //     child: Text(
            //       "Clients",
            //       style: GoogleFonts.viga(
            //           fontStyle: FontStyle.normal,
            //           fontSize: 18.0,
            //           color: Colors.black87),
            //     )),
            // Expanded(
            //   child: Padding(
            //     padding: EdgeInsets.only(
            //         left: 10.0, right: 10, top: 5, bottom: 10),
            //     child: Container(
            //       width: 200,
            //       height: 45,
            //       child: TextField(
            //         onChanged: (value) {
            //           print("value+++" + value);
            //           if (value.toString().trim().length == 0) {
            //             setState(() {
            //               isSearching = false;
            //             });
            //           } else {
            //             setState(() {
            //               isSearching = true;
            //             });
            //             if (_timer != null) {
            //               _timer!.cancel();
            //             }
            //             print("value+++" + value);
            //             setState(() {
            //               agencyClientList = [];
            //             });
            //
            //             _timer = Timer(Duration(milliseconds: 1000), () {
            //               for (Content cli in wp!.content!) {
            //                 print("value+++" + cli.name!.toString());
            //                 if (cli.name!
            //                     .toLowerCase()
            //                     .contains(value.toLowerCase())) {
            //                   print("value2+++" + cli.name!.toString());
            //                   agencyClientList!.add(cli);
            //                 }
            //               }
            //               setState(() {});
            //             });
            //           }
            //         },
            //         controller: searchController,
            //         decoration: InputDecoration(
            //
            //             labelText: "Search Client",
            //             hintText: "",
            //             labelStyle: TextStyle(color: lightgreyColor),
            //             border: OutlineInputBorder(
            //                 borderSide: BorderSide(color: lightgreyColor),
            //                 borderRadius:
            //                 BorderRadius.all(Radius.circular(5.0))),
            //             focusedBorder: OutlineInputBorder(
            //                 borderSide: BorderSide(color: lightgreyColor),
            //                 borderRadius:
            //                 BorderRadius.all(Radius.circular(5.0))),
            //             enabledBorder: OutlineInputBorder(
            //                 borderSide: BorderSide(color: lightgreyColor),
            //                 borderRadius:
            //                 BorderRadius.all(Radius.circular(5.0)))),
            //
            //       ),
            //     ),
            //   ),
            //   flex: 0,
            // ),
            Expanded(
              child: ListView(
                children: [
                isSearching?
                Column(
                    children: List.generate(
                      agencyClientList!.length,
                          (int index) {
                        Content cli = agencyClientList![index];
                        return Padding(
                          padding: const EdgeInsets.only(
                              left: 10.0, right: 10, top: 10),
                          child: Card(
                            color: tileColor,
                            margin: const EdgeInsets.only(top: 0.0),
                            elevation: 1.0,
                            child: Padding(
                              padding:
                              const EdgeInsets.only(top: 0.0),
                              child: ListTile(
                                title: Row(
                                  mainAxisAlignment:
                                  MainAxisAlignment.start,
                                  crossAxisAlignment:
                                  CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Padding(
                                padding: const EdgeInsets.only(top:2.0),
                                child: Image.asset(
                                        "assets/images/client_default.png",
                                        height: 35.0,
                                        width: 35.0,
                                      )),
                                      flex: 0,
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(left: 20.0),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: [


                                          Text(
                                            cli.name.toString(),
                                            style: GoogleFonts.viga(
                                                fontStyle: FontStyle
                                                    .normal,
                                                fontSize: 15.0,
                                                color:
                                                Colors.black87),
                                          ),
                                          Text(
                                            'Service',
                                            style: GoogleFonts.lato(
                                                fontStyle:
                                                FontStyle.normal,
                                                fontSize: 14.0,
                                                color:
                                                Colors.black),
                                          ),
                                        ],
                                      ),
                                    ),


                                  ],
                                ),
                                onTap: () {
                                  routeTo(
                                      context,
                                      ClientDirector(
                                          cli.id.toString()));
                                },

                              ),
                            ),
                          ),
                        );
                      },
                    ))
                    :  Column(
                      children: List.generate(
                        wp!.content!.length,
                            (int index) {
                              Content cli = wp!.content![index];
                              return Padding(
                                padding: const EdgeInsets.only(
                                    left: 10.0, right: 10, top: 10),
                                child: Card(
                                  color: tileColor,
                                  margin: const EdgeInsets.only(top: 0.0),
                                  elevation: 1.0,
                                  child: Padding(
                                    padding:
                                    const EdgeInsets.only(top: 0.0),
                                    child: ListTile(
                                      iconColor: welcomeTextColor,
                                      tileColor: accentPrimaryColor,
                                      title: Row(
                                        mainAxisAlignment:
                                        MainAxisAlignment.start,
                                        crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child: Padding(
                                              padding: const EdgeInsets.only(top:2.0),
                                              child: Image.asset(
                                                "assets/images/client_default.png",
                                                height: 35.0,
                                                width: 35.0,
                                              ),
                                            ),
                                            flex: 0,
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.only(left: 20.0),
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              children: [


                                                Text(
                                                  cli.name.toString(),
                                                  style: GoogleFonts.viga(
                                                      fontStyle: FontStyle
                                                          .normal,
                                                      fontSize: 15.0,
                                                      color:
                                                      Colors.black87),
                                                ),
                                                Text(
                                                  'Service',
                                                  style: GoogleFonts.lato(
                                                      fontStyle:
                                                      FontStyle.normal,
                                                      fontSize: 14.0,
                                                      color:
                                                      Colors.black),
                                                ),
                                              ],
                                            ),
                                          ),


                                        ],
                                      ),
                                      onTap: () {
                                        routeTo(
                                            context,
                                            ClientDirector(
                                                cli.id.toString()));
                                      },

                                    ),
                                  ),
                                ),
                              );
                        },
                      )),
                ],
              ),
              flex: 1,
            ),
          ],
        ): SizedBox(),
      )
    ;
  }


}
