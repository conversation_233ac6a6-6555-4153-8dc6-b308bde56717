PODS:
  - DKImagePickerController/Core (4.3.4):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.4)
  - DKImagePickerController/PhotoGallery (4.3.4):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.4)
  - DKPhotoGallery (0.0.17):
    - DKPhotoGallery/Core (= 0.0.17)
    - DKPhotoGallery/Model (= 0.0.17)
    - DKPhotoGallery/Preview (= 0.0.17)
    - DKPhotoGallery/Resource (= 0.0.17)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.17):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.17):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/CoreOnly (10.3.0):
    - FirebaseCore (= 10.3.0)
  - Firebase/Messaging (10.3.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.3.0)
  - firebase_core (2.4.1):
    - Firebase/CoreOnly (= 10.3.0)
    - Flutter
  - firebase_messaging (14.2.1):
    - Firebase/Messaging (= 10.3.0)
    - firebase_core
    - Flutter
  - FirebaseCore (10.3.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.5.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.5.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.3.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - Flutter (1.0.0)
  - flutter_downloader (0.0.1):
    - Flutter
  - GoogleDataTransport (9.2.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.11.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/Network (7.11.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.0)"
  - GoogleUtilities/Reachability (7.11.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.11.0):
    - GoogleUtilities/Logger
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.6.1)
  - image_picker_ios (0.0.1):
    - Flutter
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - "permission_handler (5.1.0+2)":
    - Flutter
  - PromisesObjC (2.2.0)
  - SDWebImage (5.14.2):
    - SDWebImage/Core (= 5.14.2)
  - SDWebImage/Core (5.14.2)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.3)
  - TOCropViewController (2.6.1)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_downloader (from `.symlinks/plugins/flutter_downloader/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/ios`)
  - permission_handler (from `.symlinks/plugins/permission_handler/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - SDWebImage
    - SwiftyGif
    - TOCropViewController

EXTERNAL SOURCES:
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_downloader:
    :path: ".symlinks/plugins/flutter_downloader/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/ios"
  permission_handler:
    :path: ".symlinks/plugins/permission_handler/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  DKImagePickerController: b512c28220a2b8ac7419f21c491fc8534b7601ac
  DKPhotoGallery: fdfad5125a9fdda9cc57df834d49df790dbb4179
  file_picker: 3e6c3790de664ccf9b882732d9db5eaf6b8d4eb1
  Firebase: f92fc551ead69c94168d36c2b26188263860acd9
  firebase_core: bf59c32d2e53814f558efa20840c1902fa2fe461
  firebase_messaging: ee597229fc260f8fa491fa8f2d4a32dfbfa406fa
  FirebaseCore: 988754646ab3bd4bdcb740f1bfe26b9f6c0d5f2a
  FirebaseCoreInternal: e463f41bb935cd049505bf7e9a5bdd7dcea90df6
  FirebaseInstallations: 935bc4abb6f7a035cab7a0c31cb777b2be3dd254
  FirebaseMessaging: e345b219fd15d325f0cf2fef28cb8ce00d851b3f
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_downloader: b7301ae057deadd4b1650dc7c05375f10ff12c39
  GoogleDataTransport: ea169759df570f4e37bdee1623ec32a7e64e67c4
  GoogleUtilities: c2bdc4cf2ce786c4d2e6b3bcfd599a25ca78f06f
  image_cropper: 60c2789d1f1a78c873235d4319ca0c34a69f2d98
  image_picker_ios: b786a5dcf033a8336a657191401bfdf12017dabb
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  path_provider_foundation: 37748e03f12783f9de2cb2c4eadfaa25fe6d4852
  permission_handler: ccb20a9fad0ee9b1314a52b70b76b473c5f8dab0
  PromisesObjC: 09985d6d70fbe7878040aa746d78236e6946d2ef
  SDWebImage: b9a731e1d6307f44ca703b3976d18c24ca561e84
  shared_preferences_foundation: 297b3ebca31b34ec92be11acd7fb0ba932c822ca
  SwiftyGif: 6c3eafd0ce693cad58bb63d2b2fb9bacb8552780
  TOCropViewController: edfd4f25713d56905ad1e0b9f5be3fbe0f59c863
  url_launcher_ios: ae1517e5e344f5544fb090b079e11f399dfbe4d2

PODFILE CHECKSUM: f6072c8d48b155f8c5aabd6a9b8568c37a1fabda

COCOAPODS: 1.11.0
