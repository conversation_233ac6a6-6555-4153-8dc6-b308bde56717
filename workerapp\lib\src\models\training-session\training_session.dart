
import 'package:freezed_annotation/freezed_annotation.dart';
import '../training/Training.dart';
import '../agency/Agency.dart';
import '../shiftlocation/ShiftLocation.dart';

part 'training_session.freezed.dart';
part 'training_session.g.dart';

@freezed
abstract class TrainingSession with _$TrainingSession {
  factory TrainingSession({

    int? id,
    String? name,
    int? trainingId,
    String? trainingName,
    int? shiftLocationId,
    List<int>? agencyIds,
    String? shiftLocationName,
    String? agencyName,
    String? trainingStatus,
    String? postCode,
    String? address,
    String? startDateTime,
    String? endDateTime,
    double? breakTime,
    double? trainingCost,
    ShiftLocation? shiftLocation,
    Agency? trainer,
    Training? training,
    int? vacancies,
    String? notes,
    bool? isAgencyPaying,
    bool? publishToAllWorkers,
    bool? publishToAllAgencies,


  }) = _TrainingSession;

  factory TrainingSession.fromJson(Map<String, dynamic> json) =>
      _$TrainingSessionFromJson(json);
}
