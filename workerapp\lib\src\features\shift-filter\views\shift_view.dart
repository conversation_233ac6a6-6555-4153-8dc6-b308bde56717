// import 'package:date_range_form_field/date_range_form_field.dart'; // Temporarily disabled
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:ndialog/ndialog.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/features/shift-filter/data/filter_model.dart';
import 'package:work_link/src/features/shift-filter/logic/agency_selector_popup.dart';
import 'package:work_link/src/features/shift-filter/logic/client_shift_selector_popup.dart';
import 'package:work_link/src/features/shift-filter/logic/location_selector_popup.dart';
import 'package:work_link/src/features/shift-filter/logic/shift_reason_input.dart';
import 'package:work_link/src/features/shift-filter/views/view_shift.dart';
import 'package:work_link/src/features/shifts/data/shift_category.dart';
import 'package:work_link/src/models/custom_exception.dart';
import 'package:work_link/src/models/filter-shift/filtered_shift.dart';

import 'package:work_link/src/utils/index.dart';

import 'trailing_header.dart';

final dateRangeProvider = AutoDisposeStateProvider<DateTimeRange?>((_) => null);

final selectedAgencyProvider = AutoDisposeStateProvider<Map?>((_) => null);
final selectedClientProvider = AutoDisposeStateProvider<Map?>((_) => null);
final selectedLocationProvider = AutoDisposeStateProvider<Map?>((_) => null);

final filteredShiftsProvider =
    AutoDisposeStateProvider<List<FilteredShift>?>((_) => null);

class ShiftView extends ConsumerWidget {
  ShiftView({Key? key, required this.status}) : super(key: key);

  final ShiftCategoryStatus status;

  final formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context,   watch) {
    final shiftRepo =watch.watch(shiftRepoProvider);
    final _profile =watch.watch(loginResponseProvider);
    final dialog =watch.watch(dialogProvider);

    final sAgency =watch.watch(selectedAgencyProvider);
    final sClient =watch.watch(selectedClientProvider);
    final sLocation =watch.watch(selectedLocationProvider);
    final sDate =watch.watch(dateRangeProvider);
    final shifts =watch.watch(filteredShiftsProvider);

    final spacer = SizedBox(width: 8);

    bool filtersSet() {
      if (sDate == null) return false;

      var ssDate = sDate.duration.inDays > 31 ? null : true;

      if (ssDate == null) {
        WidgetsBinding.instance?.addPostFrameCallback((_) {
          dialog.showFloatingFlushbar(
            context: context,
            title: 'Date',
            message: 'Date range exceeds max 31 days limit',
            warning: true,
          );
        });

        return false;
      }

      if (sAgency == null || sClient == null || sLocation == null) return false;

      return true;
    }

    void resetFilters() {
      watch.read(selectedAgencyProvider.notifier).state = null;
      watch.read(selectedClientProvider.notifier).state = null;
      watch.read(selectedLocationProvider.notifier).state = null;
      watch.read(dateRangeProvider.notifier).state = null;
    }

    Widget trailingWidgets(ShiftCategoryStatus status, FilteredShift shift) {
      var w;

      switch (status.status) {
        case 'NEW':
          w = Expanded(
            flex: 3,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                  onPressed: () =>
                      routeTo(context, ViewShift(shift: shift, status: status)),
                ),
                shift.requireApplicationByWorkers == null
                    ? IconButton(
                        icon: Icon(Icons.add_box_outlined,
                            color: welcomeTextColor),
                        onPressed: () async {
                          // book a shift
                          final result = await ProgressDialog.future(
                            context,
                            dismissable: false,
                            future: shiftRepo.bookAShift(
                              shiftId: shift.id!,
                              workerId: _profile!.workerId ?? 1,
                              agencyId: _profile!.agentId ?? 1,
                            ),
                            message:
                                Text("booking a shift..").textColor(textColor),
                            title: Text("Book Shift").textColor(textColor),
                            //backgroundColor: Colors.white70,
                            onProgressError: (err) {
                              print(err);

                              Navigator.pop(context);
                            },
                            onProgressCancel: () => Navigator.pop(context),
                          );

                          // check result
                          if (result is bool) {
                            // added ok
                            dialog.showFloatingFlushbar(
                              context: context,
                              title: 'Book Shift',
                              message: 'Shift has been booked successfully.',
                            );
                          }

                          // err
                          else {
                            // added ok
                            dialog.showFloatingFlushbar(
                              context: context,
                              title: 'Book Shift',
                              message: result.message,
                              warning: true,
                            );
                          }
                        },
                      )
                    : !shift.requireApplicationByWorkers!
                        ? IconButton(
                            icon: Icon(Icons.add_box_outlined,
                                color: welcomeTextColor),
                            onPressed: () async {
                              // book a shift
                              final result = await ProgressDialog.future(
                                context,
                                dismissable: false,
                                future: shiftRepo.bookAShift(
                                  shiftId: shift.id!,
                                  workerId: _profile!.workerId ?? 1,
                                  agencyId: _profile!.agentId ?? 1,
                                ),
                                message: Text("booking a shift..")
                                    .textColor(textColor),
                                title: Text("Book Shift").textColor(textColor),
                                //backgroundColor: Colors.white70,
                                onProgressError: (err) {
                                  print(err);

                                  Navigator.pop(context);
                                },
                                onProgressCancel: () => Navigator.pop(context),
                              );

                              // check result
                              if (result is bool) {
                                // added ok
                                dialog.showFloatingFlushbar(
                                  context: context,
                                  title: 'Book Shift',
                                  message:
                                      'Shift has been booked successfully.',
                                );
                              }

                              // err
                              else {
                                // added ok
                                dialog.showFloatingFlushbar(
                                  context: context,
                                  title: 'Book Shift',
                                  message: result.message,
                                  warning: true,
                                );
                              }
                            },
                          )
                        : Text('\t\t\t\t-\t\t\t\t\t'),
                shift.requireApplicationByWorkers == null
                    ? Text('\t\t\t\t-\t\t\t\t\t')
                    : shift.requireApplicationByWorkers ?? false
                        ? IconButton(
                            icon: Icon(Icons.business_center_rounded,
                                color: welcomeTextColor),
                            onPressed: () async {
                              // book a shift
                              final result = await ProgressDialog.future(
                                context,
                                dismissable: false,
                                future: shiftRepo.applyAShift(
                                  shiftId: shift.id!,
                                  workerId: _profile!.workerId ?? 1,
                                  agencyId: _profile!.agentId ?? 1,
                                ),
                                message: Text("applying a shift..")
                                    .textColor(textColor),
                                title: Text("Apply Shift").textColor(textColor),
                                //backgroundColor: Colors.white70,
                                onProgressError: (err) {
                                  print(err);

                                  Navigator.pop(context);
                                },
                                onProgressCancel: () => Navigator.pop(context),
                              );

                              // check result
                              if (result is bool) {
                                // added ok
                                dialog.showFloatingFlushbar(
                                  context: context,
                                  title: 'Apply Shift',
                                  message:
                                      'Shift has been applied successfully.',
                                );
                              }

                              // err
                              else {
                                // added ok
                                dialog.showFloatingFlushbar(
                                  context: context,
                                  title: 'Apply Shift',
                                  message: result.message,
                                  warning: true,
                                );
                              }
                            },
                          )
                        : Text('\t\t\t\t-\t\t\t\t\t'),
              ],
            ),
          );
          break;

        case 'CANCELLED':
          w = Expanded(
            flex: 3,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                  onPressed: () =>
                      routeTo(context, ViewShift(shift: shift, status: status)),
                ),
                Text(shift.shiftStatus ?? '---').fontSize(11),
              ],
            ),
          );
          break;

        case 'AWAITING_AUTHORIZATION':
          w = Expanded(
            flex: 3,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                //  Spacer(),
                IconButton(
                  icon: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                  onPressed: () =>
                      routeTo(context, ViewShift(shift: shift, status: status)),
                ),
                SizedBox(width: 8),
              ],
            ),
          );
          break;

        case 'BOOKED':
          w = Expanded(
            flex: 3,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                  onPressed: () =>
                      routeTo(context, ViewShift(shift: shift, status: status)),
                ),
                IconButton(
                  icon: Icon(Icons.cancel_outlined, color: Colors.redAccent),
                  onPressed: () =>
                      shiftReasonInput(context, formKey, 'Cancel', shift),
                ),
              ],
            ),
          );
          break;

        case 'APPLIED':
          w = Expanded(
            flex: 3,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                  onPressed: () =>
                      routeTo(context, ViewShift(shift: shift, status: status)),
                ),
                IconButton(
                  icon: Icon(Icons.cancel_outlined, color: Colors.redAccent),
                  onPressed: () =>
                      shiftReasonInput(context, formKey, 'Cancel', shift),
                ),
              ],
            ),
          );
          break;

        case 'AUTHORIZED':
          w = Expanded(
            flex: 3,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                  onPressed: () =>
                      routeTo(context, ViewShift(shift: shift, status: status)),
                ),
                IconButton(
                  icon: Icon(Icons.help, color: Colors.purpleAccent),
                  onPressed: () => shiftReasonInput(
                      context, formKey, 'Query', shift,
                      isQuery: true),
                ),
                IconButton(
                  icon: Icon(Icons.check_box, color: Colors.greenAccent),
                  onPressed: () {},
                ),
              ],
            ),
          );
          break;

        case 'IN_QUERY':
          w = Expanded(
            flex: 3,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                //  Spacer(),
                IconButton(
                  icon: Tooltip(
                        message: "View",
                        child: Icon(Icons.remove_red_eye, color: welcomeTextColor),
                      ),
                  onPressed: () =>
                      routeTo(context, ViewShift(shift: shift, status: status)),
                ),
                SizedBox(width: 8),
              ],
            ),
          );
          break;

        default:
          w = SizedBox.shrink();
      }

      return w;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          color: shiftDetailsColor,
          // height: 80,
          padding: const EdgeInsets.all(3),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () async {
                        var result = await agencySelector(context);

                        if (result is Map) {
                          watch.read(selectedAgencyProvider.notifier).state = result;
                        }
                      },
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              color: Colors.white,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                Text('Select Agency').fontSize(12),
                                Icon(Icons.arrow_drop_down)
                                    .iconColor(Colors.grey)
                                    .iconSize(20)
                              ],
                            ),
                          ),
                          sAgency == null
                              ? Text('')
                              : Text(sAgency['name']).fontSize(11),
                        ],
                      ),
                    ),
                  ),
                  spacer,
                  Expanded(
                    child: InkWell(
                      onTap: () async {
                        var result = await clientSelector(
                          context,
                          ApiFilter(
                            getDefault: sAgency == null,
                            id: sAgency == null ? 0 : sAgency['id'],
                          ),
                        );

                        if (result is Map) {
                          watch.read(selectedClientProvider.notifier).state = result;
                        }
                      },
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              color: Colors.white,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                Text('Select Client').fontSize(12),
                                Icon(Icons.arrow_drop_down)
                                    .iconColor(Colors.grey)
                                    .iconSize(20)
                              ],
                            ),
                          ),
                          sClient == null
                              ? Text('')
                              : Text(sClient['name']).fontSize(11),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () async {
                        var result = await locationSelector(
                          context,
                          ApiFilter(
                            getDefault: sClient == null,
                            id: sClient == null ? 0 : sClient['id'],
                          ),
                        );

                        if (result is Map) {
                          // check if client was set, if not set client automatically from response
                          if (sClient == null) {
                            watch.read(selectedClientProvider.notifier).state = {
                              "id": result['clientId'],
                              "name": result['client'],
                            };
                          }
                          watch.read(selectedLocationProvider.notifier).state = result;
                        }
                      },
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              color: Colors.white,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                Text('Select Location').fontSize(12),
                                Icon(Icons.arrow_drop_down)
                                    .iconColor(Colors.grey)
                                    .iconSize(20)
                              ],
                            ),
                          ),
                          sLocation == null
                              ? Text('')
                              : Text(sLocation['name']).fontSize(11),
                        ],
                      ),
                    ),
                  ),
                  spacer,
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 45,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            color: Colors.white,
                          ),
                          child: GestureDetector(
                            onTap: () async {
                              final DateTimeRange? picked = await showDateRangePicker(
                                context: context,
                                firstDate: DateTime.now().subtract(Duration(days: 190)),
                                lastDate: DateTime.now().add(Duration(days: 365)),
                                initialDateRange: watch.read(dateRangeProvider.notifier).state,
                              );
                              if (picked != null) {
                                if (picked.start.isBefore(DateTime.now())) {
                                  // Show error
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(content: Text('error')),
                                  );
                                } else if (picked.duration.inDays > 31) {
                                  // Show error
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(content: Text('exceeds max days limit')),
                                  );
                                } else {
                                  watch.read(dateRangeProvider.notifier).state = picked;
                                }
                              }
                            },
                            child: Container(
                              padding: EdgeInsets.all(12),
                              child: Text(
                                watch.read(dateRangeProvider.notifier).state != null
                                  ? '${DateFormat('yyyy.MM.dd').format(watch.read(dateRangeProvider.notifier).state!.start)} - ${DateFormat('yyyy.MM.dd').format(watch.read(dateRangeProvider.notifier).state!.end)}'
                                  : 'date range',
                                style: TextStyle(fontSize: 10),
                              ),
                            ),
                          ),
                        ),
                        sDate == null
                            ? SizedBox.shrink()
                            : Text('${DateFormat('yyyy.MM.dd').format(sDate.start)} - ${DateFormat('yyyy.MM.dd').format(sDate.end)} ',
                                    maxLines: 2)
                                .fontSize(8),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                    backgroundColor: filtersSet() ? deepBlueColor : Colors.grey),
                onPressed: filtersSet()
                    ? () async {
                        final result = await ProgressDialog.future(
                          context,
                          dismissable: false,
                          future: shiftRepo.getFilteredShiftByStatus(
                            FilterModel(
                              status: status.status,
                              agentId: sAgency!['id'],
                              clientId: sClient!['id'],
                              workerId: _profile!.workerId!,
                              startDate:
                                  DateFormat('yyyy-MM-dd').format(sDate!.start),
                              endDate:
                                  DateFormat('yyyy-MM-dd').format(sDate.end),
                              location: sLocation!['name'],
                            ),
                          ),
                          message: Text("fetching ${status.category} shifts..")
                              .textColor(textColor),
                          title: Text("Shifts").textColor(textColor),
                          onProgressError: (err) {
                            print(err);
                            // resetFilters();
                            //return Navigator.pop(context);
                          },
                          onProgressCancel: () {
                            resetFilters();
                            return Navigator.pop(context);
                          },
                        );

                        if (result is List<FilteredShift>) {
                          watch.read(filteredShiftsProvider.notifier).state = result;
                        }

                        //
                        if (result is CustomException) {
                          dialog.showFloatingFlushbar(
                            context: context,
                            title: 'Shift',
                            message: result.message,
                            warning: true,
                          );
                        }
                      }
                    : () {},
                icon: Icon(Icons.filter_alt),
                label: Text('filter'),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                    backgroundColor: filtersSet() ? deepBlueColor : Colors.grey),
                onPressed: () {
                  resetFilters();
                },
                icon: Icon(Icons.filter_alt_off),
                label: Text('reset'),
              ),
            ),
          ],
        ),
        Divider(height: 30),
        shifts == null
            ? Center(
                child: Padding(
                  padding: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height * 0.15,
                      left: 8,
                      right: 8),
                  child: Text(
                    'provide all required filters to view shifts',
                    style: TextStyle(
                      fontStyle: FontStyle.italic,
                      color: Colors.grey,
                    ),
                  ),
                ),
              )
            : shifts.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8.0,
                        vertical: 60,
                      ),
                      child: Text(
                        'no shifts available matching ${status.status} status shifts',
                        style: TextStyle(
                          fontStyle: FontStyle.italic,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  )
                : Expanded(
                    child: Column(
                      children: [
                        trailingHeading(status),
                        Expanded(
                          child: ListView.separated(
                            padding: EdgeInsets.all(8),
                            itemBuilder: (context, index) {
                              final FilteredShift shift = shifts[index];

                              return Row(
                                children: [
                                  Expanded(
                                    flex: 5,
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(10),
                                      child: ListTile(
                                        tileColor: tileColor,
                                        title: Text(shift.shiftDate!),
                                        subtitle:
                                            Text(shift.shiftLocation ?? 'N/A')
                                                .fontWeight(FontWeight.w600),
                                        trailing: Text(
                                                'Start\n${shift.shiftStartTime!}')
                                            .fontWeight(FontWeight.w600),
                                      ),
                                    ),
                                  ),
                                  trailingWidgets(status, shift),
                                ],
                              );
                            },
                            separatorBuilder: (_, x) => SizedBox(height: 20),
                            itemCount: shifts.length,
                          ),
                        ),
                      ],
                    ),
                  ),
      ],
    );
  }
}
