import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/widgets/edit_text.dart';
import 'package:work_link/src/utils/styles.dart';
import 'package:work_link/src/features/agencies/views/agencies_view.dart';
import 'package:work_link/src/features/clients/views/clients_view.dart';
import 'package:work_link/src/features/profile/views/profile_view.dart';
import 'package:work_link/src/features/shift-filter/views/shifts_home_view.dart';
import 'package:work_link/src/features/shifts/views/shifts_home_view.dart';

import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import 'package:work_link/src/widgets/index.dart';

import '../utils/constants.dart';
import 'ApiCallingWithoutProgressIndicator.dart';
import '../widgets/CustomProgressDialog.dart';

class ChangePassword extends StatefulWidget {
  @override
  _ChangePasswordState createState() => _ChangePasswordState();
}

class _ChangePasswordState extends State<ChangePassword> {
  final TextEditingController _oldPasswordController = TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  final _formKey = GlobalKey<FormState>();

  getProfileUpdate() async {
    try {
      CustomProgressLoader.showLoader(context);
      Map map = {
        "oldPassword": _oldPasswordController.text,
        "matchingPassword": _newPasswordController.text,
        "newPassword": _newPasswordController.text
      };

      print("map+++" + map.toString());
      var response = await ApiCalling().apiCallpost(context,
          "$userService/api/v1/user-management/user/change-password", map,"post");
      print("response+++" + response.toString());

      CustomProgressLoader.cancelLoader(context);
      if (response != null) {
        if (response.statusCode == 200) {
          if(response.data['message'].toString().contains("updated")){
            routeBack(context);
          }
        }
      }
    } catch (e) {
      CustomProgressLoader.cancelLoader(context);
      print("issue shubh" + e.toString());
      return null;
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  Widget build(BuildContext context) {

    bool validateStructure(String value){
      String  pattern = r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!@#\$&*~]).{8,}$';
      RegExp regExp = new RegExp(pattern);
      return regExp.hasMatch(value);
    }


    MediaQueryData mediaQuery = MediaQuery.of(context);
    return Form(
      key: _formKey,
      child: Scaffold(
        appBar: AppBarDefault(context,"Change Password",
            leading: Container(
              height: 30,
              width: 30,
              child: Padding(
                padding: const EdgeInsets.only(left: 15.0, right: 15),
                child: InkWell(
                  child: Icon(   Icons.arrow_back,
                    color: welcomeTextColor,
                    size: 20,
                  ),
                  onTap: () {
                    routeBack(context);
                  },
                ),
              ),
            )),
        body: Container(
          height: double.infinity,
          width: double.infinity,

          /// Set Background image in splash screen layout (Click to open code)
          decoration: const BoxDecoration(
            color: Colors.white,
          ),
          child: ListView(
            children: <Widget>[
              Padding(
                padding:
                    const EdgeInsets.only(left: 15.0, right: 15.0, top: 20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    EditText(
                      hint: 'Old Password',
                      textEditingController: _oldPasswordController,
                      keyboardType: TextInputType.text,
                      validationMessage: "Please enter old password",
                      maxLength: 30,
                      color: background,
                      textColor: Colors.black,
                      borderColor: background,
                      hgt: 60.0,
                      isHide: true,
                      isValidation: true,
                      isReadOnly: false,
                    ),
                    SizedBox(
                      height: 15.0,
                    ),
                    EditText(
                      hint: 'New Password',
                      textEditingController: _newPasswordController,
                      keyboardType: TextInputType.text,
                      validationMessage: "Please enter New password",
                      maxLength: 30,
                      color: background,
                      textColor: Colors.black,
                      borderColor: background,
                      hgt: 60.0,
                      isHide: true,
                      isValidation: true,
                      isReadOnly: false,
                    ),
                    SizedBox(
                      height: 15.0,
                    ),
                    EditText(
                      hint: 'Confirm Password',
                      textEditingController: _confirmPasswordController,
                      keyboardType: TextInputType.text,
                      validationMessage: "Please enter Confirm password",
                      maxLength: 30,
                      textColor: Colors.black,
                      color: background,
                      borderColor: background,
                      hgt: 60.0,
                    //   validate: (value){
                    //     if(!validateStructure(value??'')){
                    //       return "Password should contain at least ";
                    //     }
                    //
                    //     return (value == null || value == "" || !value.contains('@') || !value.contains('.'))
                    //         ? 'Enter valid email'
                    //         : null;
                    //
                    //     // Continue
                    // },
                      isHide: true,
                      isValidation: true,
                      isReadOnly: false,
                    ),
                    Padding(padding: EdgeInsets.all( 12),
                    child: Text("Password should contain at least on upper case, "
                        "one lower case, one special character and at least 8 characters"))
                  ],
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.only(left: 10.0, right: 10.0, top: 20.0),
                child: GestureDetector(
                  onTap: () {
                    final formState = _formKey.currentState;
                    if (formState!.validate()) {
                      formState.save();
                      getProfileUpdate();
                      //makeLoginApiCall();
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(left: 70.0, right: 70.0),
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        elevation: 8,
                        // fixedSize: Size(width * 0.9, sy(40)),
                      ),
                      onPressed: () async {
                        _submit();
                      },
                      child: Text(
                        'Submit',
                        style: TextStyle(
                          color: Colors.white,
                          // fontSize: sx(16),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _submit() {
    final isValid = _formKey.currentState!.validate();
    if (!isValid) {
      return;
    } else {
      if (_newPasswordController.text == _confirmPasswordController.text) {
        getProfileUpdate();
      }
    }
    _formKey.currentState!.save();
  }
}
