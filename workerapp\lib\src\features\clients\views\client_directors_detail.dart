import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import '../../../widgets/ApiCallingWithoutProgressIndicator.dart';
import '../../../widgets/CustomProgressDialog.dart';
import '../../../utils/UserPreference.dart';
import '../../../widgets/app_appbar.dart';
import '../model/DirectorateModel.dart';


class ClientDirectorDetail extends StatefulWidget {
  Content? cli;

  ClientDirectorDetail(this.cli);

  @override
  ClientDirectorDetailState createState() => ClientDirectorDetailState(cli);
}

class ClientDirectorDetailState extends State<ClientDirectorDetail> {
  ClientDirectorDetailState(this.cli);

  Content? cli;


  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  Widget build(BuildContext context) {
    MediaQueryData mediaQuery = MediaQuery.of(context);
    return Scaffold(
      appBar: AppBarDefault(context,"Directorate", leading:
      Container(
        height: 30,
        width: 30,
        child: Padding(
          padding: const EdgeInsets.only(left: 15.0, right: 15),
          child: InkWell(
            child: Icon(Icons.arrow_back_ios, color: welcomeTextColor,),
            onTap: () {
              routeBack(context);
            },
          ),
        ),
      )),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [

          Padding(
            padding: const EdgeInsets.fromLTRB(13.0, 13, 10, 0),
            child: Text("Client Directorate Details", style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: Colors.black)),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(13.0, 5, 10, 5),
            child: Text("24-7 Support UK", style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: welcomeTextColor)),
          ),


          Card(
            color: accentPrimaryColor,
            margin: const EdgeInsets.only(left: 10, right: 10, top: 5.0),
            elevation: 1.0,
            child: Padding(
              padding: const EdgeInsets.only(top: 6.0),
              child: Column(
                  children: [
              Padding(
              padding: const EdgeInsets.only(left:10.0),
              child: Row(
                children: [
              Expanded(child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [

              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
              Expanded(child: Container(
                width: 150,
                child: Text(
                'Location',
                  style: GoogleFonts.lato(
                      fontStyle: FontStyle.normal,
                      fontSize: 14.0,
                      color: Colors.black,fontWeight: FontWeight.bold),
                ),
              ), flex: 0,),
            Expanded(child: Text(
              cli!.location.toString(),
              style: GoogleFonts.lato(
                  fontStyle: FontStyle.normal,
                  fontSize: 14.0,
                  color: Colors.black),
            ), flex: 1,),



            ],
          ),
                  SizedBox(
                    height: 3.0,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(child: Container(
                        width: 150,
                        child: Text(
                          'Post Code',
                          style: GoogleFonts.lato(
                              fontStyle: FontStyle.normal,
                              fontSize: 14.0,
                              color: Colors.black,fontWeight: FontWeight.bold),
                        ),
                      ), flex: 0,),
                      Expanded(child: Text(
                        cli!.postCode.toString(),
                        style: GoogleFonts.lato(
                            fontStyle: FontStyle.normal,
                            fontSize: 14.0,
                            color: Colors.black),
                      ), flex: 1,),



                    ],
                  ),

                  SizedBox(
                    height: 3.0,
                  ),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(child: Container(
                        width: 150,
                        child: Text(
                          'Phone Number',
                          style: GoogleFonts.lato(
                              fontStyle: FontStyle.normal,
                              fontSize: 14.0,
                              color: Colors.black,fontWeight: FontWeight.bold),
                        ),
                      ), flex: 0,),
                      Expanded(child: Text(
                        cli!.phoneNumber.toString(),
                        style: GoogleFonts.lato(
                            fontStyle: FontStyle.normal,
                            fontSize: 14.0,
                            color: Colors.black),
                      ), flex: 1,),



                    ],
                  ),

                  SizedBox(
                    height: 3.0,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(child: Container(
                        width: 150,
                        child: Text(
                          'Email',
                          style: GoogleFonts.lato(
                              fontStyle: FontStyle.normal,
                              fontSize: 14.0,
                              color: Colors.black,fontWeight: FontWeight.bold),
                        ),
                      ), flex: 0,),
                      Expanded(child: Text(
                        "",
                        style: GoogleFonts.lato(
                            fontStyle: FontStyle.normal,
                            fontSize: 14.0,
                            color: Colors.black),
                      ), flex: 1,),



                    ],
                  ),
          SizedBox(
            height: 10.0,
          ),
        ],
      ), flex: 1,),

    ],
    ),
    ),
    ],
    ),
    ),
    )
    ],
    ),
    )
    ;
    }


}
