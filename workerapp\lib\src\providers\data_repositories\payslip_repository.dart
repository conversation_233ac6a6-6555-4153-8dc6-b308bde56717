import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/utils/constants.dart';
import 'package:work_link/src/models/payadvice/payadvice_response.dart';
import 'package:work_link/src/models/shift_controller_shift_model.dart';
import '../../utils/UserPreference.dart';
import '../../models/invoice/invoice.dart';
import '../../models/payadvice/payadvices_response.dart';
import '../../models/payslip/payslip_response.dart';
import '../../models/profile/worker_profile.dart';
import '../../utils/index.dart';

class PayslipRepository {
  Dio _dioClient;

  final _reader;

  PayslipRepository(this._reader) : _dioClient = _reader(dioProvider);

  /// get all worker payslips
  Future<PayslipResponse?> getPayslips() async {
    final options = _reader(accessKeyOptionsProvider);
    final _worker = _reader(loginResponseProvider).state;

    int workerId = _worker?.workerId ?? 1;
    try {
      final result = await _dioClient.get(
          '$dataService/api/v1/worker-payslips/$workerId/0/100?workerId=$workerId',
          options: options);

      if (result.statusCode == 200) {
        print('Getting worker payslips');
        var res = PayslipResponse.fromJson(result.data);
        print('Getting worker payslips');
        return res;
      }
      // throw error
      else {
        throw Exception('Error getting available payslips. Try again later');
      }
    } catch (e) {
      
      print(e);
      throw exceptionHandler(e, 'worker payslips',StackTrace.current);
    }
  }

  /// get all worker pay advices
  Future<PayAdvicesResponse?> getPayAdvices() async {
    final options = _reader(accessKeyOptionsProvider);
    final _worker = _reader(loginResponseProvider).state;

    int workerId = _worker?.workerId ?? 1;
    try {
      final result = await _dioClient.get(
          '$dataService/api/v1/worker-pay-advices/$workerId/0/100?workerId=$workerId',
          options: options);

      if (result.statusCode == 200) {
        print('Getting worker pay advices');
        var res = PayAdvicesResponse.fromJson(result.data);
        print('Getting worker pay advices');
        return res;
      }
      // throw error
      else {
        throw Exception('Error getting available payslips. Try again later');
      }
    } catch (e) {
      
      print(e);
      throw exceptionHandler(e, 'worker payslips',StackTrace.current);
    }
  }

  /// get worker pay advice
  Future<PayAdviceResponse?> getPayAdvice(int payadviceId) async {

    SharedPreferences prefs = await SharedPreferences.getInstance();
    int workerId= int.parse(prefs.getString(UserPreference.WORKER_ID)??"1");

    WorkerProfile worker =  await getWorkerProfile(workerId);

    final options = _reader(accessKeyOptionsProvider);
    try {
      final result = await _dioClient.get(
          '$dataService/api/v1/payAdvice/worker/view/payadvice?payAdviceId=$payadviceId',
          options: options);

      if (result.statusCode == 200) {
        print('Getting worker pay advices');
        PayAdviceResponse res = PayAdviceResponse.fromJson(result.data);
        PayAdviceResponse res2 = PayAdviceResponse.fromJson(result.data);
        res.shifts = getShifts(res2);
        res.worker = worker;

        res.agency = await getAgency(res.agentId??1);
        print('Getting worker pay advices');
        return res;
      }
      // throw error
      else {
        throw Exception('Error getting available payslips. Try again later');
      }
    } catch (e) {
      
      print(e);
      throw exceptionHandler(e, 'worker payslips',StackTrace.current);
    }
  }

  Future<Invoice?> getInvoice(int invoiceId) async {

    SharedPreferences prefs = await SharedPreferences.getInstance();
    int workerId= int.parse(prefs.getString(UserPreference.WORKER_ID)??"1");

    final options = _reader(accessKeyOptionsProvider);
    try {
      final result = await _dioClient.get(
          '$dataService/api/v1/invoice/=$invoiceId',
          options: options);

      if (result.statusCode == 200) {
        return Invoice.fromJson(result.data);
      }
      // throw error
      else {
        throw Exception('Error getting invoice. Try again later');
      }
    } catch (e) {
      
      print(e);
      rethrow;
    }
  }

  List<PayAdviceItemResult>? getShifts(PayAdviceResponse payment) {
    var pay = payment;
    List<PayAdviceItemResult> resp = [];

    pay.payAdviceItemResult?.forEach((e) {

      // var exists = resp.map((item) => item.shiftId).contains(e.shiftId);

      PayAdviceItemResult? shift;
      try{
        shift =
            resp.firstWhere((element) => element.shiftId == e.shiftId);
      }catch(e){
        shift ==null;
      }

      if(shift!=null){
       var v1 =  (shift!.total??0);
        var v2 = (e.total??0);
        // var v3 = (e.total??0);
        var i =resp.indexOf(shift);
        var s1 = resp.elementAt(i).total = v1+v2;

        // var s2 =resp.firstWhere((element) =>
        // element.shiftId == e.shiftId,
        // ).total = (shift!.total??0)+(e.total??0);

        // resp.add(s1);
        // resp.add(s2;

      }else{
         resp.add(e);
      }

    } );
    print("object");
    return resp;

  }


  Future<WorkerProfile> getWorkerProfile(int id) async {
    final options = _reader(accessKeyOptionsProvider);

    print("worker data+++"+ '$dataService/api/v1/worker/$id');


    // https: //api-test.myworklink.uk/$dataService/api/v1/agencies/0/100

    try {
      final result = await _dioClient.get(
        '$dataService/api/v1/worker/$id',
        options: options,
      );


      if (result.statusCode == 200) {
        return WorkerProfile.fromJson(result.data);
      }

      // throw error
      else {
        throw Exception(
            'There was a problem getting profile. Please try again later');
      }
    }

    //
    catch (e) {
      
      throw exceptionHandler(e, 'profile request',StackTrace.current);
    }
  }

  Future<Agency> getAgency(int id) async {
    final options = _reader(accessKeyOptionsProvider);

    print("agency data+++"+ '$dataService/api/v1/agency/$id');

    try {
      final result = await _dioClient.get(
        '$dataService/api/v1/agency/$id',
        options: options,
      );


      if (result.statusCode == 200) {
        return Agency.fromMap(
            result.data
        );
      }

      // throw error
      else {
        throw Exception(
            'There was a problem getting profile. Please try again later');
      }
    }

    //
    catch (e) {
      
      throw exceptionHandler(e, 'profile request',StackTrace.current);
    }
  }
}

