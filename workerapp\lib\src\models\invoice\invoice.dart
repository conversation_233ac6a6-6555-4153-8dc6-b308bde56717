
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:work_link/src/models/invoice/payment/payment.dart';
import 'package:work_link/src/models/shift_controller_shift_model.dart';

import '../profile/worker_profile.dart';
import 'invoice_item/invoice_item.dart';

part 'invoice.freezed.dart';
part 'invoice.g.dart';

@freezed
abstract class Invoice with _$Invoice {

  factory Invoice({



    int? id,
    int? agentId,
    int? clientId,
    String? agencyName,
    double? totalAmount,
    double? subTotalAmount,
    double? discount,
    String? invoiceStatus,
    double? vatAmount,
    double? vatPercentage,
    String? invoiceDate,
    String? dueDate,
    bool? published,
    WorkerProfile? payer,
    Agency? payee,
    List<InvoiceItem>? invoiceItemResult,
    List<Payment>? paymentRef,


  }) = _Invoice;


  factory Invoice.fromJson(Map<String, dynamic> json) =>
      _$InvoiceFromJson(json);

}
