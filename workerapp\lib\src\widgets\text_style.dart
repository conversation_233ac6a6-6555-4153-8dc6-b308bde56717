

import 'dart:ui';

import 'package:flutter/material.dart';

import '../utils/colors.dart';

final TextStyle drawerTextStyle = TextStyle(color: Colors.black,fontSize: 14,fontWeight: FontWeight.normal);

final TextStyle px18TextStyleBold = TextStyle(color: Colors.black,fontSize: 18,fontWeight: FontWeight.bold);

final TextStyle px18TextStyleNormal = TextStyle(color: welcomeTextColor,fontSize: 14,fontWeight: FontWeight.normal);
final TextStyle px18TextStyleNormaBlack = TextStyle(color: Colors.black,fontSize: 14,fontWeight: FontWeight.normal);
final TextStyle px18TextStyleNormalRED = TextStyle(color: denyRed,fontSize: 14,fontWeight: FontWeight.normal);
final TextStyle px14TextStyleNormal = TextStyle(color: Colors.black,fontSize: 14,fontWeight: FontWeight.normal);
final TextStyle px14TextStyleNormalRed = TextStyle(color: denyRed,fontSize: 14,fontWeight: FontWeight.normal);
final TextStyle px14TextStyleNormalblue = TextStyle(color: welcomeTextColor,fontSize: 14,fontWeight: FontWeight.normal);

final TextStyle px16TextStyleNormal = TextStyle(color: Color(0xFF2144C4),fontSize: 16,fontWeight: FontWeight.normal);
final TextStyle px16TextStyleBold = TextStyle(color: Color(0xFF2144C4),fontSize: 16,fontWeight: FontWeight.bold);
final TextStyle px16TextStyleNormalRed = TextStyle(color: denyRed,fontSize: 16,fontWeight: FontWeight.normal);