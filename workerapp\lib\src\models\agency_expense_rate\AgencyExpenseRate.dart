import 'package:freezed_annotation/freezed_annotation.dart';

import '../expense_rate/ExpenseRate.dart';

part 'AgencyExpenseRate.freezed.dart';
part 'AgencyExpenseRate.g.dart';

@freezed
abstract class AgencyExpenseRate with _$AgencyExpenseRate {
  factory AgencyExpenseRate({
    int? id,
    double? rate,
    ExpenseRate? expenseRate,
  }) = _AgencyExpenseRate;

  factory AgencyExpenseRate.fromJson(Map<String, dynamic> json) =>
      _$AgencyExpenseRateFromJson(json);
}
