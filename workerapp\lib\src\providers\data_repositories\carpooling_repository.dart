import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:work_link/src/models/carpooling_workers/CarPoolingWorker.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/utils/constants.dart';
import 'package:work_link/src/features/shift-filter/data/filter_model.dart';
import 'package:work_link/src/models/agency_expense_rate/AgencyExpenseRate.dart';
import 'package:work_link/src/models/filter-shift/filtered_shift.dart';
import 'package:work_link/src/models/shift/ShiftsPageResp.dart';

import '../../utils/UserPreference.dart';
import '../../utils/dio_client.dart';
import '../../utils/index.dart';

final carPoolingWorkersProvider =  FutureProvider.autoDispose.family<List<CarPoolingWorker>, int>((ref, shiftId){
  return ref.read(carpoolingRepositoryProvider).getCarPoolingWorkers(shiftId);
});


final carpoolingRepositoryProvider = Provider<ShiftRepository>((ref) => ShiftRepository(ref));


class ShiftRepository {
  final Ref ref;
  ShiftRepository(this.ref);

  Future<List<CarPoolingWorker>> getCarPoolingWorkers(int shiftId) async {
    List result = await DioClient.instance.get( '$worklinkApiUrl/shift/pooling/workers/$shiftId' );
   return result.map((e) => CarPoolingWorker.fromJson(e)).toList();
  }

}
