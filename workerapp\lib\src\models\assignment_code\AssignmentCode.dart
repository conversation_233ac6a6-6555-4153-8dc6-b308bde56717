import 'package:freezed_annotation/freezed_annotation.dart';

part 'AssignmentCode.freezed.dart';
part 'AssignmentCode.g.dart';

@freezed
abstract class AssignmentCode with _$AssignmentCode {
  factory AssignmentCode({
    int? id,
    String? name,
    String? code,
    // Services services,
  }) = _AssignmentCode;

  factory AssignmentCode.fromJson(Map<String, dynamic> json) =>
      _$AssignmentCodeFromJson(json);
}
