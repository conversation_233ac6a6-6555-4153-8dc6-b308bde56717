
import 'package:freezed_annotation/freezed_annotation.dart';

import '../address/Address.dart';

part 'Agency.freezed.dart';
part 'Agency.g.dart';

@freezed
abstract class Agency with _$Agency {
  factory Agency({

     Address? address,
     String? billingEmail,
     String? email,
     int? id,
     String? logo,
     String? name,
     // String? service,
     String? status,
     String? telephone,
     int? version,


  }) = _Agency;

  factory Agency.fromJson(Map<String, dynamic> json) =>
      _$AgencyFromJson(json);
}
