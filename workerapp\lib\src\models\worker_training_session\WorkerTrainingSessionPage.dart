
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:work_link/src/features/agencies/model/AgencyModel.dart';

import '../Pageable.dart';
import 'WorkerTrainingSession.dart';

part 'WorkerTrainingSessionPage.freezed.dart';
part 'WorkerTrainingSessionPage.g.dart';

@freezed
abstract class WorkerTrainingSessionsPage with _$WorkerTrainingSessionsPage {
  factory WorkerTrainingSessionsPage({

    List<WorkerTrainingSession>? content,
    Pageable? pageable,
    int? totalPages,
    int? totalElements,
    bool? last,
    int? size,
    int? number,
    Sort? sort,
    int? numberOfElements,
    bool? first,
    bool? empty,


  }) = _WorkerTrainingSessionsPage;

  factory WorkerTrainingSessionsPage.fromJson(Map<String, dynamic> json) =>
      _$WorkerTrainingSessionsPageFromJson(json);

}
