import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ndialog/ndialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:styled_widget/styled_widget.dart';
import '../../../providers/app_providers.dart';
import '../../../utils/color_constants.dart';
import '../../../widgets/ApiCallingWithoutProgressIndicator.dart';
import '../../../utils/UserPreference.dart';
import '../../../utils/constants.dart';
import '../../../utils/colors.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

bool available = true;

class CalendarWidget extends StatefulWidget {
  final List<DateTime> bookedDates ;
  final List<DateTime> unavailableDates ;

  const CalendarWidget({Key? key, required this.unavailableDates, required this.bookedDates}) : super(key: key);
  @override
  _CalendarWidgetState createState() => _CalendarWidgetState();
}

class _CalendarWidgetState extends State<CalendarWidget> {
  DateTime? _selectedDay;

  List<DateTime> _blackoutDates = <DateTime>[
    DateTime.now().add(Duration(days: 12)),
    DateTime.now().add(Duration(days: 13)),
    DateTime.now().add(Duration(days: 16)),
    DateTime.now().add(Duration(days: 17))
  ];

  Future<bool> updateAvailability(String date) async {
    var prefs = await SharedPreferences.getInstance();
    String workerId = await prefs!.getString(UserPreference.WORKER_ID)!;


      return addDate(workerId, date);

  }


    Future<bool> addDate(String workerId,String date) async {
    try {
      var data = {
        "date": date,
        "endTime": "00:00",
        "reason": "",
        "isAvailable": available!,
        "startTime": "00:00",
        "workerId": workerId
      };

      print(data);
      Response? response = await ApiCalling()
          .apiCallpost(context, "$dataService/api/v1/availability",data, "post");

      if (response != null) {
        if (response.statusCode == 201) {
          print("no issue occured: " );


          return true;

        }
      }
      print("issue occured: " );

      return false;

    } catch (e) {

      print("issue occured: " + e.toString());
      return false;
    }
  }

  Future<void> _onDaySelected(DateRangePickerSelectionChangedArgs args) async {
    print(args.value);
    if (args.value is PickerDateRange) {
      final DateTime rangeStartDate = args.value.startDate;
      final DateTime rangeEndDate = args.value.endDate;
    } else if (args.value is DateTime) {
      final DateTime selectedDate = args.value;
      _selectedDay = args.value;
    } else if (args.value is List<DateTime>) {
      final List<DateTime> selectedDates = args.value;
    } else {
      final List<PickerDateRange> selectedRanges = args.value;
    }



     await showDialog<bool?>(
        context: context,
        builder: (ctx) {
          return AlertDialog(
            contentPadding : const EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 0.0),
            content: Container(
              height: 255,
              child: Column(
                children: [
                  Container(
                      height: 50,
                      width: double.infinity,
                      color: welcomeTextColor,
                      child: Padding(
                        padding: const EdgeInsets.only(top:10.0),
                        child: Text('Availabity',textAlign: TextAlign.center,style: TextStyle(fontSize: 20,color: Colors.white,fontWeight: FontWeight.w500),),
                      )),
                  Padding(
                      padding: const EdgeInsets.only(top:25.0),
                      child:   Text('Change availability status for: ')),
                  Padding(
                      padding: const EdgeInsets.only(bottom: 15),
                      child:   Text(DateFormat("EEEE d MMMM y").format(args.value), style: TextStyle(
                        fontWeight: FontWeight.w700,
                      ),)),
                  SwitchExample(),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Consumer(
                          builder: (context1, watch, child) {
                            final dialog = watch.watch(dialogProvider);
                            _pop(){
                              Navigator.pop(ctx, false);
                            };
                            return ElevatedButton.icon(
                              label: Text('Done'),
                              icon: Icon(Icons.done),

                              style: ElevatedButton.styleFrom(
                                backgroundColor: welcomeTextColor,
                              ),
                              onPressed: () async {

                                _pop();


                                if (true) {

                                  var selected = DateFormat("yyyy-MM-dd").format(args.value);
                                  // submit reason
                                  // Cancel a shift
                                  final result = await ProgressDialog.future(
                                    context,
                                    dismissable: false,
                                    future: updateAvailability(selected),
                                    message: Text("Availability")
                                        .textColor(textColor),
                                    title: Text("Update availability")
                                        .textColor(textColor),
                                    //backgroundColor: Colors.white70,
                                    onProgressError: (err) {
                                      print(err.toString());
                                    },
                                  );
                                  // Navigator.pop(context);

                                  print(widget.unavailableDates);
                                  print(args.value);
                                  // check result
                                  available ? widget.unavailableDates.add(args.value)
                                  : widget.unavailableDates.removeWhere((element) => element==args.value);

                                  print(widget.unavailableDates);

                                  setState(() {

                                  });
                                  if (result == true) {



                                    dialog.showFloatingFlushbar(
                                      context: context,
                                      title: 'Availability',
                                      message:
                                      'Availability has been updated successfully.',
                                    );


                                  }

                                  // err
                                  else {
                                    // added ok
                                    dialog.showFloatingFlushbar(
                                      context: context,
                                      title: 'Availability',
                                      message: "An error occured while updating availability.",
                                      warning: true,
                                    );
                                    // Navigator.pop(context);

                                  }
                                }

                              },
                            );
                          }
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left:15.0),
                        child: ElevatedButton.icon(
                          icon: Icon(Icons.cancel_outlined),
                          label: Text('Cancel'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: denyRed,
                          ),
                          onPressed: () {
                            // TODO process
                            Navigator.pop(ctx, false);
                          },
                        ),
                      ),
                    ],)
                ],
              ),
            ),

          );
        });
  }

  Future<void> showLeaveDialog() async {



     await showDialog<bool?>(
        context: context,
        builder: (ctx) {
          return AlertDialog(
            contentPadding : const EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 0.0),
            content: Container(
              height: 455,
              width: 400,
              child: Column(
                children: [
                  Container(
                      height: 50,
                      width: double.infinity,
                      color: welcomeTextColor,
                      child: Center(
                        child: Text('Select Leave Days',textAlign: TextAlign.center,style: TextStyle(fontSize: 20,color: Colors.white,fontWeight: FontWeight.w500),),
                      )),
                  SfDateRangePicker(
                    view: DateRangePickerView.month,
                    selectionMode: DateRangePickerSelectionMode.range,
                  ),

                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Consumer(
                          builder: (context1, watch, child) {
                            final dialog = watch.watch(dialogProvider);
                            _pop(){
                              Navigator.pop(ctx, false);
                            };
                            return ElevatedButton(
                              child: Text('Apply'),
                              style: ElevatedButton.styleFrom(backgroundColor: welcomeTextColor,),
                              onPressed: () async {
                                _pop();
                              },
                            );
                          }
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left:15.0),
                        child: ElevatedButton(
                          child: Text('Cancel'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: denyRed,
                          ),
                          onPressed: () {
                            // TODO process
                            Navigator.pop(ctx, false);
                          },
                        ),
                      ),
                    ],)
                ],
              ),
            ),

          );
        });
  }


  @override
  Widget build(BuildContext context) {

    return Container(
      decoration: BoxDecoration(
          color: secondaryColor, borderRadius: BorderRadius.circular(10)),
      padding: EdgeInsets.all(10),
      child: Column(
        children: [
          SizedBox(
            height: 10,
          ),
          SfDateRangePicker(
            minDate: DateTime.now(),
            view: DateRangePickerView.month,
            monthViewSettings:DateRangePickerMonthViewSettings(
                specialDates: widget.unavailableDates,
                blackoutDates: widget.bookedDates,
                showTrailingAndLeadingDates: true),
            monthCellStyle: DateRangePickerMonthCellStyle(
              blackoutDatesDecoration: BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle),
              weekendDatesDecoration: BoxDecoration(
                  color: const Color(0xFFEFEFEF),
                  shape: BoxShape.circle),
              specialDatesDecoration: BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle),
              blackoutDateTextStyle: TextStyle(color: Colors.white, decoration: TextDecoration.none),
              specialDatesTextStyle: const TextStyle(color: Colors.white),
            ),
            onSelectionChanged: _onDaySelected,

          ),
          SizedBox(height: 8,),
          ElevatedButton.icon(
            label: Text('Apply For Leave'),
            icon: Icon(Icons.done),

            style: ElevatedButton.styleFrom(
              backgroundColor: welcomeTextColor,
            ),
            onPressed: () {
              showLeaveDialog();
            },
          ),
          SizedBox(height: 8,),
        ],
      ),
    );
  }
}



class SwitchExample extends StatefulWidget {
  const SwitchExample();

  @override
  State<SwitchExample> createState() => _SwitchExampleState();
}

class _SwitchExampleState extends State<SwitchExample> {
  bool light = true;

  @override
  Widget build(BuildContext context) {
    return Center(
      child:  Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Switch(
            value: available,
            inactiveThumbColor: Colors.green,
            activeColor: Colors.red,
            onChanged: (bool value) {
              print(value);
              available = value;
              setState(() {

              });
            },
          ),
          available? Text("Unavailable") : Text("Available"),

        ],
      ),);
  }
}


