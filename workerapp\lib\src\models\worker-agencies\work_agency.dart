// To parse this JSON data, do
//
//     final workerAgency = workerAgencyFromMap(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';

part 'work_agency.freezed.dart';
part 'work_agency.g.dart';

@freezed
abstract class WorkAgency with _$WorkAgency {
  factory WorkAgency({
    String? name,
    int? id,
    String? address,
    String? email,
    String? telephone,
    String? logo,
    String? status,
  }) = _WorkAgency;

  factory WorkAgency.fromJson(Map<String, dynamic> json) =>
      _$WorkAgencyFromJson(json);
}
