import 'dart:async';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:work_link/src/features/payrol/print/templates/invoice5.dart';
import 'package:work_link/src/models/payadvice/payadvice_response.dart';

const examples = <Example>[
  Example('Plain', 'invoice5.dart', generateInvoice5),
];

typedef LayoutCallbackWithData = Future<Uint8List> Function(
    PdfPageFormat pageFormat, PayAdviceResponse data);

class Example {
  const Example(this.name, this.file, this.builder, [this.needsData = false]);

  final String name;

  final String file;

  final LayoutCallbackWithData builder;

  final bool needsData;
}
