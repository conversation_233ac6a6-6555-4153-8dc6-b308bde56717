import 'package:dio/dio.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/utils/constants.dart';

import '../../models/worker_notifications_response.dart';
import '../../utils/index.dart';

class NotificationRepository {
  Dio _dioClient;
 
  final  _reader;

   NotificationRepository(this._reader) : _dioClient = _reader(dioProvider);

  /// get all shift by status
  Future<WorkerNotificationResponse?> getNotifications() async {
    final options = _reader(accessKeyOptionsProvider);
    final _worker = _reader(loginResponseProvider);

    int workerId = _worker?.workerId ?? 1;
    try {
      final result = await _dioClient.get(
          '$dataService/api/v1/notifications/$workerId/0/100',
          options: options);


      if (result.statusCode == 200) {
        var l = result.data as List;

        if (l.isEmpty) {
          return WorkerNotificationResponse(content: []);
        }

        final List<Map<String, dynamic>> shiftsR = List.from(l);



        List<Content> shifts = shiftsR.map((e) => Content.fromJson(e)).toList();


        return WorkerNotificationResponse(content: shifts);
      }

      // throw error
      else {
        throw Exception(
            'Error getting available notifications. Try again later');
      }
    }
    catch (e) {
      
      throw exceptionHandler(e, 'worker notifications',StackTrace.current);
    }
  }

}
