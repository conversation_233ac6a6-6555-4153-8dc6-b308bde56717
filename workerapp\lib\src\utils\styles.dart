import 'package:flutter/material.dart';

import 'colors.dart';

class Styles {
  static final ButtonStyle flatButtonStyle = TextButton.styleFrom(
    foregroundColor: Colors.white,
    fixedSize: const Size(10, 45),
    minimumSize: const Size(10, 45),
    padding: const EdgeInsets.symmetric(horizontal: 16.0),
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(8.0)),
    ),
    backgroundColor:  deepBlueColor,
  );
}
