import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:line_icons/line_icons.dart';
import 'package:ndialog/ndialog.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';

import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/features/shift-filter/logic/shift_reason_input.dart';
import 'package:work_link/src/features/shifts/data/shift_category.dart';
import 'package:work_link/src/models/filter-shift/filtered_shift.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

import 'trailing_header_widget.dart';

class ViewShift extends StatelessWidget {
  ViewShift({Key? key, required this.shift, required this.status})
      : super(key: key);

  final formKey = GlobalKey<FormBuilderState>();

  final FilteredShift shift;
  final ShiftCategoryStatus status;

  Widget shiftRowDetail(
    BuildContext context,
    String title1,
    String? msg1,
    String title2,
    String? msg2, {
    Widget third = const SizedBox.shrink(),
  }) {
    return Padding(
      padding: const EdgeInsets.only(top: 15, left: 5, right: 5),
      child: Row(
        children: [
          Expanded(
            child: ShiftTextEntryField(
              ctx: context,
              title: title1,
              initialText: msg1 ?? '',

            ),
          ),
          Expanded(
            child: ShiftTextEntryField(
              ctx: context,
              title: title2,
              initialText: msg2 ?? '',
            ),
          ),
          third,
        ],
      ),
    );
  }

  Widget footerWidget(
      BuildContext context, ShiftCategoryStatus status, FilteredShift shift) {
    var w;

    switch (status.status) {
      case 'CANCELLED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: () => routeBack(context),
                  icon: Icon(LineIcons.chevronLeft),
                  label: Text('Back'),
                ),
              ],
            );
          }),
        );
        break;

      case 'NEW':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Consumer(builder: (context1, watch, child) {
              final dialog =watch.watch(dialogProvider);
              final shiftRepo =watch.watch(shiftRepoProvider);
              final _profile =watch.watch(loginResponseProvider);

              bool tt = shift.requireApplicationByWorkers ?? false;
              String sType = tt ? 'Apply' : 'Book';
              String plural = '${sType}ing';
              String ed = '${sType}ed';

              return Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton.icon(
                    onPressed: () => routeBack(context),
                    icon: Icon(LineIcons.chevronLeft),
                    label: Text('Back'),
                  ),
                  SizedBox(width: sx(50)),
                  ElevatedButton.icon(
                    onPressed: () async {
                      // book a shift

                      final result = await ProgressDialog.future(
                        context,
                        dismissable: false,
                        future: tt
                            ? shiftRepo.applyAShift(
                                shiftId: shift.id!,
                                workerId: _profile!.workerId ?? 1,
                                agencyId: _profile!.agentId ?? 1,
                              )
                            : shiftRepo.bookAShift(
                                shiftId: shift.id!,
                                workerId: _profile!.workerId ?? 1,
                                agencyId: _profile!.agentId ?? 1,
                              ),
                        message: Text("$plural a shift..").textColor(textColor),
                        title: Text("$sType Shift").textColor(textColor),
                        //backgroundColor: Colors.white70,
                        onProgressError: (err) {
                          Navigator.pop(context);
                        },
                        onProgressCancel: () => Navigator.pop(context),
                      );

                      // check result
                      if (result is bool) {
                        // added ok
                        dialog.showFloatingFlushbar(
                          context: context,
                          title: '$sType Shift',
                          message: 'Shift has been $ed successfully.',
                        );
                      }

                      // err
                      else {
                        // added ok
                        dialog.showFloatingFlushbar(
                          context: context,
                          title: '$sType Shift',
                          message: result.message,
                          warning: true,
                        );
                      }
                    },
                    icon: Icon(LineIcons.book),
                    label: Text('$sType Shift'),
                  ),
                ],
              );
            });
          }),
        );
        break;

      case 'AWAITING_AUTHORIZATION':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: () => routeBack(context),
                  icon: Icon(LineIcons.chevronLeft),
                  label: Text('Back'),
                ),
                SizedBox(width: sx(50)),
              ],
            );
          }),
        );
        break;

      case 'BOOKED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: () => routeBack(context),
                  icon: Icon(LineIcons.chevronLeft),
                  label: Text('Back'),
                ),
                SizedBox(width: sx(50)),
                ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.redAccent,
                  ),
                  onPressed: () =>
                      shiftReasonInput(context, formKey, 'Cancel', shift),
                  icon: Icon(LineIcons.ban),
                  label: Text('Cancel Shift'),
                ),
              ],
            );
          }),
        );
        break;

      case 'APPLIED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: () => routeBack(context),
                  icon: Icon(LineIcons.chevronLeft),
                  label: Text('Back'),
                ),
                SizedBox(width: sx(50)),
                ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.redAccent,
                  ),
                  onPressed: () =>
                      shiftReasonInput(context, formKey, 'Cancel', shift),
                  icon: Icon(LineIcons.ban),
                  label: Text('Cancel Shift'),
                ),
              ],
            );
          }),
        );
        break;

      case 'AUTHORIZED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: () => routeBack(context),
                  icon: Icon(LineIcons.chevronLeft),
                  label: Text('Back'),
                ),
                SizedBox(width: sx(50)),
                ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.redAccent,
                  ),
                  onPressed: () => shiftReasonInput(
                      context, formKey, 'Query', shift,
                      isQuery: true),

                  icon: Icon(LineIcons.ban),
                  label: Text('Query Shift'),
                ),
              ],
            );
          }),
        );
        break;

      case 'IN_QUERY':
        w = RelativeBuilder(builder: (context, height, width, sy, sx) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: sy(10)),
              ShiftTextEntryField(
                ctx: context,
                title: 'QUERY',
                fieldHeight: 180,
                maxLines: 5,
                initialText: shift.queriedReason,
              ),
              Padding(
                padding: const EdgeInsets.only(bottom: 15, right: 20),
                child:
                    RelativeBuilder(builder: (context, height, width, sy, sx) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () => routeBack(context),
                        icon: Icon(LineIcons.chevronLeft),
                        label: Text('Back'),
                      ),
                    ],
                  );
                }),
              ),
            ],
          );
        });
        break;

      default:
        w = SizedBox.shrink();
    }

    return w;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppAppBar(
        context,
        leading: IconButton(
          onPressed: () => routeBack(context),
          icon: Icon(
            Icons.chevron_left,
            color: welcomeTextColor,
            size: 35,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: RelativeBuilder(builder: (context, height, width, sy, sx) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: sy(10)),
              Center(
                child: Text(status.category)
                    .fontWeight(FontWeight.bold)
                    .fontSize(sx(23)),
              ),
              SizedBox(height: sy(10)),
              Container(
                color: shiftDetailsColor,
                height: 50,
                width: double.infinity,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Text('Shift Details:'),
                      SizedBox(width: 8),
                      Text(shift.id.toString()).fontWeight(FontWeight.bold),
                      Spacer(),
                      trailingHeaderWidget(status, shift),
                    ],
                  ),
                ),
              ),
              shiftRowDetail(
                context,
                'SHIFT DIRECTORATE',
                shift.directorate,
                'LOCATION NAME',
                shift.shiftLocation,
              ),
              shiftRowDetail(
                context,
                'PHONE NUMBER',
                shift.phoneNumber ?? 'N/A',
                'POSTCODE',
                shift.postCode ?? 'N/A',
              ),
              shiftRowDetail(
                context,
                'SHIFT TYPE',
                shift.shiftType,
                'SHIFT DATE',
                shift.shiftDate,
              ),
              shiftRowDetail(
                context,
                'START TIME',
                shift.shiftStartTime,
                'END TIME',
                shift.shiftEndTime,
                third: Expanded(
                  child: ShiftTextEntryField(
                    ctx: context,
                    title: 'BREAK TIME',
                    initialText: shift.breakTime,
                  ),
                ),
              ),
              SizedBox(height: sy(10)),
              shift.showNoteToFw ?? false
                  ? Padding(
                      padding: EdgeInsets.only(bottom: sy(10)),
                      child: ShiftTextEntryField(
                        ctx: context,
                        title: 'NOTES',
                        fieldHeight: 180,
                        maxLines: 5,
                        initialText: shift.notes,
                      ),
                    )
                  : const SizedBox.shrink(),
              footerWidget(context, status, shift),
            ],
          );
        }),
      ),
    );
  }
}
