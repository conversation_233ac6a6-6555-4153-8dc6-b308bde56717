import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:work_link/src/models/assignment_code/AssignmentCode.dart';

part 'WorkerProfile2.freezed.dart';
part 'WorkerProfile2.g.dart';

@freezed
abstract class WorkerProfile2 with _$WorkerProfile2 {
  factory WorkerProfile2({
    int? id,
    String? firstname,
    String? lastname,
    String? gender,
    String? phoneNumber,
    String? email,
    String? username,
    AssignmentCode? assignmentCode,
    String? status,
    String? createdBy,
  }) = _WorkerProfile2;

  factory WorkerProfile2.fromJson(Map<String, dynamic> json) =>
      _$WorkerProfile2FromJson(json);
}
