
import 'package:freezed_annotation/freezed_annotation.dart';

part 'Shift.freezed.dart';
part 'Shift.g.dart';

@freezed
abstract class Shift with _$Shift {

 factory Shift({

  int? id,
  int? workerSpecId,
  String? shiftLocation,
  String? postCode,
  String? cost,
  int? lastAuthorisationReminder,
  bool? publishToAllAgencies,
  String? trainer,
  String? trainingName,
  String? payer,
  String? phoneNumber,
  String? directorate,
  DateTime? start,
  DateTime? end,
  int? numberOfStaff,
  String? paddress,
  String? ppostCode,
  String? pward,
  String? daddress,
  String? dpostCode,
  String? dward,
  bool? released,
  String? releaseDate,
  String? appliedStatus,
  String? actualStart,
  String? breakTime,
  int? agencyId,
  String? gender,
  String? shiftType,
  String? assignmentCode,
  String? notes,
  bool? showNoteToFw,
  bool? showNoteToAgency,
  bool? requireApplicationByWorkers,
  int? hoursBeforeBroadcasting,
  String? shiftStatus,
  String ? agency,
  String? worker,
  String? client,
  String? bookingType,
  bool? isAgencyBilled,
  String? cancelledReason,
  String? queriedReason,
  String? createdBy,
  DateTime? appliedDate,
  DateTime? bookedDate,
  DateTime? authorizedDate,
  DateTime? queriedDate,
  DateTime? cancelledDate,
  List<int?>? agencies,

 }) = _Shift;


 factory Shift.fromJson(Map<String, dynamic?> json) =>
     _$ShiftFromJson(json);

}
