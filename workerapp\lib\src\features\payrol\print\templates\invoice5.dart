import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart' as qa;

import 'package:flutter/services.dart' show rootBundle;
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart';
import 'package:work_link/src/models/payadvice/payadvice_response.dart';
import 'package:http/http.dart' as http;

import '../../view/view_pay_advice.dart';

Future<Uint8List> generateInvoice5(
    PdfPageFormat pageFormat, PayAdviceResponse data) async {
  final lorem = LoremText();
  PdfColor mainColor;
  PdfColor accent;
  PdfColor lightAccent;
  PdfColor complimentAccent;
  // if(data.business!=null && data.business!.color!=null) {
  if(false) {
    // var colo = Color(data.business!.color!);
    // double red = colo.red.toDouble()>=0?colo.red.toDouble()/255 : 0.0;
    // double green = colo.green.toDouble()>=0?colo.green.toDouble()/255 : 0.0;
    // double blue = colo.blue.toDouble()>=0?colo.blue.toDouble()/255 : 0.0;
    // print(red.toString()+" "+green.toString()+" "+blue.toString());
    // mainColor = PdfColor(red, green, blue);
    //
    // double red1 = red*1.7>=1?1 : red*1;
    // double green1 = green*1.7>=1?1 : green*1;
    // double blue1 = blue*1.7>=1?1 : blue*1;
    //
    // accent = PdfColor(red*0.3, green*0.3, blue*0.3);
    // accent = PdfColor(red*0.3, green*0.3, blue*0.3);
    // lightAccent = PdfColor(red1, green1, blue1);
    // complimentAccent = PdfColor((red*red*red), (green*green*green), (blue*blue*blue));
  }else{
    mainColor = PdfColors.teal;
    accent = PdfColors.blueGrey900;
    lightAccent = PdfColors.red;
    complimentAccent = PdfColors.green;

  }

  final payAdvice = LocalInvoice(
    payAdviceNumber: data.id.toString(),
    products: data.payAdviceItemResult!,
    logo: '',
    customerName: '',
    customerAddress: '',
    paymentInfo:
        "",
    tax: .0,
    baseColor: mainColor,
    accentColor: accent,
    lightAccent: lightAccent,
    complimentAccent: complimentAccent, 
    payAdvice: data,
  );

  return await payAdvice.buildPdf(pageFormat);
}

class LocalInvoice {
  LocalInvoice({
    required this.products,
    required this.logo,
    required this.customerName,
    required this.customerAddress,
    required this.payAdviceNumber,
    required this.tax,
    required this.payAdvice,
    required this.paymentInfo,
    required this.baseColor,
    required this.accentColor,
    required this.lightAccent,
    required this.complimentAccent,
  });

  final List<PayAdviceItemResult> products;
  final String logo;
  final PayAdviceResponse payAdvice;
  final String customerName;
  final String customerAddress;
  final String payAdviceNumber;
  final double tax;
  final String paymentInfo;
  final PdfColor baseColor;
  final PdfColor accentColor;
  final PdfColor lightAccent;
  final PdfColor complimentAccent;

  static const _darkColor = PdfColors.black;
  static const _lightColor = PdfColors.white;

  PdfColor get _baseTextColor => baseColor.isLight ? _lightColor : _darkColor;

  PdfColor get _accentTextColor => baseColor.isLight ? _lightColor : _darkColor;

  double get _total =>
      products.map<double>((p) => p.total!).reduce((a, b) => a + b);

  double get _grandTotal => _total * (1 + tax);

  ImageProvider? _logo;

  String? _bgShape;
  String? _bgShape2;

  Future<Uint8List> buildPdf(PdfPageFormat pageFormat) async {
    // Create a PDF document.
    final doc = Document();


    final directory = await getDownloadPath2();

    if(payAdvice.agency?.logo!=null) {


      http.Response response = await http.get(
          Uri.parse(payAdvice.agency!.logo!)
      );
      var bytes = response.bodyBytes;
      _logo = MemoryImage(
        (bytes).buffer.asUint8List(),
      );

    }else{
      var bytes = await rootBundle.load('assets/profile.jpg');
      _logo = MemoryImage(
        (bytes).buffer.asUint8List(),
      );
    }





    var robotoRegularFont = await rootBundle.load("assets/fonts/Roboto/Roboto-Regular.ttf");
    var robotoRegular = Font.ttf(robotoRegularFont);
    var robotoBold = Font.ttf(robotoRegularFont);
    var robotoItalic = Font.ttf(robotoRegularFont);


    // Add page to the PDF
    doc.addPage(
      MultiPage(
        pageTheme: _buildTheme(
          pageFormat,
          await robotoRegular,
          await robotoBold,
          await robotoItalic,
        ).copyWith(
          margin:  const EdgeInsets.all(50),
        ),
        // header: _buildHeader,
        footer: _buildFooter,
        build: (context) => [
          _contentHeader(context),
        ],
      ),
    );

    // Return the PDF file content
    return doc.save();
  }

  Widget _buildHeader(Context context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(bottom: 18,  top: 10, right: 10),
                    height: 90,
                    child:
                    _logo != null ? Image(_logo!) : Text("INVOICE",
                      style: TextStyle(
                      color: PdfColors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 40,
                    ),),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                children: [
                  Container(
                    // height: 50,
                    padding: const EdgeInsets.only(left: 20, top: 18),
                    alignment: Alignment.centerRight,
                    child: Text(
                      'QUOTE',
                      style: TextStyle(
                        color: PdfColors.grey700,
                        fontWeight: FontWeight.bold,
                        fontSize: 40,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          ],
        ),
        if (context.pageNumber > 1) SizedBox(height: 20)
      ],
    );
  }

  Widget _buildFooter(Context context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Container(
          height: 20,
          width: 100,
          child: Text("Worklink ©", style: TextStyle(color: PdfColors.grey, fontSize: 9)),
        ),
        // Container(
        //   height: 20,
        //   width: 100,
        //   child: BarcodeWidget(
        //     barcode: Barcode.pdf417(),
        //     data: 'Invoice $payAdviceNumber',
        //     drawText: false,
        //   ),
        // ),
        Text(
          'Page ${context.pageNumber}/${context.pagesCount}',
          style: const TextStyle(
            fontSize: 12,
            color: PdfColors.white,
          ),
        ),
      ],
    );
  }

  PageTheme _buildTheme(
      PdfPageFormat pageFormat, Font base, Font bold, Font italic) {
    return PageTheme(
      pageFormat: pageFormat,
      theme: ThemeData.withFont(
        base: base,
        bold: bold,
        italic: italic,
      ),
    );
  }

  Widget _contentHeader(Context context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [

        Padding(
            padding: const EdgeInsets.only(right: 13, top: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Pay Advice: '+payAdvice.id.toString(),
                  style: TextStyle(
                      color: PdfColors.black,
                      fontSize: 22,
                      fontWeight: FontWeight.bold),
                )
              ],
            )),

        Padding(
          padding: const EdgeInsets.only(left:20.0,right: 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                  children: [
                    Expanded(
                      child:Padding(
                      padding: const EdgeInsets.only(top: 20,),
                      child: Table(
                        border: TableBorder.all(color: PdfColors.black,
                          width: 1.1,
                          // borderRadius: BorderRadius.only(
                          //   topLeft: Radius.circular(3.0),
                          //   bottomLeft: Radius.circular(3.0),
                          //   topRight: Radius.circular(3.0),
                          //   bottomRight: Radius.circular(3.0),
                          // )
                        ),
                        columnWidths: const {
                          0: FlexColumnWidth(4),
                        },
                        children: [
                          TableRow(children: [
                            SizedBox(
                              width: 95,
                              height: 81,
                              child: Center(
                                  child:  Image(_logo!)
                              ),),




                            // Text("25", style: TextStyleOutput(fontSize: 15.0),),
                          ]),
                        ],
                      ),
                    ),
                      flex: 2
                    ),
                    Expanded(
                      child: Padding(
                      padding: const EdgeInsets.only(top: 20.0,),
                      child: Table(
                        border: TableBorder.all(color: PdfColors.black,
                          width: 1.1,
                          // borderRadius: BorderRadius.only(
                          //   topLeft: Radius.circular(3.0),
                          //   bottomLeft: Radius.circular(3.0),
                          //   topRight: Radius.circular(3.0),
                          //   bottomRight: Radius.circular(3.0),
                          // )
                        ),
                        columnWidths:  {
                          0: FlexColumnWidth(4),
                          1: FlexColumnWidth(4),
                        },
                        children: [
                          TableRow(children: [
                            Padding(padding: EdgeInsets.all(5),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("EMPLOYEE NAME:", style: TextStyle(
                                      color: PdfColors.black,
                                      fontSize: 14,),),
                                    Text((payAdvice.worker?.firstname ??"")+" "+(payAdvice.worker?.lastname ??""), style: TextStyle(fontSize: 12.0),),

                                  ],) ),
                            Padding(padding: EdgeInsets.all(5),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("JOB TITLE:", style: TextStyle(
                                      color: PdfColors.black,
                                      fontSize: 14,),),
                                    Text((payAdvice.worker?.assignmentCode ??""), style: TextStyle(fontSize: 12.0),),

                                  ],) ),


                            // Text("25", style: TextStyleOutput(fontSize: 15.0),),
                          ]),

                          TableRow(children: [
                            Padding(padding: EdgeInsets.all(5),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("EMPLOYEE NUMBER:", style: TextStyle(
                                      color: PdfColors.black,
                                      fontSize: 14,),),
                                    Text("_", style: TextStyle(fontSize: 12.0),),

                                  ],) ),
                            Padding(padding: EdgeInsets.all(5),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text("NI NUMBER:", style: TextStyle(
                                      color: PdfColors.black,
                                      fontSize: 14,),),
                                    Text("_", style: TextStyle(fontSize: 12.0),),

                                  ],) ),


                            // Text("25", style: TextStyleOutput(fontSize: 15.0),),
                          ]),
                        ],
                      ),
                    ),
                      flex: 6
                    ),

                  ]
              ),


              Padding(
                padding: const EdgeInsets.only(top: 13.0,),
                child: Table(
                  border: TableBorder.all(color: PdfColors.black,
                    width: 1.1,
                    // borderRadius: BorderRadius.only(
                    //   topLeft: Radius.circular(3.0),
                    //   bottomLeft: Radius.circular(3.0),
                    //   topRight: Radius.circular(3.0),
                    //   bottomRight: Radius.circular(3.0),
                    // )
                  ),
                  columnWidths: const {
                    0: FlexColumnWidth(4),
                    1: FlexColumnWidth(4),
                  },
                  children: [
                    TableRow(children: [

                      Padding(
                          padding: EdgeInsets.all(5),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [

                              Text("Employer Name:", style: TextStyle(
                                color: PdfColors.black,
                                fontSize: 14,),),
                              Text(payAdvice.agency?.name??"", style: TextStyle(fontSize: 12.0),),

                            ],) ),

                      Padding(padding: EdgeInsets.all(5),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text("ADDRESS:", style: TextStyle(
                                color: PdfColors.black,
                                fontSize: 14,),),
                              Text((payAdvice.agency?.address?.firstLine??"")
                                  + ", "+
                                  (payAdvice.agency?.address?.town??"")
                                  + ", "+
                                  (payAdvice.agency?.address?.county??"")
                                , style: TextStyle(fontSize: 12.0),),

                            ],) ),


                      // Text("25", style: TextStyleOutput(fontSize: 15.0),),
                    ]),

                  ],
                ),
              ),


            ],
          ),
        ),

        Padding(
            padding: const EdgeInsets.only(left: 20, top: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'PAY DATE: '+ (payAdvice.payAdviceDate ??''),
                  style: TextStyle(
                    color: PdfColors.black,
                    fontSize: 14,),
                )
              ],
            )),





        Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left:20.0,right: 5, top: 7),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 13.0, left: 0, right: 0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Container(
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 9.0),
                                  child: Text(
                                    "SHIFT ID",
                                    style: TextStyle(
                                        color: PdfColors.black,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                              flex: 1,
                            ),

                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.only(top: 9.0, left: 8),
                                child: Text(
                                  "DATE",textAlign: TextAlign.left,
                                  style: TextStyle(
                                      color: PdfColors.black,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                              flex: 2,
                            ),
                            Expanded(
                              child: Container(
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 9.0),
                                  child: Text(
                                    "HRS",textAlign: TextAlign.left,
                                    style: TextStyle(
                                        color: PdfColors.black,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                              flex: 1,
                            ),
                            Expanded(
                              child: Container(
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 9.0),
                                  child: Text(
                                    "TOTAL(£)",textAlign: TextAlign.left,
                                    style: TextStyle(
                                        color: PdfColors.black,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                              flex: 1,
                            ),
                          ],
                        ),
                      ),
                      payAdvice!=null&&payAdvice!.shifts!=null&&payAdvice!.shifts!.length>0?

                      Column(
                          children:

                          // List.generate(2, (int index) {
                          List.generate(payAdvice!.shifts!.length, (int index) {
                            return Padding(
                              padding: const EdgeInsets.only(left:0.0,right: 0),
                              child: Container(
                                color: index%2==0?PdfColor(0.9,0.9,0.9):PdfColors.white,
                                child: Padding(
                                  padding:
                                  const EdgeInsets.only( left: 5, right: 15,bottom: 10,top: 7),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Expanded(
                                        child:  Text(payAdvice!.shifts![index]!.shiftId.toString()!,

                                            style: TextStyle(
                                                color: PdfColors.black,
                                                fontSize: 12,
                                                fontWeight: FontWeight.normal)),

                                        flex: 1,
                                      ),
                                      Expanded(
                                        child:  Padding(
                                          padding: const EdgeInsets.only(left:5.0),
                                          child: Row(
                                            // mainAxisAlignment: MainAxisAlignment.center,
                                              children: [
                                                // SizedBox(width: 14,),
                                                Text(payAdvice!.shifts![index]!.startDate.toString()!,
                                                    // Text("",
                                                    textAlign: TextAlign.left,
                                                    style: TextStyle(
                                                        color: PdfColors.black,
                                                        fontSize: 12,
                                                        fontWeight: FontWeight.normal)),

                                              ]),
                                        ),

                                        flex: 2,
                                      ),
                                      Expanded(
                                        child:  Padding(
                                          padding: const EdgeInsets.only(left:5.0),
                                          child: Row(
                                            // mainAxisAlignment: MainAxisAlignment.center,
                                              children: [
                                                // SizedBox(width: 14,),
                                                Text(payAdvice!.shifts![index]!.numberOfHoursWorked?.toStringAsFixed(2)??'',
                                                    textAlign: TextAlign.left,
                                                    style: TextStyle(
                                                        color: PdfColors.black,
                                                        fontSize: 12,
                                                        fontWeight: FontWeight.normal)),

                                              ]),
                                        ),

                                        flex: 1,
                                      ),
                                      Expanded(
                                        child:  Padding(
                                          padding: const EdgeInsets.only(left:10.0),
                                          child: Row(
                                            // mainAxisAlignment: MainAxisAlignment.center,
                                              children: [
                                                // SizedBox(width: 14,),
                                                Text(payAdvice!.shifts![index]!.total?.toStringAsFixed(2)??'',
                                                    textAlign: TextAlign.left,
                                                    style: TextStyle(
                                                        color: PdfColors.black,
                                                        fontSize: 12,
                                                        fontWeight: FontWeight.normal)),

                                              ]),
                                        ),

                                        flex: 1,
                                      ),


                                    ],
                                  ),
                                ),
                              ),
                            );
                          })) : SizedBox(height: 10,),

                      Padding(
                        padding: const EdgeInsets.only( left: 10, right: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Container(
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 9.0),
                                  child: Text(
                                    "",
                                    style: TextStyle(
                                        color: PdfColors.black,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                              flex: 1,
                            ),

                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.only(top: 9.0, right: 10),
                                child: Text(
                                  "",textAlign: TextAlign.center,
                                  style: TextStyle(
                                      color: PdfColors.black,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                              flex: 2,
                            ),
                            Expanded(
                              child: Container(
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 9.0),
                                  child: Text(
                                    "GROSS PAY £",textAlign: TextAlign.left,
                                    style: TextStyle(
                                        color: PdfColors.black,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                              ),
                              flex: 1,
                            ),
                            Expanded(
                              child: Container(
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 9.0),
                                  child: Text(
                                    payAdvice.totalAmount.toString(),textAlign: TextAlign.left,
                                    style: TextStyle(
                                      color: PdfColors.black,
                                    ),
                                  ),
                                ),
                              ),
                              flex: 1,
                            ),
                          ],
                        ),
                      ),
                    ],),),
                flex:3
              ),
              Expanded(
                child: Column(children:[
                  Padding(
                    padding: const EdgeInsets.only(left:0.0,right: 20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 0.0, left: 0, right: 10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Container(
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 0),
                                    child: Text(
                                      "BANKING DETAILS",
                                      style: TextStyle(
                                          color: PdfColors.black,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold),
                                    ),
                                  ),
                                ),
                                flex: 4,
                              ),
                            ],
                          ),
                        ),
                        payAdvice!=null&&payAdvice!.payAdviceItemResult!=null&&payAdvice!.payAdviceItemResult!.length>0?

                        Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(left:0.0, right: 0 ),
                                child: Container(
                                  color: PdfColor(0.9,0.9,0.9),
                                  child: Padding(
                                    padding:
                                    const EdgeInsets.only( left: 5, right: 15,bottom: 10,top: 10),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Expanded(
                                          child:  Text('GROSS PAY £',
                                              style: TextStyle(
                                                  color: PdfColors.black,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.normal)),

                                          flex: 4,
                                        ),
                                        Expanded(
                                          child:  Text(payAdvice.workerGross??"",
                                              style: TextStyle(
                                                  color: PdfColors.black,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.normal)),

                                          flex: 4,
                                        ),

                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left:0.0, right: 0  ),
                                child: Container(
                                  color: PdfColor(0.9,0.9,0.9),
                                  child: Padding(
                                    padding:
                                    const EdgeInsets.only( left: 5, right: 15,bottom: 10,top: 10),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Expanded(
                                          child:  Text('OTHER PAY £',
                                              style: TextStyle(
                                                  color: PdfColors.black,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.normal)),

                                          flex: 4,
                                        ),
                                        Expanded(
                                          child:  Text("0.00",
                                              style: TextStyle(
                                                  color: PdfColors.black,
                                                  fontSize: 13,
                                                  fontWeight: FontWeight.normal)),

                                          flex: 4,
                                        ),

                                      ],
                                    ),
                                  ),
                                ),
                              )
                            ]

                        ) : SizedBox(height: 10,)
                      ],
                    ),
                  ),
                ]),
                flex:2
              ),
            ]
        ),

        Padding(
            padding: const EdgeInsets.only(left: 13.0, right: 13, top: 13),
            child: Container(
              height: 1.0,
              color: PdfColor(1,1,1),
            )),
        Padding(
            padding: const EdgeInsets.only(left: 20, top: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'SHIFT SUMMARY',
                  style: TextStyle(
                    color: PdfColors.black,
                    fontSize: 14,),
                )
              ],
            )),
        Padding(
          padding: const EdgeInsets.only(left:20.0,right: 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 13.0, left: 0, ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Container(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 9.0),
                          child: Text(
                            "ID",
                            style: TextStyle(
                                color: PdfColors.black,
                                fontSize: 12,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                      flex: 1,
                    ),

                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 9.0, right: 0),
                        child: Text(
                          "DATE",textAlign: TextAlign.left,
                          style: TextStyle(
                              color: PdfColors.black,
                              fontSize: 12,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                      flex: 1,
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 9.0, right: 0),
                        child: Text(
                          "DIRECTORATE",textAlign: TextAlign.left,
                          style: TextStyle(
                              color: PdfColors.black,
                              fontSize: 12,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                      flex: 2,
                    ),
                    Expanded(
                      child: Container(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 9.0),
                          child: Text(
                            "HRS",textAlign: TextAlign.left,
                            style: TextStyle(
                                color: PdfColors.black,
                                fontSize: 13,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                      flex: 1,
                    ),
                    Expanded(
                      child: Container(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 9.0),
                          child: Text(
                            "TOTAL(£)",textAlign: TextAlign.left,
                            style: TextStyle(
                                color: PdfColors.black,
                                fontSize: 12,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                      flex: 1,
                    ),
                  ],
                ),
              ),
              payAdvice!=null&&payAdvice!.payAdviceItemResult!=null&&payAdvice!.payAdviceItemResult!.length>0?

              Column(
                  children:

                  List.generate(payAdvice!.payAdviceItemResult?.length??0, (int index) {
                    // List.generate(payAdvice!.content!.length, (int index) {
                    return Padding(
                      padding: const EdgeInsets.only(left:0.0,right: 0),
                      child: Container(
                        color: index%2==0?PdfColor(0.9,0.9,0.9):PdfColors.white,
                        child: Padding(
                          padding:
                          const EdgeInsets.only( left: 0, right: 0,bottom: 10,top: 7),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [


                              Expanded(
                                child:  Text(payAdvice!.payAdviceItemResult![index]!.shiftId.toString(),
                                    style: TextStyle(
                                        color: PdfColors.black,
                                        fontSize: 12,
                                        fontWeight: FontWeight.normal)),

                                flex: 1,
                              ),
                              Expanded(
                                child:  Padding(
                                  padding: const EdgeInsets.only(left:0.0),
                                  child: Row(
                                      children: [
                                        // SizedBox(width: 14,),
                                        Text(payAdvice!.payAdviceItemResult![index]!.startDate.toString()!,
                                            textAlign: TextAlign.left,
                                            style: TextStyle(
                                                color: PdfColors.black,
                                                fontSize: 12,
                                                fontWeight: FontWeight.normal)),

                                      ]),
                                ),

                                flex: 1,
                              ),
                              Expanded(

                                child: Container(
                                  child: Text(payAdvice!.payAdviceItemResult![index]!.directorate.toString()!,
                                      textAlign: TextAlign.left,
                                      style: TextStyle(
                                          color: PdfColors.black,
                                          fontSize: 12,
                                          fontWeight: FontWeight.normal)
                                  ),),

                                flex: 2,
                              ),
                              Expanded(
                                child:  Padding(
                                  padding: const EdgeInsets.only(left:0.0),
                                  child: Row(
                                      children: [
                                        // SizedBox(width: 14,),
                                        Text(payAdvice!.payAdviceItemResult![index]!.numberOfHoursWorked?.toStringAsFixed(2)??"",
                                            textAlign: TextAlign.left,
                                            style: TextStyle(
                                                color: PdfColors.black,
                                                fontSize: 13,
                                                fontWeight: FontWeight.normal)),

                                      ]),
                                ),

                                flex: 1,
                              ),
                              Expanded(
                                child:  Padding(
                                  padding: const EdgeInsets.only(left:0.0),
                                  child: Row(
                                      children: [
                                        // SizedBox(width: 14,),
                                        Text(payAdvice!.payAdviceItemResult![index]!.total?.toStringAsFixed(2)??"",
                                            textAlign: TextAlign.left,
                                            style: TextStyle(
                                                color: PdfColors.black,
                                                fontSize: 12,
                                                fontWeight: FontWeight.normal)),

                                      ]),
                                ),

                                flex: 1,
                              ),


                            ],
                          ),
                        ),
                      ),
                    );
                  })) : SizedBox(height: 10,)
            ],),),
        Padding(
            padding: const EdgeInsets.only(left: 13.0, right: 13, top: 13),
            child: Container(
              height: 1.0,
              color: PdfColor(1,1,1),
            )),



      ],
    );
  }

}

String _formatCurrency(double amount) {
  return '${amount.toStringAsFixed(2)}';
}

String _formatDate(DateTime date) {
  final format = DateFormat.yMMMd('en_US');
  return format.format(date);
}

Future<String?> getDownloadPath2() async {
  Directory? directory;
  String directoryStr;
  try {
    if (Platform.isIOS ) {
      directory = await getApplicationDocumentsDirectory();
    } else if (Platform.isWindows) {
      directory = await getApplicationDocumentsDirectory();
      directoryStr =  "${directory.path}\\Invoices\\";
      directory = Directory(directoryStr);

    } else {
      // directory = Directory('/storage/emulated/0/Download/Invoices/');
      // Put file in global download folder, if for an unknown reason it didn't exist, we fallback
      // ignore: avoid_slow_async_io

      directory = await getExternalStorageDirectory();
    }
  } catch (err, stack) {
    print("Cannot get download folder path");
  }
  return directory?.path;
}