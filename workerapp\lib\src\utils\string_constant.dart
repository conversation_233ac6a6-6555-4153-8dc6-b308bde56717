import 'package:flutter/material.dart';

// final String Faq1 = "FAQs – Flexible worker";
final String Faq1 = "What can I do in MyWorkLink mobile app?";

final String Faq2 = "Can I update my availability?";
final String Faq3 = "Can I cancel a booked shift?";
final String Faq4 = "What happens when I ‘Apply’ for a shift?";
final String Faq5 = "What is Timesheet Release?";
final String Faq6 = "What if I have a query and don’t agree with the worked times?";
final String Faq7 = "Can I claim for expenses?";
final String Faq9 = "Can I use MyWorkLink app for more than one employment?";
final String Faq8 = "Can I manage my compliance info?";
final String Faq10 = "What if i have a query? ";
final String FaqA11 = " * View full shift details"
    "\n\n * Search for NEW shifts "
    "\n\n * Book or Apply into a shift"
    "\n\n * View shifts Waiting to be authorised (electronic signature on timesheet"
    "\n\n * Release timesheets for them to be processed for payment by your agency."
    "\n\n * Query timesheets"
    "\n\n * View pay advices (gross pay) and payslips (download option available)"
    "\n\n * Update personal contact information and profile"
    "\n\n * View your profile and registration information with your agencies including total worked shifts and hours"
;

final String FaqA1 = "In MyWorkLink the main actions you can do are";
final String FaqA2 = "Go to Profile > My Availability to specifically set days and times that you are available to work, under your profile. This helps clients and agencies to pre-book you into shifts before posting them publicly.";
final String FaqA2b = "Go to Profile > My Availability> Apply for leave days, set specific leave days and wait for approval from your manager.";

final String FaqA3 = "In cases where the system won't allow you to cancel, you will need to contact your employer directly or the client to make this change. Be aware - you need to give sufficient warning to the client that you are cancelling, or you will be liable to reprimand. The time limit for this varies depending on the client and employer.";
final String FaqA4 = "When you apply for a shift, you await approval, to be Booked into the shift by the Client or employer. They may review your profile before doing this. Once approved you will find this shift  in your Booked shifts list and must attend. You may apply for multiple shifts as long you are available on the dates, but can only be Booked into one shift per day";
final String FaqA8b = "You can manage your compliance info, RTW, HR Documents, Trainings and many other documents ";
final String FaqA8c = "You will get expiry dates notifications on your compliance information";


final String FaqA5 = "When you ‘Release’ a timesheet for payment, it is to allow the agency to process your payroll for payment of shifts that you have worked. Only timesheets that have been authorised by the client can be released for payment processing. In the first place the Timesheet needs to be Authorised by a member of staff where you worked the shift. You need to request authorisation from the client first.";

final String FaqA6 = "Once a shift is Authorised there is an option to Query a Timesheet if you don't agree with the shift details, e.g. the break time may have changed, or you did overtime, or less time and this hasn't been updated. You can Query a Timesheet before it has been paid. ";
final String FaqA61 = "NB. please check with your agency payroll for their terms and conditions e.g pay dates, cut off to dates to process payroll. ";
final String FaqA7 = "Once a shift is Authorised there is an option to Query a Timesheet if you don't agree with the shift details, e.g. the break time may have changed, or you did overtime, or less time and this hasn't been updated. You can Query a Timesheet before it has been paid. ";
final String FaqA71 = "ONLY query a timesheet if you have a dispute about times you worked. \nNB. Querying a timesheet means it will only be processed for payment once query has been resolved, so you may have delays in getting paid. Please check t’s & c’s with your agency on cut off dates and times for querying shifts.";
final String FaqA12 = " Querying a timesheet means it will only be processed for payment once query has been resolved, so you may have delays in getting paid. Please check t’s & c’s with your agency on cut off dates and times for querying shifts.";
final String FaqA8 = "Depending on your agency, you can claim expenses if they will agree to pay for them. Also note claiming expenses may delay payroll processing. ";
final String FaqA9 = "YES! MyWorkLink can be used by any one and you can have multiple agency employers and see all your details on one platform. Your agency/employer has to be registered with us. \nYou will earn £400 bonus if you successfully refer a new employment agency to register with us. ";
final String FaqA10 = "Always check for updates on MyWorkLink app from your agencies or clients.\nAlways check your booked shift times.";

final String Faq11 = "If you have further questions, please contact";


extension StringCasingExtension on String {
  String toCapitalized() => length > 0 ?'${this[0].toUpperCase()}${substring(1).toLowerCase()}':'';
  String toTitleCase() => replaceAll(RegExp(' +'), ' ').split(' ').map((str) => str.toCapitalized()).join(' ');
}