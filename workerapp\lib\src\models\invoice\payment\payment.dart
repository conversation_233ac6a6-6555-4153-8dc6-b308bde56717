
import 'package:freezed_annotation/freezed_annotation.dart';

import '../invoice.dart';

part 'payment.freezed.dart';
part 'payment.g.dart';

@freezed
abstract class Payment with _$Payment {
  factory Payment({

    int? id,
    String? ref,
    double? total,
    String? status,
    List<int>? paymentDate,


  }) = _Payment;

  factory Payment.fromJson(Map<String, dynamic> json) =>
      _$PaymentFromJson(json);

}
