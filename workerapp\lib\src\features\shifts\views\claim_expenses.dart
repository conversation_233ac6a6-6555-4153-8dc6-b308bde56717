import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:line_icons/line_icons.dart';
import 'package:ndialog/ndialog.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';

import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/utils/color_constants.dart';
import 'package:work_link/src/features/shifts/data/shift_category.dart';
import 'package:work_link/src/features/shifts/logic/shift_reason_input.dart';
import 'package:work_link/src/models/agency_expense_rate/AgencyExpenseRate.dart';
import 'package:work_link/src/models/shift/ShiftsPageResp.dart';
import 'package:work_link/src/models/shift_expense_claim/ShiftExpenseClaim.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../../models/shift/Shift.dart';
import '../../../utils/constants.dart';
import '../../../widgets/edit_text.dart';
import '../../../providers/data_repositories/ShiftAPIs.dart';
import '../../../utils/dialog_service.dart';
import '../../notificationns/logic/select_agency_input.dart';
import '../../notificationns/logic/select_agency_input_booking.dart';
import 'trailing_header_widget.dart';

final _shiftProvider =
AutoDisposeFutureProviderFamily<dynamic?, int>(
        (ref, status) {
      final _shift = ref.watch(shiftRepoProvider);
      return _shift.releaseShift(
          shiftId: status!
      );
    });

class ClaimExpenses extends StatelessWidget {


  ClaimExpenses({Key? key, required this.shift, required this.status, required this.claims})
      : super(key: key);

  final formKey = GlobalKey<FormBuilderState>();

  final Shift shift;
  final List<ShiftExpenseClaim> claims;
  final ShiftCategoryStatus status;

  Widget shiftRowDetail(
    BuildContext context,
    String title1,
    String? msg1,
    String title2,
    String? msg2, {
    Widget third = const SizedBox.shrink(),
  }) {
    return Padding(
      padding: const EdgeInsets.only(top: 15, left: 5, right: 5),
      child: Row(
        children: [
          Expanded(
            child: ShiftTextEntryField(
              ctx: context,
              title: title1,
              initialText: msg1 ?? '',
            ),
          ),
          Expanded(
            child: ShiftTextEntryField(
              ctx: context,
              title: title2,
              initialText: msg2 ?? '',
            ),
          ),
          third,
        ],
      ),
    );
  }

  Widget footerWidget(
      BuildContext context, ShiftCategoryStatus status, Shift shift) {
    var w;
    releaseCurrentShift() async {
      return await _shiftProvider(shift.id!);
    }

    Future releaseDialog(){
      return showDialog<bool?>(
          context: context,
          builder: (ctx) {
            return AlertDialog(
              contentPadding : const EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 0.0),
              content: Container(
                height: 200,
                child: Column(
                  children: [
                    Container(
                        height: 40,
                        width: double.infinity,
                        color: welcomeTextColor,
                        child: Padding(
                          padding: const EdgeInsets.only(top:10.0),
                          child: Text('RELEASE SHIFT',textAlign: TextAlign.center,style: TextStyle(fontSize: 14,color: Colors.white,fontWeight: FontWeight.w500),),
                        )),
                    Padding(
                        padding: const EdgeInsets.only(top:35.0,bottom: 30, left: 20, right: 20),
                        child:   Text('This action will release shift for payment. Do you wish to proceed?')),

                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [


                        ElevatedButton.icon(
                          label: Text('Yes'),
                          icon: Icon(Icons.done),

                          style: ElevatedButton.styleFrom(
                            backgroundColor: welcomeTextColor,
                          ),
                          onPressed: () async {
                            // TODO process


                            Navigator.pop(context, true);
                            final result = await ProgressDialog.future(
                              context,
                              dismissable: false,
                              future: releaseCurrentShift(),
                              message:
                              Text("Releasing shift..").textColor(textColor),
                              title: Text("Release Shift").textColor(textColor),
                              //backgroundColor: Colors.white70,
                              onProgressError: (err) {
                                print(err);

                                Navigator.pop(context);
                              },
                              onProgressFinish: (er) {
                                DialogService().showFloatingFlushbar(
                                  context: context,
                                  title: 'Released',
                                  message:
                                  'Shift has been released successfully.',
                                );},
                              onProgressCancel: () => Navigator.pop(context),
                            );

                          },
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left:15.0),
                          child: ElevatedButton.icon(
                            icon: Icon(Icons.cancel_outlined),
                            label: Text('No'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: denyRed,
                            ),
                            onPressed: () {
                              // TODO process
                              Navigator.pop(context, false);
                            },
                          ),
                        ),
                      ],)
                  ],
                ),
              ),

            );
          });
    }

    switch (status.status) {
      case 'CANCELLED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: () => routeBack(context),
                  icon: Icon(LineIcons.chevronLeft),
                  label: Text('Back'),
                ),
              ],
            );
          }),
        );
        break;

      case 'NEW':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Consumer(builder: (context1, watch, child) {
              final dialog =watch.watch(dialogProvider);
              final shiftRepo =watch.watch(shiftRepoProvider);
              final _profile =watch.watch(loginResponseProvider);

              bool tt = shift.requireApplicationByWorkers ?? false;
              String sType = tt ? 'Apply' : 'Book';
              String plural = '${sType}ing';
              String ed = '${sType}ed';

              return Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton.icon(
                    onPressed: () => routeBack(context),
                    icon: Icon(LineIcons.chevronLeft),
                    label: Text('Back'),
                  ),
                  SizedBox(width: sx(50)),
                  ElevatedButton.icon(
                    onPressed: () async {
                      // book a shift
                      if(tt){
                        selectAgencyInput(context, formKey, 'Apply', shift);

                      }else{
                        selectAgencyInputBooking(context, formKey, 'Book', shift);

                      }
                      // if(tt){
                      // }else{
                      //   final result = await ProgressDialog.future(
                      //     context,
                      //     dismissable: false,
                      //     future: tt
                      //         ? shiftRepo.applyAShift(
                      //       shiftId: shift.id!,
                      //       workerId: _profile.state!.workerId ?? 1,
                      //       agencyId: _profile.state!.agentId ?? 1,
                      //     )
                      //         : shiftRepo.bookAShift(
                      //       shiftId: shift.id!,
                      //       workerId: _profile.state!.workerId ?? 1,
                      //       agencyId: _profile.state!.agentId ?? 1,
                      //     ),
                      //     message: Text("$plural a shift..").textColor(textColor),
                      //     title: Text("$sType Shift").textColor(textColor),
                      //     //backgroundColor: Colors.white70,
                      //     onProgressError: (err) {
                      //       Navigator.pop(context);
                      //     },
                      //     onProgressCancel: () => Navigator.pop(context),
                      //   );
                      //
                      //   // check result
                      //
                      //   if (result is bool) {
                      //     // added ok
                      //     dialog.showFloatingFlushbar(
                      //       context: context,
                      //       title: '$sType Shift',
                      //       message: 'Shift has been $ed successfully.',
                      //     );
                      //   }
                      //
                      //   // err
                      //   else {
                      //     // added ok
                      //     dialog.showFloatingFlushbar(
                      //       context: context,
                      //       title: '$sType Shift',
                      //       message: result.message,
                      //       warning: true,
                      //     );
                      //   }
                      // }




                    },
                    icon: Icon(LineIcons.book),
                    label: Text('$sType Shift'),
                  ),
                ],
              );
            });
          }),
        );
        break;

      case 'AWAITING_AUTHORIZATION':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: () => routeBack(context),
                  icon: Icon(LineIcons.chevronLeft),
                  label: Text('Back'),
                ),
                SizedBox(width: sx(50)),
              ],
            );
          }),
        );
        break;

      case 'BOOKED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: () => routeBack(context),
                  icon: Icon(LineIcons.chevronLeft),
                  label: Text('Back'),
                ),
                SizedBox(width: sx(50)),
                ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.redAccent,
                  ),
                  onPressed: () =>
                      shiftReasonInput(context, formKey, 'Cancel', shift),
                  icon: Icon(LineIcons.ban),
                  label: Text('Cancel Shift'),
                ),
              ],
            );
          }),
        );
        break;

      case 'APPLIED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: () => routeBack(context),
                  icon: Icon(LineIcons.chevronLeft),
                  label: Text('Back'),
                ),
                SizedBox(width: sx(50)),
                ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.redAccent,
                  ),
                  onPressed: () =>
                      shiftReasonInput(context, formKey, 'Cancel', shift),
                  icon: Icon(LineIcons.ban),
                  label: Text('Cancel Shift'),
                ),
              ],
            );
          }),
        );
        break;

      case 'AUTHORIZED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 10, left: 10),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.all( Radius.circular(4),),
                        side: BorderSide(color: welcomeTextColor)
                    ),
                    backgroundColor:  Colors.white,
                  ),
                  onPressed: () => routeBack(context),
                  // icon: Icon(Icons.arrow_back_ios_new, color: welcomeTextColor,),
                  child: Text('Cancel', style: TextStyle(color: welcomeTextColor),),
                ),
                SizedBox(width: 5,),
                if(shift.released != true )

                  Consumer(
                    builder: (context1, watch, child) {
                      final dialog =watch.watch(dialogProvider);
                      final shiftRepo =watch.watch(shiftRepoProvider);
                      final _profile =watch.watch(loginResponseProvider);

                      return RelativeBuilder(builder: (context, height, width, sy, sx) {
                        return ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            elevation: 8,
                            backgroundColor: welcomeTextColor
                            // fixedSize: Size(width * 0.9, sy(40)),
                          ),
                          onPressed: () async {
                            // apply a shift
                            final result = await ProgressDialog.future(
                              context,
                              dismissable: false,
                              future: shiftRepo.releaseShift(
                                  shiftId: shift.id!
                              ),
                              message: Text("releasing a shift..")
                                  .textColor(textColor),
                              title: Text("Release Shift").textColor(textColor),
                              //backgroundColor: Colors.white70,
                              onProgressError: (err) {
                                print(err);

                                Navigator.pop(context);
                              },
                              onProgressCancel: () => Navigator.pop(context),
                            );

                            // check result
                            if (result is bool) {
                              // added ok
                              dialog.showFloatingFlushbar(
                                context: context,
                                title: 'Release Shift',
                                message:
                                'Shift has been released successfully.',
                              );
                              Navigator.pop(context);
                            }

                            // err
                            else {
                              // added ok
                              dialog.showFloatingFlushbar(
                                context: context,
                                title: 'Release Shift',
                                message: result.message,
                                warning: true,
                              );
                              Navigator.pop(context);
                            }
                          },
                          child: Row(children: [
                            Text(
                              'Release',
                              style: TextStyle(
                                color: Colors.white,
                                // fontSize: sx(16),
                              ),
                            ),
                            SizedBox(width: 5),
                            Icon(Icons.real_estate_agent_rounded)
                          ],),

                        );
                      });
                    },
                  )
              ],
            );
          }),
        );
        break;

      case 'IN_QUERY':
        w = RelativeBuilder(builder: (context, height, width, sy, sx) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: sy(10)),
              ShiftTextEntryField(
                ctx: context,
                title: 'QUERY',
                fieldHeight: 180,
                maxLines: 5,
                initialText: shift.queriedReason,
              ),
              Padding(
                padding: const EdgeInsets.only(bottom: 15, right: 20),
                child:
                    RelativeBuilder(builder: (context, height, width, sy, sx) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () => routeBack(context),
                        icon: Icon(LineIcons.chevronLeft),
                        label: Text('Back'),
                      ),
                    ],
                  );
                }),
              ),
            ],
          );
        });
        break;

      default:
        w = SizedBox.shrink();
    }

    return w;
  }

  Widget claimExpenses(
      BuildContext context, ShiftCategoryStatus status, Shift shift, List<ShiftExpenseClaim> claims) {
    return  DropdownButtonExample(shift, claims);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppAppBar(
        context,
        leading: IconButton(
          onPressed: () => routeBack(context),
          icon: Icon(
            Icons.chevron_left,
            color: welcomeTextColor,
            size: 35,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: RelativeBuilder(builder: (context, height, width, sy, sx) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: sy(10)),
              Center(
                child: Text(status.category)
                    .fontWeight(FontWeight.bold)
                    .fontSize(sx(23)),
              ),
              SizedBox(height: sy(10)),
              Container(
                color: welcomeTextColor,
                height: 50,
                width: double.infinity,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Text('Shift Details (Claim Expenses):', style: TextStyle(color: Colors.white),),
                      SizedBox(width: 8),
                      Text(shift.id.toString(), style: TextStyle(color: Colors.white)).fontWeight(FontWeight.bold  ),
                      Spacer(),
                      trailingHeaderWidget(status, shift),
                    ],
                  ),
                ),
              ),
              shiftRowDetail(
                context,
                'SHIFT DIRECTORATE',
                shift.directorate,
                'LOCATION NAME',
                shift.shiftLocation,
              ),
              shiftRowDetail(
                context,
                'PHONE NUMBER',
                shift.phoneNumber,
                'POSTCODE',
                shift.postCode,
              ),
              shiftRowDetail(
                context,
                'SHIFT TYPE',
                shift.shiftType,
                'SHIFT DATE',
                dateFormat.format(shift.start!),
                third: Expanded(
                  child: ShiftTextEntryField(
                    ctx: context,
                    title: 'SHIFT END',
                    initialText: (shift.end!=null?dateFormat.format(shift.end!):''),
                  ),
                ),
              ),
              shiftRowDetail(
                context,
                'START TIME',
                (shift.start!=null?timeFormat.format(shift.start!):''),
                'END TIME',
                (shift.end!=null?timeFormat.format(shift.end!):''),
                third: Expanded(
                  child: ShiftTextEntryField(
                    ctx: context,
                    title: 'BREAK TIME',
                    initialText: shift.breakTime?.toString()??'',
                  ),
                ),
              ),
              SizedBox(height: sy(20)),
              Padding(padding: EdgeInsets.symmetric(horizontal: 10),
                 child: Text("Expense Claims"),
              ),

              SizedBox(height: sy(10)),
              Padding(
                padding: EdgeInsets.only(right: 10, left: 10, top: 5),
                child:   claimExpenses(context, status, shift, claims),
              ),

              SizedBox(height: sy(10)),
              footerWidget(context, status, shift),
            ],
          );
        }),
      ),
    );
  }
}


class DropdownButtonExample extends StatefulWidget {
  DropdownButtonExample(this.shift, this.claims);

  final Shift shift;
  List<ShiftExpenseClaim> claims;

  @override
  State<DropdownButtonExample> createState() => _DropdownButtonExampleState();


}

class _DropdownButtonExampleState extends State<DropdownButtonExample> {

  List<AgencyExpenseRate>? agencyExpenseRates;
  AgencyExpenseRate? dropdownValue ;


  num units = 0;
  double rate = 0;

  List<ShiftExpenseClaim> claims= [];
  init() async {
    agencyExpenseRates = widget.shift.agencyId!=null?( await getAgencyExpenseRates(widget.shift.agencyId!)):[];
    claims = (await getShiftClaims([widget.shift.id!]))!;


    dropdownValue = agencyExpenseRates?.first??null;
    setState(() {
      agencyExpenseRates;
    });
  }

  @override
  void initState() {
    super.initState();
    init();
  }


  @override
  Widget build(BuildContext context) {
    var _addressController = TextEditingController();

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [

        claims!.length!=0?SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child:         Column(
            children:      List.generate(
              claims!.length,
                  (index) => claim(claims![index]),
            ),
          ),
        ): Text("No claims on this shift."),
        SizedBox(height: 10,),

        Padding(padding: EdgeInsets.symmetric(horizontal: 10),
          child: Text("Add Expense Claim"),
        ),
        SizedBox(height: (10)),
        Container(
          padding: EdgeInsets.symmetric(horizontal:9, vertical: 6),
          decoration: BoxDecoration(
            color: inputBgColor,
            borderRadius: BorderRadius.all(Radius.circular(5)),
          ),
          child:SizedBox(
            width: MediaQuery.of(context).size.width-40,
            child: DropdownButton<AgencyExpenseRate>(
              value: dropdownValue,
              icon: const Icon(Icons.arrow_downward),
              elevation: 16,
              dropdownColor: shiftFieldDetailsColor,
              focusColor: shiftFieldDetailsColor,
              underline: SizedBox(),
              iconEnabledColor: welcomeTextColor,
              onChanged: (AgencyExpenseRate? value) {
                // This is called when the user selects an item.
                setState(() {
                  dropdownValue = value!;
                });
              },
              items: agencyExpenseRates?.map<

                  DropdownMenuItem<AgencyExpenseRate>>((value) {
                return DropdownMenuItem(
                  value: value,
                  child: Text(value.expenseRate?.name!=null?(value.expenseRate!.name!+" (£"+(value.rate.toString())+"/"+(value.expenseRate!.unit!+")")):"Rates                  "),
                );
              }).toList(),


            ) ,
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            SizedBox(
              width: 150,
              child: TextField(
                // controller: _addressController = TextEditingController(),
                keyboardType: TextInputType.number,
                onChanged: (value){
                  try{
                    units = num.parse(value);
                  }catch(e){
                    units = 0;
                  }
                  rate = (dropdownValue?.rate??0)*units;
                  setState(() {
                    rate;
                  });
                },
                // maxLength: 30,
              ),
            ),
            SizedBox(width: 10,),
            Text("\£"+rate.toStringAsFixed(2), style: TextStyle(fontSize: 16),),
            SizedBox(width: 15,),
            ElevatedButton(
              child: Text('Claim'),

              style: ElevatedButton.styleFrom(
                backgroundColor: welcomeTextColor,
              ),
              onPressed: () async {
                // TODO process

                if(rate==null || rate<0.001){
                  DialogService().showFloatingFlushbar(
                    context: context,
                    title: 'Invalid claim',
                    message:
                    'Please enter a claim amount',
                      warning: true
                  );
                }else{
                  var data = {
                    "agencyExpenseRateId": dropdownValue?.id,
                    "amount": rate,
                    "description": dropdownValue?.expenseRate!.name!,
                    "rate": dropdownValue?.rate,
                    "shiftId": widget.shift.id,
                  };


                  await ProgressDialog.future(
                    context,
                    dismissable: false,
                    future: claimExpense(jsonEncode(data)),
                    message:
                    Text("Claiming expense..").textColor(textColor),
                    title: Text("Expense claim").textColor(textColor),
                    //backgroundColor: Colors.white70,
                    onProgressError: (err) {
                      print(err);

                      Navigator.pop(context);
                    },
                    onProgressFinish: (er) {
                      DialogService().showFloatingFlushbar(
                        context: context,
                        title: 'Claimed!',
                        message:
                        'Claim was successful',
                      );
                    },
                    onProgressCancel: () => Navigator.pop(context),
                  );

                   claims = (await getShiftClaims([widget.shift.id!]))!;

                  rate = 0;
                  setState(() {
                    claims;
                  });
                }

              },
            ),
          ],
        )
      ],
    );
  }

  claim(ShiftExpenseClaim claim){
    return
      Padding(
        padding: const EdgeInsets.only(top: 15, left: 10, right: 10),
        child: SizedBox(
          width: MediaQuery.of(context).size.width-40,
            child: Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: inputBgColor,
                borderRadius: BorderRadius.all(Radius.circular(5)),
              ),
              child:  Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(claim.agencyExpenseRate!.expenseRate!.name!+" ("+"\£"+claim.agencyExpenseRate!.rate.toString()+"/"+(claim.agencyExpenseRate?.expenseRate?.unit??"unit")+")  "),
                      Text(claim!.status!, style: TextStyle(),),
                    ],
                  ),
                  SizedBox(width: 30,),
                  Text("Amount: \£"+claim.amount.toString())
                ],),
        )),
      );
  }
}
