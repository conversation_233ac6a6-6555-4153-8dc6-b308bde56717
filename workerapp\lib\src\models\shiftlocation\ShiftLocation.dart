
import 'package:freezed_annotation/freezed_annotation.dart';

part 'ShiftLocation.freezed.dart';
part 'ShiftLocation.g.dart';

@freezed
abstract class ShiftLocation with _$ShiftLocation {
  factory ShiftLocation({

   int? id,
   String? name,
   String? postcode,
   String? phoneNumber,


  }) = _ShiftLocation;

  factory ShiftLocation.fromJson(Map<String, dynamic> json) =>
      _$ShiftLocationFromJson(json);
}
