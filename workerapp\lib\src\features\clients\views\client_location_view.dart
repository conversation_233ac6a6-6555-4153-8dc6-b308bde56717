import 'package:flutter/material.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:work_link/src/models/agencies-worker/address.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';
import 'package:styled_widget/styled_widget.dart';

class ClientLocationView extends StatelessWidget {
  const ClientLocationView({Key? key, required this.address}) : super(key: key);

  final Address address;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppAppBar(
          context,
          leading: IconButton(
            onPressed: () => routeBack(context),
            icon: Icon(
              Icons.chevron_left,
              color: welcomeTextColor,
              size: 35,
            ),
          ),
        ),
        body: RelativeBuilder(builder: (context, height, width, sy, sx) {
          return Padding(
            padding: const EdgeInsets.all(15.0),
            child: Column(
              children: [
                Text('Client Locations')
                    .fontWeight(FontWeight.w600)
                    .fontSize(sx(32)),
                Sized<PERSON>ox(height: sy(20)),
                ListTile(
                  tileColor: tileColor,
                  title: Text(
                          '${address.firstLine} ${address.secondLine}, ${address.county ?? ''}')
                      .fontWeight(FontWeight.w600),
                  subtitle: Text(address.town!),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }
}
