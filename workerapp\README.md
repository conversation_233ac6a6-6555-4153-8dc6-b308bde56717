# work_link

WorkLink Mobile app

## test creds
```json
{
  "username": "<EMAIL>",
  "password": "C2F4D18B"
}
```

## Getting Started

### Generate app icon
```bash
$ flutter clean
$ flutter pub get
$ flutter pub run flutter_launcher_icons:main
$ C:\flutter3.3.9\bin\flutter.bat pub run flutter_launcher_icons:main
```

### Change app id (package name)
replace `com.new.package.name` with your new app id
```bash
$ flutter clean
$ flutter pub get
$ flutter pub run change_app_package_name:main com.new.package.name
```

### Generate Freezed .g files

flutter pub run build_runner build --delete-conflicting-outputs
flutter pub run build_runner build web
C:\flutter3.3.9\bin\flutter.bat --no-color pub run build_runner build --delete-conflicting-outputs

C:\flutter3.3.9\bin\flutter.bat --no-color pub add flutter_localizations --sdk=flutter
C:\flutter3.3.9\bin\flutter.bat --no-color pub pub add intl:any



## Build for android store
flutter build appbundle --target-platform android-arm,android-arm64,android-x64
flutter build appbundle --target=lib/main.dart --target-platform android-arm,android-arm64,android-x64
C:\flutter3.3.9\bin\flutter.bat build appbundle --target=lib/main.dart --target-platform android-arm,android-arm64,android-x64


// Build without cache
C:\flutter3.3.9\bin\flutter.bat build web --pwa-strategy=none

