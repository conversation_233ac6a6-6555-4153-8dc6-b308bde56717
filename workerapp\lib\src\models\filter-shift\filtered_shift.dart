import 'dart:convert';

List<FilteredShift> filteredShiftFromJson(String str) =>
    List<FilteredShift>.from(
        json.decode(str).map((x) => FilteredShift.fromJson(x)));

String filteredShiftToJson(List<FilteredShift> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class FilteredShift {
  FilteredShift({
    this.id,
    this.shiftLocation,
    this.postCode,
    this.phoneNumber,
    this.directorate,
    this.shiftDate,
    this.shiftStartTime,
    this.shiftEndTime,
    this.breakTime,
    this.gender,
    this.shiftType,
    this.assignmentCode,
    this.notes,
    this.showNoteToFw,
    this.showNoteToAgency,
    this.requireApplicationByWorkers,
    this.hoursBeforeBroadcasting,
    this.shiftStatus,
    this.appliedStatus,
    this.agency,
    this.worker,
    this.client,
    this.cancelledReason,
    this.queriedReason,
    this.createdBy,
    this.appliedDate,
    this.bookedDate,
    this.authorizedDate,
    this.queriedDate,
    this.cancelledDate,
    this.agencies,
  });

  final int? id;
  final String? shiftLocation;
  final String? postCode;
  final String? phoneNumber;
  final String? directorate;
  final String? shiftDate;
  final String? shiftStartTime;
  final String? shiftEndTime;
  final String? breakTime;
  final String? gender;
  final String? shiftType;
  final String? assignmentCode;
  final String? notes;
  final bool? showNoteToFw;
  final bool? showNoteToAgency;
  final bool? requireApplicationByWorkers;
  final int? hoursBeforeBroadcasting;
  final String? shiftStatus;
  final String? appliedStatus;
  final String? agency;
  final String? worker;
  final String? client;
  final String? cancelledReason;
  final String? queriedReason;
  final String? createdBy;
  final String? appliedDate;
  final String? bookedDate;
  final String? authorizedDate;
  final String? queriedDate;
  final String? cancelledDate;
  final List<dynamic>? agencies;

  factory FilteredShift.fromJson(Map<String, dynamic> json) => FilteredShift(
        id: json["id"],
        shiftLocation: json["shiftLocation"],
        postCode: json["postCode"],
        phoneNumber: json["phoneNumber"],
        directorate: json["directorate"],
        shiftDate: json["shiftDate"],
        shiftStartTime: json["shiftStartTime"],
        shiftEndTime: json["shiftEndTime"],
        breakTime: json["breakTime"],
        gender: json["gender"],
        shiftType: json["shiftType"],
        assignmentCode: json["assignmentCode"],
        notes: json["notes"],
        showNoteToFw: json["showNoteToFw"],
        showNoteToAgency: json["showNoteToAgency"],
        requireApplicationByWorkers: json["requireApplicationByWorkers"],
        hoursBeforeBroadcasting: json["hoursBeforeBroadcasting"],
        shiftStatus: json["shiftStatus"],
        appliedStatus: json["appliedStatus"],
        agency: json["agency"],
        worker: json["worker"],
        client: json["client"],
        cancelledReason: json["cancelledReason"],
        queriedReason: json["queriedReason"],
        createdBy: json["createdBy"],
        appliedDate: json["appliedDate"],
        bookedDate: json["bookedDate"],
        authorizedDate: json["authorizedDate"],
        queriedDate: json["queriedDate"],
        cancelledDate: json["cancelledDate"],
        agencies: List<dynamic>.from(json["agencies"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "shiftLocation": shiftLocation,
        "postCode": postCode,
        "phoneNumber": phoneNumber,
        "directorate": directorate,
        "shiftDate": shiftDate,
        "shiftStartTime": shiftStartTime,
        "shiftEndTime": shiftEndTime,
        "breakTime": breakTime,
        "gender": gender,
        "shiftType": shiftType,
        "assignmentCode": assignmentCode,
        "notes": notes,
        "showNoteToFw": showNoteToFw,
        "showNoteToAgency": showNoteToAgency,
        "requireApplicationByWorkers": requireApplicationByWorkers,
        "hoursBeforeBroadcasting": hoursBeforeBroadcasting,
        "shiftStatus": shiftStatus,
        "appliedStatus": appliedStatus,
        "agency": agency,
        "worker": worker,
        "client": client,
        "cancelledReason": cancelledReason,
        "queriedReason": queriedReason,
        "createdBy": createdBy,
        "appliedDate": appliedDate,
        "bookedDate": bookedDate,
        "authorizedDate": authorizedDate,
        "queriedDate": queriedDate,
        "cancelledDate": cancelledDate,
        "agencies":
            agencies == null ? [] : List<dynamic>.from(agencies!.map((x) => x)),
      };
}
