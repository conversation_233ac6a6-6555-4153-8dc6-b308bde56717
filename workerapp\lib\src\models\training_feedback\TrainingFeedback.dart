
import 'package:freezed_annotation/freezed_annotation.dart';

import '../address/Address.dart';
import '../worker_training_session/WorkerTrainingSession.dart';

part 'TrainingFeedback.freezed.dart';
part 'TrainingFeedback.g.dart';

@freezed
abstract class TrainingFeedback with _$TrainingFeedback {
  factory TrainingFeedback({

    int? id,
    int? content,
    int? facility,
    int? trainer,
    int? method,
    bool? isRelevant,
    bool? skillsDev,
    String? comment,
    WorkerTrainingSession? booking,


  }) = _TrainingFeedback;

  factory TrainingFeedback.fromJson(Map<String, dynamic> json) =>
      _$TrainingFeedbackFromJson(json);
}
