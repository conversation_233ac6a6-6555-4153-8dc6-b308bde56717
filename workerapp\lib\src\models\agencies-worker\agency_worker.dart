import 'package:freezed_annotation/freezed_annotation.dart';

part 'agency_worker.freezed.dart';
part 'agency_worker.g.dart';

@freezed
abstract class AgencyWorker with _$AgencyWorker {
  factory AgencyWorker({
    String? name,
    int? id,
    String? service,
    String? status,
    String? createdBy,
    String? firstLine,
    String? county,
    String? email,
    String? billingEmail,
    String? telephone,
    String? logo,
    String? postCode,
  }) = _AgencyWorker;

  factory AgencyWorker.fromJson(Map<String, dynamic> json) =>
      _$AgencyWorkerFromJson(json);
}
