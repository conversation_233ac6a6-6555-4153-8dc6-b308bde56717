import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:line_icons/line_icons.dart';
import 'package:ndialog/ndialog.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/features/shifts/data/shift_category.dart';
import 'package:work_link/src/features/shifts/logic/shift_reason_input.dart';
import 'package:work_link/src/models/shift/ShiftsPageResp.dart';
import 'package:work_link/src/utils/color_constants.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import 'package:work_link/src/widgets/index.dart';
import '../../../models/shift/Shift.dart';
import '../../../utils/constants.dart';
import 'trailing_header_widget.dart';

class ViewTransport extends StatelessWidget {
  ViewTransport({Key? key, required this.shift, required this.status})
      : super(key: key);

  final formKey = GlobalKey<FormBuilderState>();

  final Shift shift;
  final ShiftCategoryStatus status;

  Widget shiftRowDetail(
    BuildContext context,
    String title1,
    String? msg1,
    String title2,
    String? msg2, {
        double? fheight,
    Widget third = const SizedBox.shrink(),
  }) {
    return Padding(
      padding: const EdgeInsets.only(top: 5, left: 5, right: 5),
      child: Row(
        children: [
          Expanded(
            child: ShiftTextEntryField(
              ctx: context,
              title: title1,
              initialText: msg1 ?? '',
              fieldHeight: fheight??40,
            ),
          ),
          Expanded(
            child: ShiftTextEntryField(
              ctx: context,
              title: title2,
              fieldHeight: fheight??40,
              initialText: msg2 ?? '',
            ),
          ),
          third,
        ],
      ),
    );
  }

  Widget footerWidget(
      BuildContext context, ShiftCategoryStatus status, Shift shift) {
    var w;

    switch (status.status) {
      case 'CANCELLED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // ElevatedButton(
                //   style: ElevatedButton.styleFrom(
                //     shape: RoundedRectangleBorder(
                //         borderRadius: BorderRadius.all( Radius.circular(4),),
                //         side: BorderSide(color: welcomeTextColor)
                //     ),
                //     backgroundColor:  Colors.white,
                //   ),
                //   onPressed: () => routeBack(context),
                //   child: Text('Back', style: TextStyle(color: welcomeTextColor),),
                // ),
              ],
            );
          }),
        );
        break;

      case 'NEW':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Consumer(builder: (context1, watch, child) {
              final dialog =watch.watch(dialogProvider);
              final shiftRepo =watch.watch(shiftRepoProvider);
              final _profile =watch.watch(loginResponseProvider);
              final transportRepo =watch.watch(transportRepoProvider);

              String sType ='Apply' ;
              String plural = '${sType}ing';
              String ed = '${sType}ed';

              return Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  SizedBox(width: sx(50)),
                  ElevatedButton.icon(
                    onPressed: () async {

                      final result = await ProgressDialog.future(
                        context,
                        dismissable: false,
                        future: transportRepo.applyATransport(shift.workerSpecId!, _profile?.workerId??0,),
                        message: Text("applying for a transport job...").textColor(textColor),
                        title: Text("Applying Transport").textColor(textColor),
                        onProgressFinish: (e){
                          dialog.showFloatingFlushbar(
                            context: context,
                            title: 'Transport Apply',
                            message: 'Transport has been applied successfully.',
                          );
                        },
                        onProgressError: (err) {
                          dialog.showFloatingFlushbar(
                            context: context,
                            title: 'Error Applying',
                            message: err.message,
                            warning: true,
                          );
                        },
                      );



                    },
                    icon: Icon(LineIcons.book),
                    label: Text('Apply for Job'),
                  ),
                ],
              );
            });
          }),
        );
        break;

      case 'AWAITING_AUTHORIZATION':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
              ],
            );
          }),
        );
        break;

      case 'BOOKED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: secondaryAccent,
                  ),
                  onPressed: () =>
                      shiftReasonInput(context, formKey, 'Cancel', shift),
                  icon: Icon(LineIcons.ban),
                  label: Text('Cancel ' +(shift.bookingType?.toCapitalized()??"Shift")),
                ),
              ],
            );
          }),
        );
        break;

      case 'APPLIED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: welcomeTextColor,
                  ),
                  onPressed: () =>
                      shiftReasonInput(context, formKey, 'Cancel', shift),
                  icon: Icon(LineIcons.ban),
                  label: Text('Cancel Shift'),
                ),
              ],
            );
          }),
        );
        break;

      case 'AUTHORIZED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if(shift.released != true && shift.shiftStatus != 'BILLED' && shift.bookingType?.toLowerCase()!='transport')ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: secondaryAccent,
                  ),
                  onPressed: () => shiftReasonInput(
                      context, formKey, 'Query', shift,
                      isQuery: true),
                  icon: Icon(LineIcons.ban),
                  label: Text('Query Shift'),
                ),
              ],
            );
          }),
        );
        break;

      case 'IN_QUERY':
        w = RelativeBuilder(builder: (context, height, width, sy, sx) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: sy(10)),
              ShiftTextEntryField(
                ctx: context,
                title: 'QUERY',
                fieldHeight: 180,
                maxLines: 5,
                initialText: shift.queriedReason,
              ),
              Padding(
                padding: const EdgeInsets.only(bottom: 15, right: 20),
                child:
                    RelativeBuilder(builder: (context, height, width, sy, sx) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                    ],
                  );
                }),
              ),
            ],
          );
        });
        break;

      default:
        w = SizedBox.shrink();
    }

    return w;
  }

  @override
  Widget build(BuildContext context) {
    print(shift.toJson());
    return Scaffold(
      appBar: AppBarDefault(
        context,"Secure Transport",
        leading: IconButton(
          onPressed: () => routeBack(context),
          icon: Icon(
            Icons.chevron_left,
            color: welcomeTextColor,
            size: 35,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: RelativeBuilder(builder: (context, height, width, sy, sx) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: sy(10)),
              Center(
                child: Text(status.category)
                    .fontWeight(FontWeight.bold)
                    .fontSize(sx(23)),
              ),
              SizedBox(height: sy(10)),
              Container(
                color: welcomeTextColor,
                height: 50,
                width: double.infinity,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Text('Job Card Details:', style: TextStyle(color: Colors.white),),
                              SizedBox(width: 8),
                              Text(shift.id?.toString()??'',  style: TextStyle(color: Colors.white)).fontWeight(FontWeight.bold),
                            ],
                          ),
                          // Spacer(),
                          trailingHeaderWidget(status, shift),
                        ],
                      )),
                ),
              ),

              shiftRowDetail(
                context,
                'Pick up location',
                  (shift.paddress??'\n')+
                    (shift.ppostCode??'')+'\n'+
                    (shift.pward??'')+'\n',
                'Destination',
                  (shift.daddress??'\n')+
                      (shift.dpostCode??'')+'\n'+
                      (shift.dward??'')+'\n',
                fheight: 80
              ),



              shiftRowDetail(
                context,
                'Pickup Date',
                dateFormat.format(shift.start!),
                'Pick up time',
                (shift.start!=null?timeFormat.format(shift.start!):''),

              ),


              shiftRowDetail(
                context,
                'Actual Start',
                shift.actualStart,
                'End Time',
                ((shift.end!=null?dateTimeFormat.format(shift.end!):'')),

              ),


              Padding(
                padding: EdgeInsets.only(bottom: sy(10), left: 5, right: 5),
                child:ShiftTextEntryField(
                ctx: context,
                title: 'Break Time Minutes',
                initialText: shift.breakTime?.toString()??'',
              ),),


              Container(
                color: welcomeTextColor,
                margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                // height: 50,
                width: double.infinity,
                child: Padding(
                  padding: const EdgeInsets.all(defaultPadding),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Text('Staff', style: TextStyle(color: Colors.white),),
                      SizedBox(width: 8),
                      Text(shift.id?.toString()??'',  style: TextStyle(color: Colors.white)).fontWeight(FontWeight.bold),
                      Spacer(),
                    ],
                  ),
                ),
              ),


              // shiftRowDetail(
              //   context,
              //     'Risk of restraint in and out of the vehicle',
              //   'LOW',
              //     'Is patient aware of transport',
              //    'Yes'
              // ),

              Padding(
                      padding: EdgeInsets.only(bottom: sy(10), left: 5, right: 5),
                      child: ShiftTextEntryField(
                        ctx: context,
                        title: 'Number of staff on team',
                        initialText: shift.numberOfStaff?.toString()??'',
                      ),
                    ) ,


              footerWidget(context, status, shift),
            ],
          );
        }),
      ),
    );
  }
}
