import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class ExpenseClaimedWidget extends StatefulWidget {
  @override
  ExpenseClaimedWidgetState createState() => ExpenseClaimedWidgetState();
}

class ExpenseClaimedWidgetState extends State<ExpenseClaimedWidget> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 13.0, left: 10, right: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 9.0),
                    child: Text(
                      "ID",maxLines: 1,overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 10,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  flex: 1,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 9.0),
                    child: Text(
                      "DATE",textAlign: TextAlign.start,maxLines: 1,overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 10,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  flex: 2,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 9.0),
                    child: Text(
                      "DESCRIPTION",textAlign: TextAlign.center,maxLines: 1,overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 10,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  flex: 2,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 9.0),
                    child: Text(
                      "UNITS",textAlign: TextAlign.center,maxLines: 1,overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 10,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  flex: 2,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 9.0),
                    child: Text(
                      "AMOUNT",maxLines: 1,overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 10,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  flex: 2,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 9.0),
                    child: Text(
                      "STATUS",maxLines: 1,overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 11,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  flex: 2,
                ),
              ],
            ),
          ),
          flex: 0,
        ),
        Expanded(
          child: Container(
            child: ListView(
              children: [
                Padding(
                  padding: const EdgeInsets.only(top:8.0),
                  child: Column(
                      children: List.generate(1, (int index) {
                    return Padding(
                      padding: const EdgeInsets.only(left:10.0,right: 10),
                      child: Container(
                        color: index%2==0?Color(0xffE6E6E6):Colors.white,
                        // child: Padding(
                        //   padding:
                        //       const EdgeInsets.only( left: 5, right: 15,bottom: 10),
                        //   child: Row(
                        //     mainAxisAlignment: MainAxisAlignment.start,
                        //     crossAxisAlignment: CrossAxisAlignment.start,
                        //     children: [
                        //       Expanded(
                        //         child: Padding(
                        //           padding: const EdgeInsets.only(top: 9.0),
                        //           child: Text(
                        //             "1", maxLines: 1,overflow: TextOverflow.ellipsis,
                        //             style: TextStyle(
                        //                 color: Colors.black,
                        //                 fontSize: 10,
                        //                 fontWeight: FontWeight.normal),
                        //           ),
                        //         ),
                        //         flex: 1,
                        //       ),
                        //       // Expanded(
                        //       //   child: Padding(
                        //       //     padding: const EdgeInsets.only(top: 9.0),
                        //       //     child: Text(
                        //       //       "02/07/2022",textAlign: TextAlign.start, maxLines: 1,overflow: TextOverflow.ellipsis,
                        //       //       style: TextStyle(
                        //       //           color: Colors.black,
                        //       //           fontSize: 10,
                        //       //           fontWeight: FontWeight.normal),
                        //       //     ),
                        //       //   ),
                        //       //   flex: 2,
                        //       // ),
                        //       Expanded(
                        //         child: Padding(
                        //           padding: const EdgeInsets.only(top: 9.0),
                        //           child: Text(
                        //             "Transport",textAlign: TextAlign.center,
                        //             maxLines: 1,overflow: TextOverflow.ellipsis,
                        //             style: TextStyle(
                        //                 color: Colors.black,
                        //                 fontSize: 10,
                        //                 fontWeight: FontWeight.normal),
                        //           ),
                        //         ),
                        //         flex: 2,
                        //       ),
                        //       Expanded(
                        //         child: Padding(
                        //           padding: const EdgeInsets.only(top: 9.0),
                        //           child: Text(
                        //             "25",textAlign: TextAlign.center,  maxLines: 1,overflow: TextOverflow.ellipsis,
                        //             style: TextStyle(
                        //                 color: Colors.black,
                        //                 fontSize: 10,
                        //                 fontWeight: FontWeight.normal),
                        //           ),
                        //         ),
                        //         flex: 2,
                        //       ),
                        //       Expanded(
                        //         child: Padding(
                        //           padding: const EdgeInsets.only(top: 9.0),
                        //           child: Text(
                        //             "50",textAlign: TextAlign.center,  maxLines: 1,overflow: TextOverflow.ellipsis,
                        //             style: TextStyle(
                        //                 color: Colors.black,
                        //                 fontSize: 10,
                        //                 fontWeight: FontWeight.normal),
                        //           ),
                        //         ),
                        //         flex: 2,
                        //       ),
                        //       Expanded(
                        //         child: Padding(
                        //           padding: const EdgeInsets.only(top: 9.0),
                        //           child: Text(
                        //             "Approved",textAlign: TextAlign.center,  maxLines: 1,overflow: TextOverflow.ellipsis,
                        //             style: TextStyle(
                        //                 color: Colors.black,
                        //                 fontSize: 11,
                        //                 fontWeight: FontWeight.normal),
                        //           ),
                        //         ),
                        //         flex: 2,
                        //       ),
                        //     ],
                        //   ),
                        // ),
                      ),
                    );
                  })),
                ),
              ],
            ),
          ),
          flex: 1,
        )
      ],
    );
  }
}
