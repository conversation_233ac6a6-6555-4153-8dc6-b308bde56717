import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:work_link/src/models/training_feedback/TrainingFeedback.dart';

import '../app_providers.dart';
import '../../models/worker_training_session/WorkerTrainingSession.dart';
import '../../models/worker_training_session/WorkerTrainingSessionPage.dart';
import '../../utils/constants.dart';

final workerTrainingSessionRepositoryProvider = Provider<WorkerTrainingSessionRepository>((ref) => WorkerTrainingSessionRepository(ref.read));

final trainingFeedBackProvider =  FutureProvider<WorkerTrainingSession?>((ref){
  return ref.read(workerTrainingSessionRepositoryProvider).getTrainingFeedbackForm();
});


class WorkerTrainingSessionRepository {
  Dio _dioClient;
  final _reader;

  WorkerTrainingSessionRepository(this._reader) : _dioClient = _reader(dioProvider);

  @override
  Future<WorkerTrainingSession> getTrainingFeedbackForm() async {
    Options? options = _reader(accessKeyOptionsProvider);
    final result = await _dioClient.get('$dataService/api/v1/training-booking/feedback', options: options);
    return WorkerTrainingSession.fromJson(result.data);
  }

  Future<bool> createTrainingFeedback(TrainingFeedback feedback) async {
    Options? options = _reader(accessKeyOptionsProvider);
    await _dioClient.post('$dataService/api/v1/training-feedback/${feedback.booking!.id}', data: feedback.toJson(),options: options);
    return true;
  }

  // Future<PaginatedWorkerTrainingSessions> searchWorkerTrainingSessions(String req) async {
  //
  //   if((kIsWeb||strictWeb)){
  //     int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();
  //
  //
  //     var result = await DioClient.instance.get( '$invoicerService/invoices/search/${activeBusinessId}/${req}/0/40' );
  //
  //     var res = result['content'] as List;
  //
  //     return PaginatedWorkerTrainingSessions(
  //       content: res.map((e) => WorkerTrainingSession.fromJson(e)).toList(),
  //       totalItems: result['totalElements'],
  //       offset: result['number']*result['size'],
  //       itemCount: result['numberOfElements'],
  //       pageNumber:result['number'],
  //     );
  //
  //   }
  //   else{
  //     try{
  //       ref.read(syncNotifierProvider.notifier).syncAll();
  //     }catch(e){
  //       print("Sync failed!");
  //       print(e.toString());
  //     }
  //     return searchDbWorkerTrainingSessions(req);
  //   }
  //
  // }
  //
  // @override
  // Future<WorkerTrainingSession> createWorkerTrainingSession(WorkerTrainingSession inv) async {
  //
  //
  //   if((kIsWeb||strictWeb)){
  //     var result = await DioClient.instance.post( '$invoicerService/invoice', data: inv.toJson());
  //
  //     return  WorkerTrainingSession.fromJson(result);
  //
  //   }
  //   else{
  //
  //     var savedWorkerTrainingSession  = await inv.dbSave();
  //
  //     try{
  //       ref.read(syncNotifierProvider.notifier).syncAll();
  //       return savedWorkerTrainingSession;
  //     }catch(e){
  //       print("Sync failed!");
  //       print(e.toString());
  //       return savedWorkerTrainingSession;
  //     }
  //
  //
  //   }
  //
  // }
  //
  //
  //
  // @override
  // Future<WorkerTrainingSession> getWorkerTrainingSession(int req) async {
  //
  //   if((kIsWeb||strictWeb)){
  //     var  result = await DioClient.instance.get( '$invoicerService/invoice/${req}' );
  //     return  WorkerTrainingSession.fromJson(result);
  //
  //
  //   }
  //   else{
  //     try{
  //       ref.read(syncNotifierProvider.notifier).syncAll();
  //     }catch(e){
  //       print("Sync failed!");
  //       print(e.toString());
  //     }
  //
  //     var res =  getDbWorkerTrainingSession(req);
  //     return res;
  //   }
  //
  // }
  //
  // Future<WorkerTrainingSession> getNewWorkerTrainingSession(String type) async {
  //   WorkerTrainingSession invoice = new WorkerTrainingSession();
  //
  //   if(type=='quote'){
  //     invoice.invoiceType = 'QUOTATION';
  //   }
  //
  //   DateFormat dateFormat = DateFormat("yyyy-MM-dd");
  //
  //
  //   invoice.currency =   '\$';
  //
  //   var prefs = await SharedPreferences.getInstance();
  //
  //
  //
  //
  //
  //
  //   var activeClient = await prefs!.getInt(UserPreference.activeClient);
  //   if(activeClient !=null) {
  //     invoice.client = await ref.read(clientsRepositoryProvider).getClient(activeClient);
  //   }else{
  //     invoice.client = Client();
  //   }
  //
  //   invoice.invoiceStatus = 'DRAFT';
  //
  //   invoice.invoiceDate = dateFormat.format(DateTime.now());
  //   invoice.dueDate = dateFormat.format(DateTime.now().add(const Duration(days: 7))) ;
  //
  //   if(invoice.invoiceItems == null) {
  //     invoice.invoiceItems = [WorkerTrainingSessionItem()];
  //   }
  //   if(invoice.payments == null) {
  //     invoice.payments= [Payment()];
  //   }
  //
  //   if(invoice.business == null) {
  //     var activeBusiness = await prefs!.getInt(UserPreference.activeBusiness);
  //
  //     if(activeBusiness!=null)invoice.business= await ref.read(businessesRepositoryProvider).getBusiness(activeBusiness);
  //   }
  //
  //
  //   invoice.vatPercent = invoice.business?.tax;
  //
  //
  //   var activeCurrency = "USD";
  //
  //   activeCurrency = (await prefs!.getString(UserPreference.activeCurrency))??"USD";
  //
  //   var currencySymbol = "\$";
  //
  //   if(invoice.client?.currency!=null){
  //     activeCurrency = invoice.client!.currency!;
  //   }
  //
  //
  //
  //   if(invoice.currency==null)invoice.currency = activeCurrency;
  //   if(invoice.currencySymbol==null)invoice.currencySymbol = currencySymbol;
  //
  //
  //
  //   return invoice;
  //
  // }


}
