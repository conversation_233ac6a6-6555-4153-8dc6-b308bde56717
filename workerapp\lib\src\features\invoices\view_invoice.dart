
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:work_link/src/utils/colors.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';
import '../../models/invoice/invoice.dart';
import '../../providers/data_repositories/invoice_repository.dart';
import '../../utils/color_constants.dart';
import '../../models/custom_exception.dart';
import '../../models/payadvice/payadvice_response.dart';
import '../../widgets/appbar_default.dart';




class ViewInvoice extends ConsumerWidget {
  final int invoiceId;

  PayAdviceResponse? payAdvice;
  SharedPreferences? prefs;

  ViewInvoice(this.invoiceId);


  @override
  Widget build(BuildContext context,   watch) {

    return  Scaffold(
        appBar: AppBarDefault(context, "Invoice",
            leading: Container(
              height: 5,
              width: 30,
              child: Padding(
                padding: const EdgeInsets.only(left: 15.0, right: 15),
                child: InkWell(
                  child: Icon(
                    Icons.arrow_back,
                    color: welcomeTextColor,
                    size: 20,
                  ),
                  onTap: () {
                    routeBack(context);
                  },
                ),
              ),
            )),
        body: SafeArea(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: defaultPadding),
              width: double.infinity,
              child:         watch.watch(invoiceProvider(invoiceId)).when(
                data: (Invoice? invoice) {
                  double totalPayments = 0;
                  invoice!.paymentRef?.forEach((e) {
                    totalPayments+=e.total!;
                  });
                  if (invoice != null) {

                    if (invoice == null) {
                      return Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            'failed to get invoice',
                            style: TextStyle(
                              fontStyle: FontStyle.italic,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      );
                    }

                    return
                    SingleChildScrollView( child:Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                            padding: const EdgeInsets.only(right: 13, top: 20),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      width: 65,
                                      height: 65,
                                      child: Padding(padding: EdgeInsets.all(0),
                                        child: invoice.payee?.logo != null
                                            ?Image.network(invoice.payee!.logo!)
                                            :Image.asset('assets/images/logo.png',
                                          // height: kToolbarHeight * 0.7,
                                        ),),
                                    ),
                                    Text(
                                       invoice.payee?.name??"",
                                    ),
                                    Text(
                                      (invoice.payee?.address?.firstLine??"")+" \n"+
                                      // (invoice.payee?.address?.secondLine??"")+" \n"+
                                      (invoice.payee?.address?.town??"")+" \n"+
                                      // (invoice.payee?.address?.county??"")+" \n"+
                                      (invoice.payee?.address?.postcode??"")

                                    ),
                                  ],

                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Padding(
                                        padding:  const EdgeInsets.only(top: 15, bottom: 15),
                                      child:Container(
                                        color: invoice.invoiceStatus=="UNPAID"?denyRed:Colors.green,
                                        padding:  const EdgeInsets.all(10),
                                        child: Text(
                                          invoice.invoiceStatus??"UNPAID",
                                          style: TextStyle(
                                              color: Colors.white,
                                            fontSize: 14
                                          ),
                                        ),
                                      )
                                    ),

                                    Text(
                                      (invoice.payer?.firstname??"")+" "+ (invoice.payer?.lastname??""),
                                    ),
                                    Text(
                                         "\n"+
                                         " \n"

                                    ),
                                  ],

                                ),
                              ],
                            )),


                        Padding(
                            padding:  const EdgeInsets.only(top: 48),
                            child:Container(

                              padding:  const EdgeInsets.all(5),
                              decoration: BoxDecoration(
                                color: accentPrimaryColor,
                                borderRadius: BorderRadius.all(
                                   Radius.circular(smallBorderRadius),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'INVOICE #',
                                        style: TextStyle(
                                            color: welcomeTextColor,
                                          fontWeight: FontWeight.bold
                                        ),
                                      ),
                                      SizedBox(height: 3,),
                                      Text(
                                        invoice!.id.toString(),
                                        style: TextStyle(
                                        ),
                                      ),
                                    ],
                                  ),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'ISSUE DATE',
                                        style: TextStyle(
                                            color: welcomeTextColor,
                                          fontWeight: FontWeight.bold
                                        ),
                                      ),
                                      SizedBox(height: 3,),
                                      Text(
                                        invoice.invoiceDate??"",
                                        style: TextStyle(
                                        ),
                                      ),
                                    ],
                                  ),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'DUE DATE',
                                        style: TextStyle(
                                            color: welcomeTextColor,
                                          fontWeight: FontWeight.bold
                                        ),
                                      ),
                                      SizedBox(height: 5,),
                                      Text(
                                        invoice.dueDate??"",
                                        style: TextStyle(
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            )
                        ),



                        //Invoice Items
                        Padding(
                          padding: const EdgeInsets.symmetric(),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox( height: 20,),
                              Container(
                                padding:  const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: welcomeTextColor,
                                  borderRadius: BorderRadius.only(
                                    topRight:  Radius.circular(defaultBorderRadius),
                                    topLeft:  Radius.circular(defaultBorderRadius),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Text(
                                        "DESCRIPTION",
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      flex: 4,
                                    ),
                                    Expanded(
                                      child: Text(
                                        "TOTAL (£)",
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      flex: 1,
                                    ),

                                  ],
                                ),
                              ),



                              if(invoice!=null&&invoice!.invoiceItemResult!=null&&invoice!.invoiceItemResult!.length>0)

                              Column(
                                  children:

                                    List.generate(invoice!.invoiceItemResult!.length, (int index) {
                                    return Padding(
                                      padding: const EdgeInsets.symmetric(),
                                      child: Container(
                                        color: index%2==0?Color(0xffE6E6E6):Colors.white,
                                        child: Padding(
                                          padding:
                                          const EdgeInsets.symmetric(horizontal:10, vertical: 5),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            crossAxisAlignment: CrossAxisAlignment.center,
                                            children: [
                                              Expanded(
                                                child:  Text(invoice!.invoiceItemResult![index]!.description.toString()!,
                                                    style: TextStyle(
                                                        color: Colors.black,
                                                        fontSize: 13,
                                                        fontWeight: FontWeight.normal)),

                                                flex: 4,
                                              ),
                                              Expanded(
                                                child:  Text(invoice!.invoiceItemResult![index]!.total?.toStringAsFixed(2)??'',
                                                    textAlign: TextAlign.start,
                                                    style: TextStyle(
                                                        color: Colors.black,
                                                        fontSize: 13,
                                                        fontWeight: FontWeight.normal)),

                                                flex: 1,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    );
                                  })),


                              Padding(
                                  padding: const EdgeInsets.only( top: 10),
                                  child: Container(
                                    height: 1.0,
                                    color: Color(0xffA2A2A2),
                                  )),


                              Padding(
                                padding: const EdgeInsets.only( left: 10, right: 10),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Container(
                                        child: Padding(
                                          padding: const EdgeInsets.only(top: 9.0),
                                          child: Text(
                                            "",
                                            style: TextStyle(
                                                color: Colors.black,
                                                fontSize: 13,
                                                fontWeight: FontWeight.bold),
                                          ),
                                        ),
                                      ),
                                      flex: 2,
                                    ),
                                    Expanded(
                                      child: Container(
                                        child: Padding(
                                          padding: const EdgeInsets.only(right: 5.0, top: 10),
                                          child: Text(
                                            "Total",textAlign: TextAlign.end,
                                            style: TextStyle(
                                                color: Colors.black,
                                                fontSize: 13,
                                                fontWeight: FontWeight.bold),
                                          ),
                                        ),
                                      ),
                                      flex: 3,
                                    ),
                                    Expanded(
                                      child: Container(
                                        child: Padding(
                                          padding: const EdgeInsets.only(top: 9.0),
                                          child: Column(
                                              children:[

                                                Text(
                                            invoice.invoiceItemResult?[0].total.toString()??"0",textAlign: TextAlign.start,
                                            style: TextStyle(
                                              color: Colors.black,
                                            ),
                                          ),
                                                Padding(
                                                    padding: const EdgeInsets.only( top: 10),
                                                    child: Container(
                                                      height: 1.0,
                                                      color: Color(0xffA2A2A2),
                                                    )),
                                                Padding(
                                                    padding: const EdgeInsets.only( top: 2),
                                                    child: Container(
                                                      height: 1.0,
                                                      color: Color(0xffA2A2A2),
                                                    )),

                                              ])
                                        ),
                                      ),
                                      flex: 2,
                                    ),
                                  ],
                                ),
                              ),
                            ],),),

                        //Payments
                        Container(

                          width: 250,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox( height: 20,),
                              Text("Payments"),
                              SizedBox( height: 5,),
                              Container(
                                padding:  const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: welcomeTextColor,
                                  borderRadius: BorderRadius.only(
                                    topRight:  Radius.circular(defaultBorderRadius),
                                    topLeft:  Radius.circular(defaultBorderRadius),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Text(
                                        "Ref",
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      flex: 4,
                                    ),
                                    Expanded(
                                      child: Text(
                                        "Amount (£)",
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      flex: 2,
                                    ),

                                  ],
                                ),
                              ),



                              (invoice!=null&&invoice!.paymentRef!=null&&invoice!.paymentRef!.length>0)?

                              Column(
                                  children:

                                    List.generate(invoice!.paymentRef!.length, (int index) {
                                    return Padding(
                                      padding: const EdgeInsets.symmetric(),
                                      child: Container(
                                        color: index%2==0?Color(0xffE6E6E6):Colors.white,
                                        child: Padding(
                                          padding:
                                          const EdgeInsets.symmetric(horizontal:10, vertical: 5),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            crossAxisAlignment: CrossAxisAlignment.center,
                                            children: [
                                              Expanded(
                                                child:  Text(invoice!.paymentRef![index]!.ref.toString()!,
                                                    style: TextStyle(
                                                        color: Colors.black,
                                                        fontSize: 13,
                                                        fontWeight: FontWeight.normal)),

                                                flex: 4,
                                              ),
                                              Expanded(
                                                child:  Text(invoice!.paymentRef![index]!.total?.toStringAsFixed(2)??'',
                                                    textAlign: TextAlign.start,
                                                    style: TextStyle(
                                                        color: Colors.black,
                                                        fontSize: 13,
                                                        fontWeight: FontWeight.normal)),

                                                flex: 2,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    );
                                  })):
                              Center(child: Padding(
                                padding: EdgeInsets.all(10),
                                child: Text("No related payments."),
                              ),),


                              Padding(
                                  padding: const EdgeInsets.only( top: 10),
                                  child: Container(
                                    height: 1.0,
                                    color: Color(0xffA2A2A2),
                                  )),


                              Padding(
                                padding: const EdgeInsets.only( left: 10, right: 10),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Container(
                                        child: Padding(
                                          padding: const EdgeInsets.only(top: 9.0),
                                          child: Text(
                                            "",
                                            style: TextStyle(
                                                color: Colors.black,
                                                fontSize: 13,
                                                fontWeight: FontWeight.bold),
                                          ),
                                        ),
                                      ),
                                      flex: 2,
                                    ),
                                    Expanded(
                                      child: Container(
                                        child: Padding(
                                          padding: const EdgeInsets.only(right: 5.0, top: 10),
                                          child: Text(
                                            "Total Due",textAlign: TextAlign.end,
                                            style: TextStyle(
                                                color: Colors.black,
                                                fontSize: 13,
                                                fontWeight: FontWeight.bold),
                                          ),
                                        ),
                                      ),
                                      flex: 3,
                                    ),
                                    Expanded(
                                      child: Container(
                                        child: Padding(
                                          padding: const EdgeInsets.only(top: 9.0),
                                          child: Column(
                                              children:[

                                                Text(
                                            ((invoice.invoiceItemResult?[0].total??0)-totalPayments).toString()??"0",textAlign: TextAlign.start,
                                            style: TextStyle(
                                              color: Colors.black,
                                            ),
                                          ),
                                                Padding(
                                                    padding: const EdgeInsets.only( top: 10),
                                                    child: Container(
                                                      height: 1.0,
                                                      color: Color(0xffA2A2A2),
                                                    )),
                                                Padding(
                                                    padding: const EdgeInsets.only( top: 2),
                                                    child: Container(
                                                      height: 1.0,
                                                      color: Color(0xffA2A2A2),
                                                    )),

                                              ])
                                        ),
                                      ),
                                      flex: 2,
                                    ),
                                  ],
                                ),
                              ),
                            ],),),


                        SizedBox(height: 30,),


                        // Bank Details

                        Container(
                          width: 250,
                          child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(  ),
                                  child: Container(
                                    padding: const EdgeInsets.all(5),
                                    decoration: BoxDecoration(
                                      color: welcomeTextColor,
                                      borderRadius: BorderRadius.only(
                                        topRight:  Radius.circular(smallBorderRadius),
                                        topLeft:  Radius.circular(smallBorderRadius),
                                      ),
                                    ),
                                    child: Padding(
                                      padding:
                                      const EdgeInsets.only( ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child:  Text('BANKING DETAILS',
                                                style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.bold)),

                                            flex: 6,
                                          ),

                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(   ),
                                  child: Container(
                                    color: Colors.white,
                                    child: Padding(
                                      padding:const EdgeInsets.all(5),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child:  Text('Bank Name',
                                                style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.normal)),

                                            flex: 4,
                                          ), Expanded(
                                            child:  Text("",
                                                style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.normal)),

                                            flex: 4,
                                          ),

                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(   ),
                                  child: Container(
                                    color: Color(0xffE6E6E6),
                                    child: Padding(
                                      padding: const EdgeInsets.all(5),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child:  Text('Acc No.',
                                                style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.normal)),

                                            flex: 4,
                                          ),
                                          Expanded(
                                            child:  Text(" ",
                                                style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.normal)),

                                            flex: 4,
                                          ),

                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only( ),
                                  child: Container(
                                    color: Colors.white,
                                    child: Padding(
                                      padding:
                                      const EdgeInsets.all(5),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child:  Text('Sort Code',
                                                style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.normal)),

                                            flex: 4,
                                          ),
                                          Expanded(
                                            child:  Text(" ",
                                                style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.normal)),

                                            flex: 4,
                                          ),

                                        ],
                                      ),
                                    ),
                                  ),
                                )
                              ]

                          ),
                        ),
                        // Bank Details Ends


                        SizedBox(height: 20,),

                        //footer
                        Row(
                          children: [
                            Icon(
                              Icons.info,
                              color: welcomeTextColor,
                            ),
                            Expanded(child:
                            Text("Once payment is done, notify your trainer to complete booking."
                                " You can still view your invoice from the dashboard under Invoices",
                              overflow: TextOverflow.visible,
                              style: TextStyle(fontSize:12 ),
                            ),
                            )

                            // Text("Invoices", style: TextStyle(fontWeight: FontWeight.bold))
                          ],
                        )
                        //footer ends


                      ],
                    ));
                  }
                  // err
                  else {
                    return Center(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          'failed to get pay advice',
                          style: TextStyle(
                            fontStyle: FontStyle.italic,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    );
                  }
                },
                loading: () => Center(
                  child: CircularProgressIndicator(),
                ),
                error: (e, st) => Center(
                  child: ErrorPage(
                    error: e is CustomException ? e.message : e.toString(),
                    stackTrace: st,
                    onTryAgain: () => watch.refresh(invoiceProvider(invoiceId)),
                  ),
                ),
              ),
            )),
      );
  }
}


