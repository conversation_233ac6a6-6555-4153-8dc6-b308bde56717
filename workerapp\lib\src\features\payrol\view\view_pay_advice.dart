
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/utils/colors.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../../utils/color_constants.dart';
import '../../../models/custom_exception.dart';
import '../../../models/payadvice/payadvice_response.dart';
import '../print/app.dart';



final _payslipProvider =
AutoDisposeFutureProviderFamily<PayAdviceResponse?, int>(
        (ref, payAdviceId) {
      final _payslip = ref.watch(payslipRepoProvider);

      return _payslip.getPayAdvice(payAdviceId);
    });


// class ViewPayAdvice extends StatefulWidget {
//   const ViewPayAdvice({Key? key, required this.payAdviceId}) : super(key: key);
//
//   @override
//   ViewPayAdviceState createState() => ViewPayAdviceState(payAdviceId);
//   final int payAdviceId;
// }

class ViewPayAdvice extends ConsumerWidget {
  final int payAdviceId;

  PayAdviceResponse? payAdvice;
  SharedPreferences? prefs;

  ViewPayAdvice(this.payAdviceId);


  @override
  Widget build(BuildContext context,   watch) {
    final monthLater =  DateTime.now().add(Duration(days: 30));
    final today =  DateTime.now();
    double width = MediaQuery.of(context).size.width;

    return Scaffold(
      appBar: AppBarDefault(context,"Pay Advice",
          leading: Container(
            height: 30,
            width: 30,
            child: Padding(
              padding: const EdgeInsets.only(left: 15.0, right: 15),
              child: InkWell(
                child: Icon(   Icons.arrow_back,
                  color: welcomeTextColor,
                  size: 20,
                ),
                onTap: () {
                  routeBack(context);
                },
              ),
            ),
          )),
      body: SafeArea(
          child: Container(
            width: double.infinity,
            child:        watch.watch(_payslipProvider(payAdviceId)).when(
              data: (PayAdviceResponse? value) {
                if (value != null) {
                  final payAdvice = value;
                  print("This is the pay advice:"+ payAdvice.payAdviceDate.toString());

                  if (payAdvice == null) {
                    return Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          'failed to get pay advice',
                          style: TextStyle(
                            fontStyle: FontStyle.italic,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    );
                  }

                  return payAdvice == null
                      ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        'pay advice not found',
                        style: TextStyle(
                          fontStyle: FontStyle.italic,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  )
                      :
                  SingleChildScrollView( child:Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                          padding: const EdgeInsets.only(right: 13, top: 20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'Pay Advice: '+payAdvice.id.toString(),
                                style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 22,
                                    fontWeight: FontWeight.bold),
                              )
                            ],
                          )),



                      Padding(
                        padding: const EdgeInsets.only(left:20.0,right: 20),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [

                            Padding(
                              padding: const EdgeInsets.only(top: 20,),
                              child: Table(
                                border: TableBorder.all(color: Colors.black,
                                    width: 1.1,
                                    borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(3.0),
                                      bottomLeft: Radius.circular(3.0),
                                      topRight: Radius.circular(3.0),
                                      bottomRight: Radius.circular(3.0),
                                    )),
                                columnWidths: const {
                                  0: FlexColumnWidth(4),
                                },
                                children: [
                                  TableRow(children: [
                                    SizedBox(
                                      width: 150,
                                      height: 150,
                                      child: Padding(padding: EdgeInsets.all(5),
                                        child: payAdvice.agency?.logo != null
                                            ?Image.network(payAdvice.agency!.logo!)
                                            :Image.asset('assets/images/logo.png',
                                          // height: kToolbarHeight * 0.7,
                                        ),),
                                    )



                                    // Text("25", style: TextStyleOutput(fontSize: 15.0),),
                                  ]),
                                ],
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 13.0,),
                              child: Table(
                                border: TableBorder.all(color: Colors.black,
                                    width: 1.1,
                                    borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(3.0),
                                      bottomLeft: Radius.circular(3.0),
                                      topRight: Radius.circular(3.0),
                                      bottomRight: Radius.circular(3.0),
                                    )),
                                columnWidths: const {
                                  0: FlexColumnWidth(4),
                                  1: FlexColumnWidth(4),
                                },
                                children: [
                                  TableRow(children: [

                                    Padding(
                                        padding: EdgeInsets.all(5),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [

                                            Text("Employer Name:", style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 16,),),
                                            Text(payAdvice.agency?.name??"", style: TextStyle(fontSize: 15.0),),

                                          ],) ),

                                    Padding(padding: EdgeInsets.all(5),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text("ADDRESS:", style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 16,),),
                                            Text((payAdvice.agency?.address?.firstLine??"")
                                                + ", "+
                                                (payAdvice.agency?.address?.town??"")
                                                + ", "+
                                                (payAdvice.agency?.address?.county??"")
                                              , style: TextStyle(fontSize: 15.0),),

                                          ],) ),


                                    // Text("25", style: TextStyleOutput(fontSize: 15.0),),
                                  ]),

                                ],
                              ),
                            ),

                            Padding(
                              padding: const EdgeInsets.only(top: 13.0,),
                              child: Table(
                                border: TableBorder.all(color: Colors.black,
                                    width: 1.1,
                                    borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(3.0),
                                      bottomLeft: Radius.circular(3.0),
                                      topRight: Radius.circular(3.0),
                                      bottomRight: Radius.circular(3.0),
                                    )),
                                columnWidths:  {
                                  0: FlexColumnWidth(4),
                                  1: FlexColumnWidth(4),
                                },
                                children: [
                                  TableRow(children: [
                                    Padding(padding: EdgeInsets.all(5),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text("EMPLOYEE NAME:", style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 16,),),
                                            Text((payAdvice.worker?.firstname ??"")+" "+(payAdvice.worker?.lastname ??""), style: TextStyle(fontSize: 15.0),),

                                          ],) ),
                                    Padding(padding: EdgeInsets.all(5),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text("JOB TITLE:", style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 16,),),
                                            Text((payAdvice.worker?.assignmentCode ??""), style: TextStyle(fontSize: 15.0),),

                                          ],) ),


                                    // Text("25", style: TextStyleOutput(fontSize: 15.0),),
                                  ]),

                                  TableRow(children: [
                                    Padding(padding: EdgeInsets.all(5),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text("EMPLOYEE NUMBER:", style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 16,),),
                                            Text("", style: TextStyle(fontSize: 15.0),),

                                          ],) ),
                                    Padding(padding: EdgeInsets.all(5),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text("NI NUMBER:", style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 16,),),
                                            Text("", style: TextStyle(fontSize: 15.0),),

                                          ],) ),


                                    // Text("25", style: TextStyleOutput(fontSize: 15.0),),
                                  ]),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      Padding(
                          padding: const EdgeInsets.only(left: 20, top: 20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                'PAY DATE: '+ (payAdvice.payAdviceDate ??''),
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 16,),
                              )
                            ],
                          )),

                      Padding(
                          padding: const EdgeInsets.only(left: 13.0, right: 13, top: 13),
                          child: Container(
                            height: 1.0,
                            color: Color(0xffA2A2A2),
                          )),

                      //Shifts summary
                      Padding(
                        padding: const EdgeInsets.only(left:20.0,right: 20),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 13.0, left: 0, right: 0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Container(
                                      child: Padding(
                                        padding: const EdgeInsets.only(top: 9.0),
                                        child: Text(
                                          "SHIFT",
                                          style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 13,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                    ),
                                    flex: 1,
                                  ),

                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 9.0, left: 8),
                                      child: Text(
                                        "DATE",textAlign: TextAlign.start,
                                        style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                    flex: 3,
                                  ),
                                  Expanded(
                                    child: Container(
                                      child: Padding(
                                        padding: const EdgeInsets.only(top: 9.0),
                                        child: Text(
                                          "HRS",textAlign: TextAlign.start,
                                          style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 13,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                    ),
                                    flex: 2,
                                  ),
                                  Expanded(
                                    child: Container(
                                      child: Padding(
                                        padding: const EdgeInsets.only(top: 9.0),
                                        child: Text(
                                          "TOTAL(£)",textAlign: TextAlign.start,
                                          style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 13,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                    ),
                                    flex: 2,
                                  ),
                                ],
                              ),
                            ),
                            payAdvice!=null&&payAdvice!.shifts!=null&&payAdvice!.shifts!.length>0?

                            Column(
                                children:

                                // List.generate(2, (int index) {
                                List.generate(payAdvice!.shifts!.length, (int index) {
                                  return Padding(
                                    padding: const EdgeInsets.only(left:0.0,right: 0),
                                    child: Container(
                                      color: index%2==0?Color(0xffE6E6E6):Colors.white,
                                      child: Padding(
                                        padding:
                                        const EdgeInsets.only( left: 5, right: 15,bottom: 10,top: 7),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            Expanded(
                                              child:  Text(payAdvice!.shifts![index]!.shiftId.toString()!,
                                                  style: TextStyle(
                                                      color: Colors.black,
                                                      fontSize: 13,
                                                      fontWeight: FontWeight.normal)),

                                              flex: 1,
                                            ),
                                            Expanded(
                                              child:  Padding(
                                                padding: const EdgeInsets.only(left:5.0),
                                                child: Row(
                                                  // mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                      // SizedBox(width: 14,),
                                                      Text(payAdvice!.shifts![index]!.startDate.toString()!,
                                                          // Text("",
                                                          textAlign: TextAlign.start,
                                                          style: TextStyle(
                                                              color: Colors.black,
                                                              fontSize: 13,
                                                              fontWeight: FontWeight.normal)),

                                                    ]),
                                              ),

                                              flex: 3,
                                            ),
                                            Expanded(
                                              child:  Padding(
                                                padding: const EdgeInsets.only(left:5.0),
                                                child: Row(
                                                  // mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                      // SizedBox(width: 14,),
                                                      Text(payAdvice!.shifts![index]!.numberOfHoursWorked?.toStringAsFixed(2)??'',
                                                          textAlign: TextAlign.start,
                                                          style: TextStyle(
                                                              color: Colors.black,
                                                              fontSize: 13,
                                                              fontWeight: FontWeight.normal)),

                                                    ]),
                                              ),

                                              flex: 2,
                                            ),
                                            Expanded(
                                              child:  Padding(
                                                padding: const EdgeInsets.only(left:10.0),
                                                child: Row(
                                                  // mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                      // SizedBox(width: 14,),
                                                      Text(payAdvice!.shifts![index]!.total?.toStringAsFixed(2)??'',
                                                          textAlign: TextAlign.start,
                                                          style: TextStyle(
                                                              color: Colors.black,
                                                              fontSize: 13,
                                                              fontWeight: FontWeight.normal)),

                                                    ]),
                                              ),

                                              flex: 2,
                                            ),


                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                })) : SizedBox(height: 10,),

                            Padding(
                              padding: const EdgeInsets.only( left: 10, right: 10),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Container(
                                      child: Padding(
                                        padding: const EdgeInsets.only(top: 9.0),
                                        child: Text(
                                          "",
                                          style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 13,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                    ),
                                    flex: 1,
                                  ),

                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 9.0, right: 10),
                                      child: Text(
                                        "",textAlign: TextAlign.center,
                                        style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                    flex: 3,
                                  ),
                                  Expanded(
                                    child: Container(
                                      child: Padding(
                                        padding: const EdgeInsets.only(top: 9.0),
                                        child: Text(
                                          "GROSS PAY £",textAlign: TextAlign.start,
                                          style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 13,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                    ),
                                    flex: 2,
                                  ),
                                  Expanded(
                                    child: Container(
                                      child: Padding(
                                        padding: const EdgeInsets.only(top: 9.0),
                                        child: Text(
                                          payAdvice.totalAmount.toString(),textAlign: TextAlign.start,
                                          style: TextStyle(
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                    ),
                                    flex: 2,
                                  ),
                                ],
                              ),
                            ),
                          ],),),
                      Padding(
                          padding: const EdgeInsets.only(left: 13.0, right: 13, top: 13),
                          child: Container(
                            height: 1.0,
                            color: Color(0xffA2A2A2),
                          )),


                      // Year to date summary
                      Padding(
                        padding: const EdgeInsets.only(left:20.0,right: 20),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 13.0, left: 10, right: 10),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Container(
                                      child: Padding(
                                        padding: const EdgeInsets.only(top: 9.0),
                                        child: Text(
                                          "YEAR TO DATE SUMMARY",
                                          style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 13,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                    ),
                                    flex: 4,
                                  ),
                                ],
                              ),
                            ),
                            payAdvice!=null&&payAdvice!.payAdviceItemResult!=null&&payAdvice!.payAdviceItemResult!.length>0?

                            Column(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(left:0.0, right: 150  ),
                                    child: Container(
                                      color: Color(0xffE6E6E6),
                                      child: Padding(
                                        padding:
                                        const EdgeInsets.only( left: 5, right: 15,bottom: 10,top: 10),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child:  Text('GROSS PAY £',
                                                  style: TextStyle(
                                                      color: Colors.black,
                                                      fontSize: 13,
                                                      fontWeight: FontWeight.normal)),

                                              flex: 4,
                                            ),
                                            Expanded(
                                              child:  Text(payAdvice.workerGross??"",
                                                  style: TextStyle(
                                                      color: Colors.black,
                                                      fontSize: 13,
                                                      fontWeight: FontWeight.normal)),

                                              flex: 4,
                                            ),

                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left:0.0, right: 150  ),
                                    child: Container(
                                      color: Color(0xffE6E6E6),
                                      child: Padding(
                                        padding:
                                        const EdgeInsets.only( left: 5, right: 15,bottom: 10,top: 10),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child:  Text('OTHER PAY £',
                                                  style: TextStyle(
                                                      color: Colors.black,
                                                      fontSize: 13,
                                                      fontWeight: FontWeight.normal)),

                                              flex: 4,
                                            ),
                                            Expanded(
                                              child:  Text("0.00",
                                                  style: TextStyle(
                                                      color: Colors.black,
                                                      fontSize: 13,
                                                      fontWeight: FontWeight.normal)),

                                              flex: 4,
                                            ),

                                          ],
                                        ),
                                      ),
                                    ),
                                  )
                                ]

                            ) : SizedBox(height: 10,)
                          ],
                        ),
                      ),

                      Padding(
                          padding: const EdgeInsets.only(left: 13.0, right: 13, top: 13),
                          child: Container(
                            height: 1.0,
                            color: Color(0xffA2A2A2),
                          )),
                      Padding(
                        padding: const EdgeInsets.only(left:20.0,right: 20),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 13.0, left: 0, ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Container(
                                      child: Padding(
                                        padding: const EdgeInsets.only(top: 9.0),
                                        child: Text(
                                          "ID",
                                          style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 13,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                    ),
                                    flex: 2,
                                  ),

                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 9.0, right: 0),
                                      child: Text(
                                        "DATE",textAlign: TextAlign.start,
                                        style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                    flex: 2,
                                  ),
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 9.0, right: 0),
                                      child: Text(
                                        "DIRECTORATE",textAlign: TextAlign.center,
                                        style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                    flex: 3,
                                  ),
                                  Expanded(
                                    child: Container(
                                      child: Padding(
                                        padding: const EdgeInsets.only(top: 9.0),
                                        child: Text(
                                          "HRS",textAlign: TextAlign.center,
                                          style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 13,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                    ),
                                    flex: 2,
                                  ),
                                  Expanded(
                                    child: Container(
                                      child: Padding(
                                        padding: const EdgeInsets.only(top: 9.0),
                                        child: Text(
                                          "TOTAL(£)",textAlign: TextAlign.center,
                                          style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 13,
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                    ),
                                    flex: 2,
                                  ),
                                ],
                              ),
                            ),
                            payAdvice!=null&&payAdvice!.payAdviceItemResult!=null&&payAdvice!.payAdviceItemResult!.length>0?

                            Column(
                                children:

                                List.generate(payAdvice!.payAdviceItemResult?.length??0, (int index) {
                                  // List.generate(payAdvice!.content!.length, (int index) {
                                  return Padding(
                                    padding: const EdgeInsets.only(left:0.0,right: 0),
                                    child: Container(
                                      color: index%2==0?Color(0xffE6E6E6):Colors.white,
                                      child: Padding(
                                        padding:
                                        const EdgeInsets.only( left: 0, right: 0,bottom: 10,top: 7),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [


                                            Expanded(
                                              child:  Text(payAdvice!.payAdviceItemResult![index]!.shiftId.toString(),
                                                  style: TextStyle(
                                                      color: Colors.black,
                                                      fontSize: 13,
                                                      fontWeight: FontWeight.normal)),

                                              flex: 2,
                                            ),
                                            Expanded(
                                              child:  Padding(
                                                padding: const EdgeInsets.only(left:0.0),
                                                child: Row(
                                                    children: [
                                                      // SizedBox(width: 14,),
                                                      Text(payAdvice!.payAdviceItemResult![index]!.startDate.toString()!,
                                                          textAlign: TextAlign.start,
                                                          style: TextStyle(
                                                              color: Colors.black,
                                                              fontSize: 13,
                                                              fontWeight: FontWeight.normal)),

                                                    ]),
                                              ),

                                              flex: 3,
                                            ),
                                            Expanded(

                                              child: Container(
                                                child: Text(payAdvice!.payAdviceItemResult![index]!.directorate.toString()!,
                                                    textAlign: TextAlign.start,
                                                    style: TextStyle(
                                                        color: Colors.black,
                                                        fontSize: 13,
                                                        fontWeight: FontWeight.normal)
                                                ),),

                                              flex: 4,
                                            ),
                                            Expanded(
                                              child:  Padding(
                                                padding: const EdgeInsets.only(left:0.0),
                                                child: Row(
                                                    children: [
                                                      // SizedBox(width: 14,),
                                                      Text(payAdvice!.payAdviceItemResult![index]!.numberOfHoursWorked?.toStringAsFixed(2)??"",
                                                          textAlign: TextAlign.start,
                                                          style: TextStyle(
                                                              color: Colors.black,
                                                              fontSize: 13,
                                                              fontWeight: FontWeight.normal)),

                                                    ]),
                                              ),

                                              flex: 2,
                                            ),
                                            Expanded(
                                              child:  Padding(
                                                padding: const EdgeInsets.only(left:0.0),
                                                child: Row(
                                                    children: [
                                                      // SizedBox(width: 14,),
                                                      Text(payAdvice!.payAdviceItemResult![index]!.total?.toStringAsFixed(2)??"",
                                                          textAlign: TextAlign.start,
                                                          style: TextStyle(
                                                              color: Colors.black,
                                                              fontSize: 13,
                                                              fontWeight: FontWeight.normal)),

                                                    ]),
                                              ),

                                              flex: 2,
                                            ),


                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                })) : SizedBox(height: 10,)
                          ],),),
                      Padding(
                          padding: const EdgeInsets.only(left: 13.0, right: 13, top: 13),
                          child: Container(
                            height: 1.0,
                            color: Color(0xffA2A2A2),
                          )),

                      Padding(
                        padding: EdgeInsets.only(left: 15),

                        child: ElevatedButton.icon(
                          style: TextButton.styleFrom(
                            backgroundColor: welcomeTextColor,
                            padding: EdgeInsets.symmetric(
                              horizontal: defaultPadding * 1.5,
                            ),
                          ),
                          onPressed: () async {
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => PdfInvoice(invoice: payAdvice,)),
                            );

                          },
                          icon: Icon(Icons.document_scanner),
                          label: Text(
                            "Print",
                          ),
                        ),)



                    ],
                  ));
                }
                // err
                else {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        'failed to get pay advice',
                        style: TextStyle(
                          fontStyle: FontStyle.italic,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  );
                }
              },
              loading: () => Center(
                child: CircularProgressIndicator(),
              ),
              error: (e, st) => Center(
                child: ErrorPage(
                  error: e is CustomException ? e.message : e.toString(),
                  stackTrace: st,onTryAgain: () => watch.refresh(_payslipProvider(payAdviceId)),
                ),
              ),
            ),
          )),
    );
  }
}


