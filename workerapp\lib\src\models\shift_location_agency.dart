// To parse this JSON data, do
//
//     final shiftLocationAgency = shiftLocationAgencyFromJson(jsonString);

import 'dart:convert';

List<ShiftLocationAgency> shiftLocationAgencyFromJson(String str) =>
    List<ShiftLocationAgency>.from(
        json.decode(str).map((x) => ShiftLocationAgency.fromJson(x)));

String shiftLocationAgencyToJson(List<ShiftLocationAgency> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ShiftLocationAgency {
  ShiftLocationAgency({
    this.id,
    this.name,
    this.postcode,
    this.createdBy,
    this.phoneNumber,
    this.client,
    this.clientId,
  });

  final int? id;
  final String? name;
  final String? postcode;
  final String? createdBy;
  final String? phoneNumber;
  final String? client;
  final int? clientId;

  factory ShiftLocationAgency.fromJson(Map<String, dynamic> json) =>
      ShiftLocationAgency(
        id: json["id"],
        name: json["name"],
        postcode: json["postcode"],
        createdBy: json["createdBy"],
        phoneNumber: json["phoneNumber"],
        client: json["client"],
        clientId: json["clientId"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "postcode": postcode,
        "createdBy": createdBy,
        "phoneNumber": phoneNumber,
        "client": client,
        "clientId": clientId,
      };
}
