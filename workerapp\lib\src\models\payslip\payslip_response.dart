// To parse this JSON data, do
//
//     final PayslipResponse = PayslipResponseFromJson(jsonString);

import 'dart:convert';

class PayslipResponse {
  PayslipResponse({
    this.content,
    this.pageable,
    this.totalPages,
    this.totalElements,
    this.last,
    this.size,
    this.number,
    this.sort,
    this.numberOfElements,
    this.first,
    this.empty,
  });

  final List<Content?>? content;
  final Pageable? pageable;
  final int? totalPages;
  final int? totalElements;
  final bool? last;
  final int? size;
  final int? number;
  final Sort? sort;
  final int? numberOfElements;
  final bool? first;
  final bool? empty;

  PayslipResponse copyWith({
    List<Content?>? content,
    Pageable? pageable,
    int? totalPages,
    int? totalElements,
    bool? last,
    int? size,
    int? number,
    Sort? sort,
    int? numberOfElements,
    bool? first,
    bool? empty,
  }) =>
      PayslipResponse(
        content: content ?? this.content,
        pageable: pageable ?? this.pageable,
        totalPages: totalPages ?? this.totalPages,
        totalElements: totalElements ?? this.totalElements,
        last: last ?? this.last,
        size: size ?? this.size,
        number: number ?? this.number,
        sort: sort ?? this.sort,
        numberOfElements: numberOfElements ?? this.numberOfElements,
        first: first ?? this.first,
        empty: empty ?? this.empty,
      );

  factory PayslipResponse.fromRawJson(String str) =>
      PayslipResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory PayslipResponse.fromJson(Map<String, dynamic> json) =>
      PayslipResponse(
        content:
            List<Content>.from(json["content"].map((x) => Content.fromJson(x))),
        pageable: Pageable.fromJson(json["pageable"]),
        totalPages: json["totalPages"],
        totalElements: json["totalElements"],
        last: json["last"],
        size: json["size"],
        number: json["number"],
        sort: Sort.fromJson(json["sort"]),
        numberOfElements: json["numberOfElements"],
        first: json["first"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "content": List<dynamic>.from(content!.map((x) => x?.toJson())),
        "pageable": pageable?.toJson(),
        "totalPages": totalPages,
        "totalElements": totalElements,
        "last": last,
        "size": size,
        "number": number,
        "sort": sort?.toJson(),
        "numberOfElements": numberOfElements,
        "first": first,
        "empty": empty,
      };
}

List<Content> contentFromJson(String str) =>
    List<Content>.from(json.decode(str).map((x) => Content.fromJson(x)));

String contentToJson(List<Content> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Content {

  Content({
    this.id,
    this.date,
    this.workerId,
    this.agencyId,
    this.payslipPDF

  });

  final int? id;
  final String? date;
  final String? workerId;
  final String? agencyId;
  final String? payslipPDF;

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        id: json["id"],
        date: json["date"],
      workerId: json["workerId"],
    agencyId: json["agencyId"],
      payslipPDF: json["payslipPDF"],


  );


  Map<String, dynamic> toJson() => {
        "id": id,
        "date": date,
        "workerId": workerId,
    "agencyId": agencyId,
    "payslipPDF": payslipPDF,

  };
}

class Pageable {
  Pageable({
    this.sort,
    this.offset,
    this.pageNumber,
    this.pageSize,
    this.paged,
    this.unpaged,
  });

  final Sort? sort;
  final int? offset;
  final int? pageNumber;
  final int? pageSize;
  final bool? paged;
  final bool? unpaged;

  Pageable copyWith({
    Sort? sort,
    int? offset,
    int? pageNumber,
    int? pageSize,
    bool? paged,
    bool? unpaged,
  }) =>
      Pageable(
        sort: sort ?? this.sort,
        offset: offset ?? this.offset,
        pageNumber: pageNumber ?? this.pageNumber,
        pageSize: pageSize ?? this.pageSize,
        paged: paged ?? this.paged,
        unpaged: unpaged ?? this.unpaged,
      );

  factory Pageable.fromRawJson(String str) =>
      Pageable.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
        sort: Sort.fromJson(json["sort"]),
        offset: json["offset"],
        pageNumber: json["pageNumber"],
        pageSize: json["pageSize"],
        paged: json["paged"],
        unpaged: json["unpaged"],
      );

  Map<String, dynamic> toJson() => {
        "sort": sort?.toJson(),
        "offset": offset,
        "pageNumber": pageNumber,
        "pageSize": pageSize,
        "paged": paged,
        "unpaged": unpaged,
      };
}

class Sort {
  Sort({
    this.sorted,
    this.unsorted,
    this.empty,
  });

  final bool? sorted;
  final bool? unsorted;
  final bool? empty;

  Sort copyWith({
    bool? sorted,
    bool? unsorted,
    bool? empty,
  }) =>
      Sort(
        sorted: sorted ?? this.sorted,
        unsorted: unsorted ?? this.unsorted,
        empty: empty ?? this.empty,
      );

  factory Sort.fromRawJson(String str) => Sort.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Sort.fromJson(Map<String, dynamic> json) => Sort(
        sorted: json["sorted"],
        unsorted: json["unsorted"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
        "sorted": sorted,
        "unsorted": unsorted,
        "empty": empty,
      };
}
