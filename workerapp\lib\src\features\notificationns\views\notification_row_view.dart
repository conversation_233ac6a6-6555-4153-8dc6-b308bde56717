import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/features/chat/chat_page.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/features/shifts/data/shift_category.dart';
import 'package:work_link/src/models/custom_exception.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';
import '../../../models/worker_notifications_response.dart';
import '../../chat/chat_app2.dart';

final _shiftProvider =
    AutoDisposeFutureProviderFamily<WorkerNotificationResponse?, String>(
        (ref, status) {
  final _notification = ref.watch(notificationRepoProvider);
  return _notification.getNotifications();
});

class NotificationRowView extends ConsumerWidget {
  NotificationRowView({Key? key,}) : super(key: key);



  final formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context,   watch) {


    return watch.watch(_shiftProvider("")).when(
      data: (WorkerNotificationResponse? value) {
        if (value != null) {
          final shifts = value.content;

          if (shifts == null) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  'failed to get notifications',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    color: Colors.grey,
                  ),
                ),
              ),
            );
          }

          return shifts.isEmpty
              ? Center(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      'no notifications available',
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                )
              : SingleChildScrollView(
            child: Column(
              children: [

                ListView(
                  shrinkWrap: true,
                  children: [
                    GestureDetector(
                      child: NotificationTile(
                        time: 'Today, 12:10pm',
                        message: 'TM accepted your carpooling request for a Shift in Leeds on 20 Oct 2024. Tap here to chat and ride!',
                        isNew: true,
                      ),
                      // onTap: ()=>  Navigator.push(context, MaterialPageRoute(builder: (context) =>  ChatPage()),),
                      onTap: ()=>  Navigator.push(context, MaterialPageRoute(builder: (context) =>  WebSocketChatPage()),),
                    ),
                    NotificationTile(
                      time: 'Today, 10:30am',
                      message: 'TM accepted your carpooling request for a Shift in Leeds on 20 Oct 2024. Tap here to chat and ride!',
                      isNew: false,
                    ),
                  ],
                ),

                ListView.builder(
                  shrinkWrap: true,
                  itemCount: shifts.length,
                  itemBuilder: (context, index) =>  NotificationTile(
                    time: 'Today, 10:30pm',
                    message: shifts[index]!.title!,
                    isNew: true,
                  ),
                ),


                Center(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 10, bottom: 20),
                    child: Text(
                      'pull down to refresh',
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        // err
        else {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                'failed to get shifts',
                style: TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
              ),
            ),
          );
        }
      },
      loading: () => Center(
        child: CircularProgressIndicator(),
      ),
      error: (e, st) => Center(
        child: ErrorPage(
          error: e is CustomException ? e.message : e.toString(),
          stackTrace: st,onTryAgain: () => watch.refresh(_shiftProvider("")),
        ),
      ),
    );
  }
}




class NotificationTile extends StatelessWidget {
  final String time;
  final String message;
  final bool isNew;

  NotificationTile({required this.time, required this.message, required this.isNew});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(
        Icons.notifications,
        color: isNew ? Colors.green : Colors.grey,
      ),
      title: Text(time),
      subtitle: Text(message),
      trailing: Icon(
        Icons.circle,
        color: isNew ? Colors.pink : Colors.grey,
      ),
    );
  }
}
