
import 'package:freezed_annotation/freezed_annotation.dart';

import '../training_session.dart';

part 'training_sessions_response.freezed.dart';
part 'training_sessions_response.g.dart';

@freezed
abstract class TrainingSessionsResponse with _$TrainingSessionsResponse {
  factory TrainingSessionsResponse({

    List<TrainingSession>? content


  }) = _TrainingSessionsResponse;

  factory TrainingSessionsResponse.fromJson(Map<String, dynamic> json) =>
      _$TrainingSessionsResponseFromJson(json);

}
