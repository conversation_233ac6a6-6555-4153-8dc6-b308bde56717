  import 'dart:convert';
import 'dart:developer';

import 'package:work_link/src/models/shift_expense_claim/ShiftExpenseClaim.dart';

import '../app_providers.dart';
import '../../widgets/ApiCallingWithoutProgressIndicator.dart';
import '../../utils/constants.dart';
import '../../models/agency_expense_rate/AgencyExpenseRate.dart';
import '../../utils/exception_handler.dart';


Future<List<AgencyExpenseRate>?> getAgencyExpenseRates(int agencyId) async {
    // final options = _reader(accessKeyOptionsProvider);

      try {
        final result = await ApiCalling().apiCallForUserProfile('${baseUrl}${dataService}/api/v1/agency/expense-rate/$agencyId', 'get');


        if (result?.statusCode == 200) {
          var l = result!.data as List;

          if (l.isEmpty) return [];

          final List<Map<String, dynamic>> rates = List.from(l);

          return rates.map((e) => AgencyExpenseRate.fromJson(e)).toList();
        }

        else {
          throw Exception(
              'Error getting available shifts. Try again later');
        }
      }
      catch (e) {
        print(e.toString());
        throw exceptionHandler(e, 'worker  shifts',StackTrace.current);
      }

  }
Future<List<ShiftExpenseClaim>?> getShiftClaims(List<int> shiftIds) async {
    // final options = _reader(accessKeyOptionsProvider);

      try {
        final result = await ApiCalling().apiCallForUserProfile(
            '${baseUrl}${dataService}/api/v1/shift/expense-claim/${shiftIds.first}', 'get');



        if (result?.statusCode == 200) {
          var l = result?.data as List;

          if (l.isEmpty) return [];

          final List<Map<String, dynamic>> rates = List.from(l);

          return rates.map((e) => ShiftExpenseClaim.fromJson(e)).toList();
        }

        else {
          throw Exception(
              'Error getting available shifts. Try again later');
        }
      }
      catch (e) {
        print(e.toString());
        throw exceptionHandler(e, 'worker  shifts',StackTrace.current);
      }

  }
Future claimExpense( data) async {
    // final options = _reader(accessKeyOptionsProvider);

      try {
        final result = await ApiCalling().apiCallpost(null,
            '${baseUrl}${dataService}/api/v1/shift/expense-claim', data,"post");

        if (result?.statusCode == 200 || result?.statusCode == 201) {
          return true;
        } else {
          throw Exception(  'Error getting available shifts. Try again later');
        }
      } catch (e) {
        print(e.toString());
        throw exceptionHandler(e, 'worker  shifts',StackTrace.current);
      }

  }