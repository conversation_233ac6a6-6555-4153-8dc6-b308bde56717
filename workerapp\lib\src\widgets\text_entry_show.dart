import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/utils/index.dart';

import '../utils/color_constants.dart';

class TextEntryField extends StatelessWidget {
  const TextEntryField(
      {Key? key,
      this.title,
      this.initialText,
      required this.ctx,
      this.titleColor})
      : super(key: key);

  final String? title;
  final BuildContext ctx;
  final String? initialText;
  final Color? titleColor;

  @override
  Widget build(BuildContext context) {
    return RelativeBuilder(
      builder: (context, height, width, sy, sx) {
        return Padding(
          padding: const EdgeInsets.all(5.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(title ?? '')
                  .textColor(titleColor ?? Colors.black54)
                  .fontSize(sx(25)),
              customTextFieldShow(
                context: ctx,
                initialText: initialText ?? '',
              ),
            ],
          ),
        );
      },
    );
  }
}

Widget customTextFieldShow({
  BuildContext? context,
  String initialText = '',
}) {
  const double _radius = 10.0;

  return Padding(
    padding: EdgeInsets.all(12),
    child: Container(
      height: 60,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(_radius),
        border: Border.all(
          color: Colors.grey.withOpacity(0.3),
        ),
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: Text(
            initialText,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.w400,
              fontSize: 15,
              fontStyle: FontStyle.normal,
            ),
          ),
        ),
      ),
    ),
  );
}

// ---------------------------- shift ---------------------------
class ShiftTextEntryField extends StatelessWidget {
  const ShiftTextEntryField({
    Key? key,
    this.title,
    this.initialText,
    required this.ctx,
    this.titleColor,
    this.fieldHeight = 40,
    this.maxLines,
  }) : super(key: key);

  final String? title;
  final BuildContext ctx;
  final String? initialText;
  final Color? titleColor;
  final double fieldHeight;
  final int? maxLines;

  @override
  Widget build(BuildContext context) {
    return RelativeBuilder(
      builder: (context, height, width, sy, sx) {
        return Padding(
          padding: const EdgeInsets.all(5.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(title ?? '')
                  .textColor(titleColor ?? Colors.black54)
                  .fontSize(sx(17)),
              customShiftTextFieldShow(
                context: ctx,
                initialText: initialText ?? '',
                fieldHeight: fieldHeight,

                maxLines: maxLines,
              ),
            ],
          ),
        );
      },
    );
  }
}

Widget customShiftTextFieldShow({
  BuildContext? context,
  String initialText = '',
  double? fieldHeight,
  int? maxLines,
}) {
  const double _radius = 4.0;

  return GestureDetector(
    child:Padding(
      padding: EdgeInsets.only(top: 5),
      child: Container(
        height: fieldHeight ?? 40,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(_radius),
          color: inputBgColor,
          border: Border.all(
            color: inputBgColor.withOpacity(0.3),
          ),
        ),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: const EdgeInsets.only(left: 10),
            child: Text(
              initialText,
              softWrap: true,
              maxLines: maxLines,
              style: TextStyle(
                color: blackColor,
                fontWeight: FontWeight.w400,

                fontSize: 15,
                fontStyle: FontStyle.normal,
              ),
            ),
          ),
        ),
      ),
    ),
    onLongPress: () async {
      Clipboard.setData(ClipboardData(text: initialText));
      // Optionally, you can show a SnackBar to notify the user about the copy.
      if(context!=null)ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Copied to Clipboard')),
      );

      HapticFeedback.vibrate();
    },
  );
}
