import 'package:flutter/material.dart';
import 'package:duration/duration.dart';
import 'package:intl/intl.dart';
import 'package:work_link/src/features/shifts/data/shift_category.dart';
import 'package:work_link/src/models/filter-shift/filtered_shift.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:styled_widget/styled_widget.dart';

String _formatDuration(Duration duration) {
  if (duration.inDays > 0) {
    return '${duration.inDays}d ${duration.inHours % 24}h';
  } else if (duration.inHours > 0) {
    return '${duration.inHours}h ${duration.inMinutes % 60}m';
  } else {
    return '${duration.inMinutes}m';
  }
}

Widget trailingHeaderWidget(ShiftCategoryStatus status, FilteredShift shift) {
  var w;

  switch (status.status) {
    case 'CANCELLED':
      w = Text('Shift Closed').textColor(Colors.red);
      break;

    case 'BOOKED':
      final _shiftD = shift.shiftDate! + ' ' + shift.shiftStartTime!;
      final _fd = DateFormat('dd/MM/yyyy HH:mm').parse(_shiftD);

      final duration = _fd.difference(DateTime.now());

      final _dd = _formatDuration(duration);

      w = RichText(
        text: TextSpan(
          text: 'Shift starts in ',
          style: TextStyle(color: textColor),
          children: <TextSpan>[
            TextSpan(
              // text: ' 5hrs 27mins',
              text: _dd,
              style: TextStyle(color: Colors.blueAccent),
            ),
          ],
        ),
      );
      break;

    default:
      w = SizedBox.shrink();
  }

  return w;
}
