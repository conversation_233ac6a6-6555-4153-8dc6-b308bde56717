import 'package:freezed_annotation/freezed_annotation.dart';

part 'CarPoolingWorker.freezed.dart';
part 'CarPoolingWorker.g.dart';

@freezed
abstract class CarPoolingWorker with _$CarPoolingWorker {
  factory CarPoolingWorker({
    int? id,
    String? firstName,
    String? gender,
    String? lastName,
    String? postCode,
  }) = _CarPoolingWorker;

  factory CarPoolingWorker.fromJson(Map<String, dynamic> json) =>
      _$CarPoolingWorkerFromJson(json);
}
