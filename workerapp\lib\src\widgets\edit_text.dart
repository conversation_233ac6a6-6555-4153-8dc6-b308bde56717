import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';


class EditText extends StatelessWidget {
  String? hint, validationMessage;
  TextEditingController? textEditingController;
  TextInputType? keyboardType;
  int maxLength;
  double hgt;
  Color color;
  Color borderColor;
  Color textColor;

  bool isHide;
  void Function(String?)? onChanged;
  String? Function(String?)? validate;
  bool isValidation;
  bool isReadOnly;
  String? initialValue;
  EditText(
      {Key? key,
      required this.hgt,
      this.hint,
        this.validate,
        this.onChanged,
        this.initialValue,
      this.textEditingController,
        this.keyboardType,
        this.validationMessage,
      required this.maxLength,
      required this.color,
      required this.textColor,
      required this.borderColor,
        required this.isHide,required this.isValidation,required this.isReadOnly})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Padding(
        padding: const EdgeInsets.only(top: 0.0, left: 10.0, right: 10.0),
        child: Container(
          height: hgt,
          decoration: BoxDecoration(
              color: color,
              border: Border.all(
                color: borderColor,
              ),
              borderRadius: BorderRadius.all(Radius.circular(4))),
          child: TextFormField(
            maxLength: maxLength,
            validator: validate??(value) {
              if (isValidation&&value!.isEmpty) {
                return validationMessage;
              }
              return null;
            },
            readOnly: isReadOnly,
            initialValue: initialValue,
            obscureText: isHide,

            onChanged: onChanged,
            controller: textEditingController,
            keyboardType: keyboardType,
            style:  TextStyle(
                color: textColor, fontSize: 16.0),
            decoration: InputDecoration(
              counterText: "",
              labelText:hint ,
             // hintText: hint,
              hintStyle: const TextStyle(
                  color: Colors.black54, fontSize: 16.0),
              labelStyle: const TextStyle(
                  color: Colors.black54, fontSize: 16.0),
              border: InputBorder.none,
              focusedBorder: InputBorder.none,
              enabledBorder: InputBorder.none,
              errorBorder: InputBorder.none,
              disabledBorder: InputBorder.none,
              contentPadding: const EdgeInsets.only(
                  left: 15, bottom: 11, top: 11, right: 15),
            ),
          ),
        ));
  }
}
