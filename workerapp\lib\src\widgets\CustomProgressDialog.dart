import 'package:flutter/material.dart';
import 'package:flutter/services.dart';


class CustomProgressLoader {
  static showLoader(context) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => Center(
          // Aligns the container to center
          child: Container(
            // A simplified version of dialog.
              width: 30.0,
              height: 30.0,
              child:  CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation(Colors.black54),
                strokeWidth: 2.0,
              )),
        ));
  }


  static cancelLoader(context) {
    if (context != null)
      Navigator.of(context, rootNavigator: true).pop('dialog');
  }


}
