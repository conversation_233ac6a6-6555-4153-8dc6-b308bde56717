import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smart_calendar/controller/smart_calendar_controller.dart'; // Temporarily disabled
// import 'package:smart_calendar/smart_calendar.dart'; // Temporarily disabled
import 'package:url_launcher/url_launcher.dart';
import 'package:work_link/src/widgets/ApiCallingWithoutProgressIndicator.dart';
import 'package:work_link/src/widgets/CustomProgressDialog.dart';
import 'package:work_link/src/utils/colors.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../utils/UserPreference.dart';
import '../../utils/constants.dart';
import 'model/training_model.dart';

class FormsInfoWidget extends StatefulWidget {
  @override
  FormsInfoWidgetState createState() => FormsInfoWidgetState();
}

class FormsInfoWidgetState extends State<FormsInfoWidget> {

  TrainingModel? wp;
  SharedPreferences? prefs;
  init() async {
    prefs = await SharedPreferences.getInstance();
    String? workerId = await prefs!.getString(UserPreference.WORKER_ID);
    //getBookedDates(workerId);
    Future.delayed(Duration.zero, () {
      getTrainingData(workerId);
    });
  }

  getTrainingData(workerId) async {
    try {
      CustomProgressLoader.showLoader(context);
      print("log+++"+"$dataService/api/v1/worker-training/"+workerId+"/0/300");
      Response? response = await ApiCalling()
          .apiCall(context, "$dataService/api/v1/worker-training/"+workerId+"/0/300", "get");
      CustomProgressLoader.cancelLoader(context);
      print("response++++++" + response.toString());
      if (response != null) {
        if (response.statusCode == 200) {
          wp = TrainingModel.fromJson(response.data);
      setState(() {

      });
        }
      }
    } catch (e) {

      print("issue shubh" + e.toString());
      return null;
    }
  }


  @override
  void initState() {
    // TODO: implement initState
    init();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {

    final monthLater =  DateTime.now().add(Duration(days: 30));
    final today =  DateTime.now();

    return Scaffold(
      appBar: AppBarDefault(context,"Forms",
          leading: Container(
            height: 30,
            width: 30,
            child: Padding(
              padding: const EdgeInsets.only(left: 15.0, right: 15),
              child: InkWell(
                child: Icon(   Icons.arrow_back,
                  color: welcomeTextColor,
                  size: 20,
                ),
                onTap: () {
                  routeBack(context);
                },
              ),
            ),
          )),
      body: SafeArea(
          child: Container(
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Padding(
                //     padding: const EdgeInsets.only(right: 13, top: 20),
                //     child: Row(
                //       mainAxisAlignment: MainAxisAlignment.center,
                //       children: [
                //         Padding(
                //             padding: const EdgeInsets.only(left: 13.0, right: 15),
                //             child: Image.asset(
                //               "assets/drawer/forms.png",
                //               height: 23.0,
                //               width: 23.0,
                //             )),
                //         Text(
                //           'Forms',
                //           style: TextStyle(
                //               color: Colors.black,
                //               fontSize: 22,
                //               fontWeight: FontWeight.bold),
                //         )
                //       ],
                //     )),
                // Padding(
                //     padding: const EdgeInsets.only(left: 13.0, right: 13, top: 13),
                //     child: Container(
                //       height: 1.0,
                //       color: Color(0xffA2A2A2),
                //     )),

                Expanded(
                    flex: 1,
                    child:  Padding(
                      padding: const EdgeInsets.only(left:20.0,right: 20),
                      child: SingleChildScrollView( child:Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 13.0, left: 10, right: 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Container(
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 9.0),
                                      child: Text(
                                        "TRAINING NAME",
                                        style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                  ),
                                  flex: 4,
                                ),
                                Expanded(
                                  child: Container(
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 9.0),
                                      child: Text(
                                        "ACTION",textAlign: TextAlign.center,
                                        style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                  ),
                                  flex: 2,
                                ),
                              ],
                            ),
                          ), Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(left:0.0,right: 0),
                                  child: Container(
                                    color: Color(0xffE6E6E6),
                                    child: Padding(
                                      padding:
                                      const EdgeInsets.only( left: 5, right: 15,bottom: 10,top: 7),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child:  Text("Application Form",
                                                style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.normal)),

                                            flex: 4,
                                          ),
                                          Expanded(
                                            child:  Padding(
                                              padding: const EdgeInsets.only(left:8.0),
                                              child: InkWell(
                                                  onTap: () async {
                                                    var res = await showDialog<bool?>(
                                                        context: context,
                                                        builder: (ctx) {
                                                          return AlertDialog(
                                                            contentPadding : const EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 0.0),
                                                            content: Container(
                                                              height: 185,
                                                              child: Column(
                                                                children: [
                                                                  Container(
                                                                      height: 40,
                                                                      width: double.infinity,
                                                                      color: welcomeTextColor,
                                                                      child: Padding(
                                                                        padding: const EdgeInsets.only(top:10.0),
                                                                        child: Text('This file will view in a web browser.',textAlign: TextAlign.center,style: TextStyle(fontSize: 14,color: Colors.white,fontWeight: FontWeight.w500),),
                                                                      )),
                                                                  Padding(
                                                                      padding: const EdgeInsets.only(top:35.0,bottom: 30),
                                                                      child:   Text('Do you want to proceed?')),

                                                                  Row(
                                                                    crossAxisAlignment: CrossAxisAlignment.center,
                                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                                    children: [


                                                                      ElevatedButton.icon(
                                                                        label: Text('Yes'),
                                                                        icon: Icon(Icons.done),

                                                                        style: ElevatedButton.styleFrom(
                                                                          backgroundColor: welcomeTextColor,
                                                                        ),
                                                                        onPressed: ()
                                                                        async {
                                                                          String url = webBaseUrl+"/worker/forms/application" ;
                                                                          launch(url);

                                                                        },
                                                                      ),
                                                                      Padding(
                                                                        padding: const EdgeInsets.only(left:15.0),
                                                                        child: ElevatedButton.icon(
                                                                          icon: Icon(Icons.cancel_outlined),
                                                                          label: Text('No'),
                                                                          style: ElevatedButton.styleFrom(
                                                                            backgroundColor: denyRed,
                                                                          ),
                                                                          onPressed: () {
                                                                            // TODO process
                                                                            Navigator.pop(context, false);
                                                                          },
                                                                        ),
                                                                      ),
                                                                    ],)
                                                                ],
                                                              ),
                                                            ),

                                                          );
                                                        });

                                                    print(res);
                                                  },
                                                  child: Icon(
                                                    Icons.remove_red_eye,
                                                    color: welcomeTextColor,
                                                  )),
                                            ),

                                            flex: 2,
                                          ),


                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(left:0.0,right: 0),
                                  child: Container(
                                    color: Colors.white,
                                    child: Padding(
                                      padding:
                                      const EdgeInsets.only( left: 5, right: 15,bottom: 10,top: 7),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child:  Text("Occupational Health Medical Questionnnaire",
                                                style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.normal)),

                                            flex: 4,
                                          ),
                                          Expanded(
                                            child:  Padding(
                                              padding: const EdgeInsets.only(left:8.0),
                                              child: InkWell(
                                                  onTap: () async {
                                                    String url = webBaseUrl+"worker/forms/occupational" ;
                                                    // String url = "https://test.myworklink.uk/worker/forms" ;
                                                    // _launchUrl(url);
                                                    launch(url);

                                                  },
                                                  child: Icon(
                                                    Icons.remove_red_eye,
                                                    color: welcomeTextColor,
                                                  )),
                                            ),

                                            flex: 2,
                                          ),


                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(left:0.0,right: 0),
                                  child: Container(
                                    color: Color(0xffE6E6E6),
                                    child: Padding(
                                      padding:
                                      const EdgeInsets.only( left: 5, right: 15,bottom: 10,top: 7),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child:  Text("HM Revenue & Customs",
                                                style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.normal)),

                                            flex: 4,
                                          ),
                                          Expanded(
                                            child:  Padding(
                                              padding: const EdgeInsets.only(left:8.0),
                                              child: InkWell(
                                                  onTap: () async {
                                                    String url = webBaseUrl+"worker/forms/hmrc" ;
                                                    // String url = "https://test.myworklink.uk/worker/forms" ;
                                                    // _launchUrl(url);
                                                    launch(url);

                                                  },
                                                  child: Icon(
                                                    Icons.remove_red_eye,
                                                    color: welcomeTextColor,
                                                  )),
                                            ),

                                            flex: 2,
                                          ),


                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(left:0.0,right: 0),
                                  child: Container(
                                    color: Colors.white,
                                    child: Padding(
                                      padding:
                                      const EdgeInsets.only( left: 5, right: 15,bottom: 10,top: 7),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Expanded(
                                            child:  Text("Bank",
                                                style: TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 13,
                                                    fontWeight: FontWeight.normal)),

                                            flex: 4,
                                          ),
                                          Expanded(
                                            child:  Padding(
                                              padding: const EdgeInsets.only(left:8.0),
                                              child: InkWell(
                                                  onTap: () async {
                                                    String url = webBaseUrl+"worker/forms/bank" ;
                                                    // String url = "https://test.myworklink.uk/worker/forms" ;
                                                    // _launchUrl(url);x
                                                    launch(url);

                                                  },
                                                  child: Tooltip(
                                                    message: "View",
                                                    child: Icon(
                                                      Icons.remove_red_eye,
                                                      color: welcomeTextColor,
                                                    ),
                                                  )),
                                            ),

                                            flex: 2,
                                          ),


                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ]

                          ) ,


                        ],
                      ),),
                    ))
              ],
            ),
          )),
    );
  }
}
