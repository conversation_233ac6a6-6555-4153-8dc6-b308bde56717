
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:work_link/src/features/invoices/invoices_home_screen.dart';
import 'package:work_link/src/features/login/views/login_view.dart';
import 'package:work_link/src/utils/color_constants.dart';

import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import 'package:work_link/src/widgets/index.dart';

import '../features/calendar/custom_calender.dart';
import '../features/drawer/compilance_info_widget.dart';
import '../features/drawer/forms_info_widget.dart';
import '../features/trainings/training_info_widget.dart';
import '../features/profile/views/user_profile.dart';
import '../features/splash/views/splash_view.dart';
import 'FAQ.dart';
import '../utils/UserPreference.dart';
import 'change_password.dart';



class CommonDrawer extends StatefulWidget {
  @override
  CommonDrawerState createState() => CommonDrawerState();
}

class CommonDrawerState extends State<CommonDrawer> {


   SharedPreferences? prefs;
   String? name;

   init()async{
     prefs = await SharedPreferences.getInstance();
     String?  firstName=  prefs!.getString(UserPreference.firstName);
     String?  lastName=  prefs!.getString(UserPreference.lastName);
     if(lastName==null||lastName==""){
       name=firstName;
     }else{
       name=firstName!+" "+lastName;
     }
setState(() {
  name;
});
   }

  @override
  void initState() {
    // TODO: implement initState
    init();
    super.initState();

  }




  Widget build(BuildContext context) {

    return Scaffold(
      appBar: AppBarDefault(context ,"Profile",leading:
          Container(
            height: 30,
            width: 30,
            child: Padding(
              padding: const EdgeInsets.only(left: 15.0,right: 15),
              child: InkWell(
                child:  Icon(   Icons.arrow_back,
                  color: welcomeTextColor,
                  size: 20,
                ),
                onTap: () {
            routeBack(context);
                },
              ),
            ),
          ) ),
      body: Padding(
        padding: const EdgeInsets.all(defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [

          Column(

            children: [
              Padding(
                  padding: const EdgeInsets.only(bottom:13.0),
                  child:InkWell(
                      onTap: () {
                        routeTo(context, UserProfile());
                      },
                      child:   Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(child:Image.asset(
                            "assets/images/profile.png",
                            height: 23.0,
                            width: 23.0,
                          ) ,flex: 0,),
                          Expanded(child:Padding(
                              padding: const EdgeInsets.only(left: 13.0,right: 5),
                              child:  Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('My Details',style: drawerTextStyle,),
                                  Padding(
                                      padding: const EdgeInsets.only(top: 10.0),
                                      child: Container(height: 1.0,color: dividerColor,))
                                ],
                              )),flex: 1,),
                        ],))),

              Padding(
                  padding: const EdgeInsets.only(bottom:13.0),
                  child:  InkWell(
                      onTap: () {
                        routeTo(context, Calendr());
                      },
                      child:  Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(child:Image.asset(
                            "assets/drawer/availibality.png",
                            height: 23.0,
                            width: 23.0,
                          ) ,flex: 0,),
                          Expanded(child:Padding(
                              padding: const EdgeInsets.only(left: 13.0,right: 5),
                              child:  Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('My Availability',style: drawerTextStyle,),
                                  Padding(
                                      padding: const EdgeInsets.only(top: 10.0),
                                      child: Container(height: 1.0,color: dividerColor,))
                                ],
                              )),flex: 1,),
                        ],))),

              Padding(
                  padding: const EdgeInsets.only(bottom:13.0),
                  child:  InkWell(
                      onTap: () {
                        routeTo(context, ComplianceInfoWidget());
                      },
                      child:  Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(child:Image.asset(
                            "assets/drawer/compilance.png",
                            height: 23.0,
                            width: 23.0,
                          ) ,flex: 0,),
                          Expanded(child:Padding(
                              padding: const EdgeInsets.only(left: 13.0,right: 5),
                              child:  Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Compliance information',style: drawerTextStyle,),
                                  Padding(
                                      padding: const EdgeInsets.only(top: 10.0),
                                      child: Container(height: 1.0,color: dividerColor,))
                                ],
                              )),flex: 1,),
                        ],))),


              // Padding(
              //     padding: const EdgeInsets.only(bottom:13.0),
              //     child:  InkWell(
              //         onTap: () {
              //           routeTo(context, TrainingInfoWidget());
              //         },
              //         child:  Row(
              //           crossAxisAlignment: CrossAxisAlignment.start,
              //           children: [
              //             Expanded(child:Image.asset(
              //               "assets/drawer/training.png",
              //               height: 23.0,
              //               width: 23.0,
              //             ) ,flex: 0,),
              //             Expanded(child:Padding(
              //                 padding: const EdgeInsets.only(left: 13.0,right: 5),
              //                 child:  Column(
              //                   crossAxisAlignment: CrossAxisAlignment.start,
              //                   children: [
              //                     Text('Training & Qualifications',style: drawerTextStyle,),
              //                     Padding(
              //                         padding: const EdgeInsets.only(top: 10.0),
              //                         child: Container(height: 1.0,color: dividerColor,))
              //                   ],
              //                 )),flex: 1,),
              //           ],))),



              Padding(
                  padding: const EdgeInsets.only(bottom:13.0),
                  child:  InkWell(
                      onTap: () {
                        routeTo(context, FormsInfoWidget());
                      },
                      child:  Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(child:Image.asset(
                            "assets/drawer/forms.png",
                            height: 23.0,
                            width: 23.0,
                          ) ,flex: 0,),
                          Expanded(child:Padding(
                              padding: const EdgeInsets.only(left: 13.0,right: 5),
                              child:  Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Forms',style: drawerTextStyle,),
                                  Padding(
                                      padding: const EdgeInsets.only(top: 10.0),
                                      child: Container(height: 1.0,color: dividerColor,))
                                ],
                              )),flex: 1,),
                        ],))),
              Padding(
                  padding: const EdgeInsets.only(bottom:13.0),
                  child:  InkWell(
                      onTap: () {
                        routeTo(context, InvoicesHomeScreen());
                      },
                      child:  Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(child:Image.asset(
                            "assets/drawer/compilance.png",
                            height: 23.0,
                            width: 23.0,
                          ) ,flex: 0,),
                          Expanded(child:Padding(
                              padding: const EdgeInsets.only(left: 13.0,right: 5),
                              child:  Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Invoices',style: drawerTextStyle,),
                                  Padding(
                                      padding: const EdgeInsets.only(top: 10.0),
                                      child: Container(height: 1.0,color: dividerColor,))
                                ],
                              )),flex: 1,),
                        ],))),




              Padding(
                  padding: const EdgeInsets.only(bottom:13.0),
                  child:   Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(child:Image.asset(
                        "assets/images/question.png",
                        height: 23.0,
                        width: 23.0,
                      ) ,flex: 0,),
                      Expanded(child:InkWell(
                          onTap: () {
                            routeTo(context, Faq());
                          },
                          child:Padding(
                              padding: const EdgeInsets.only(left: 13.0,right: 5),
                              child:  Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('FAQs',style: drawerTextStyle,),
                                  Padding(
                                      padding: const EdgeInsets.only(top: 10.0),
                                      child: Container(height: 1.0,color: dividerColor,))
                                ],
                              ))),flex: 1,),
                    ],)),


              Padding(
                  padding: const EdgeInsets.only(bottom:13.0),
                  child:   Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(child:Image.asset(
                        "assets/images/change.png",
                        height: 23.0,
                        width: 23.0,
                      ) ,flex: 0,),
                      Expanded(child:InkWell(
                          onTap: () {
                            routeTo(context, ChangePassword());
                          },
                          child:Padding(
                              padding: const EdgeInsets.only(left: 13.0,right: 5),
                              child:  Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Change Password',style: drawerTextStyle,),
                                  Padding(
                                      padding: const EdgeInsets.only(top: 10.0),
                                      child: Container(height: 1.0,color: dividerColor,))
                                ],
                              ))),flex: 1,),
                    ],)),


              Padding(
                  padding: const EdgeInsets.only(bottom:13.0),
                  child:   Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(child:Image.asset(
                        "assets/images/logout.png",
                        height: 23.0,
                        width: 23.0,
                      ) ,flex: 0,),
                      Expanded(child:InkWell(
                          onTap: () async{
                            await prefs!.remove('kUserPwdKey');
                            SchedulerBinding.instance!.addPostFrameCallback((_) {
                              return routeToWithClear(context, LoginView());
                            });
                          },
                          child:Padding(
                              padding: const EdgeInsets.only(left: 13.0,right: 5),
                              child:  Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Logout',style: drawerTextStyle,),
                                  Padding(
                                      padding: const EdgeInsets.only(top: 10.0),
                                      child: Container(height: 1.0,color: dividerColor,))
                                ],
                              ))),flex: 1,),
                    ],)),



            ],
          ),
          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              text: '',
              style: TextStyle(color: textColor,fontSize: 16.0),
              children: <TextSpan>[
                TextSpan(
                  // text: ' 5hrs 27mins',
                  text: 'Terms and Conditions ',
                  style: TextStyle(color: welcomeTextColor,fontSize: 16.0),
                  recognizer:
                  TapGestureRecognizer()
                    ..onTap = () async{
                      await launch("https://myworklink.uk/legal-information/");
                    },
                ), TextSpan(
                  // text: ' 5hrs 27mins',
                  text: '&',
                  style: TextStyle( fontSize: 16.0),
                  recognizer:
                  TapGestureRecognizer()
                    ..onTap = () async{
                      await launch("https://myworklink.uk/legal-information/");
                    },
                ), TextSpan(
                  // text: ' 5hrs 27mins',
                  text: ' Privacy Policy',
                  style: TextStyle(color: welcomeTextColor,fontSize: 16.0),
                  recognizer:
                  TapGestureRecognizer()
                    ..onTap = () async{
                      await launch("https://myworklink.uk/privacy-policy/");
                    },
                ),
              ],
            ),
          ),

        ],
      ),
      )

    );
  }


}
