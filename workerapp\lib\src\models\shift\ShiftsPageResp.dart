
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../features/agencies/model/AgencyModel.dart';
import '../../features/calendar/modal/calendar_model.dart';
import '../../features/clients/model/ClientModel.dart';
import '../../features/clients/model/DirectorateModel.dart';
import '../../features/drawer/model/compliance_model.dart';
import '../../features/drawer/model/training_model.dart';
import '../Pageable.dart';
import '../Sort.dart';
import 'Shift.dart';

part 'ShiftsPageResp.freezed.dart';
part 'ShiftsPageResp.g.dart';

@freezed
abstract class ShiftsPageResp with _$ShiftsPageResp {

  factory ShiftsPageResp({
     List<Shift>? content,
     Pageable? pageable,
     int? totalPages,
     int? totalElements,
     bool? last,
     int? size,
     int? number,
     // Sort? sort,
     int? numberOfElements,
     bool? first,
     bool? empty,

  }) = _ShiftsPageResp;


  factory ShiftsPageResp.fromJson(Map<String, dynamic> json) =>
      _$ShiftsPageRespFromJson(json);

}
