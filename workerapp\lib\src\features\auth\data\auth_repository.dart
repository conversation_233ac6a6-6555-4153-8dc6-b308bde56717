/// connect to backend for logging in user / provider
import 'dart:ui';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/models/custom_exception.dart';
import 'package:work_link/src/utils/constants.dart';
import 'package:work_link/src/models/auth/login_response.dart';
import 'package:work_link/src/models/profile/worker_profile.dart';
import 'package:work_link/src/utils/UserPreference.dart';

import '../../../utils/index.dart';

abstract class IAuthRepository {
  Future login(Map<String, dynamic>? credentials, {bool rememberMe = false});

  Future forgotPaswd(String email);

  Future register(Map? payload);

  Future getTokenPair(Map<String, String>? credentials);

  Future getWorkerProfile(int id);

  Future refreshTokenPair();
}

class AuthRepository implements IAuthRepository {
  Dio _dioClient;

  final url = '';

  final  _reader;

  AuthRepository(this._reader) : _dioClient = _reader(dioProvider);

  @override
  Future getWorkerProfile(int id) async {
    final options = _reader(accessKeyOptionsProvider);
    try {
      final result = await _dioClient.get(
        '$dataService/api/v1/worker/$id',
        options: options,
      );

      if (result.statusCode == 200) {
        return WorkerProfile.fromJson(result.data);
      }

      else {
        throw Exception(
            'There was a problem getting profile. Please try again later');
      }
    }

    catch (e) {
      
      throw exceptionHandler(e, 'profile request',StackTrace.current);
    }
  }

  @override
  Future login(Map<String, dynamic>? credentials,
      {bool rememberMe = false}) async {
    // https://api-test.myworklink.uk/$authService/api/v1/user-permission/login
    try {
      // await _reader(sharedPreferencesServiceProvider).resetUserCredentials();

      final result = await _dioClient.post(
        '$authService/api/v1/user-permission/login',
        data: credentials,
      );

      // print("login responsew++++" + result.toString());

      if (result.statusCode == 200) {
        // save access token in response header
        final LoginResponse resp = LoginResponse.fromJson(result.data);

        print(resp.toString());
        SharedPreferences prefs = await SharedPreferences.getInstance();

        prefs.setString(UserPreference.accessToken, resp.accessToken!);

        if(resp.accessToken=="PASSWORD_NEEDS_RESET"){
          throw new CustomException(message: "PASSWORD_NEEDS_RESET", stackTrace: StackTrace.current);
        }

        prefs.setString(UserPreference.WORKER_ID,
            resp.workerId == null ? "" : resp.workerId.toString());

        prefs.setString(UserPreference.hascoId,
            resp.hascoId == null ? "" : resp.hascoId.toString());

        prefs.setString(UserPreference.agentId,
            resp.agentId == null ? "":resp.agentId.toString());
        prefs.setString(UserPreference.clientId,
            resp.clientId == null ? "":resp.clientId.toString());
        prefs.setString(UserPreference.firstName, resp.firstName.toString());
        prefs.setString(UserPreference.lastName, resp.lastName.toString());

        prefs.setString(UserPreference.id, resp.id.toString());




        _reader(authKeyProvider.notifier).state = resp.accessToken!;

        _reader(loginResponseProvider.notifier).state = resp;

        final WorkerProfile wp = await getWorkerProfile(resp.workerId!);

        prefs.setString(UserPreference.assignmentCodeId, wp.assignmentCode.toString());

        _reader(workerProfileProvider.notifier).state = wp;

        if (rememberMe) {
          await _reader(sharedPreferencesServiceProvider)
              .cacheUserCredentials(credentials!);
        }

        return wp;
      }
    }
    catch (e) {
      
      throw exceptionHandler(e, 'login request',StackTrace.current);
    }
  }

  @override
  Future register(Map? data) async {
    try {
      final result = await _dioClient.post('auth/users/', data: data);

      if (result.statusCode == 201) {
        // all good
        return WorkerProfile.fromJson(result.data);
      }

      // throw error
      else {
        throw Exception('Failed to register. Please try again later');
      }
    }

    //
    catch (e) {
      networkErrorHandler(e, 'register user');
    }
  }

  @override
  Future getTokenPair(Map<String, String>? credentials) async {
    try {
      final result = await _dioClient.post('users/login/', data: credentials);

      //print(result.data);

      if (result.statusCode == 200) {
        // all good
        Map<String?, String?> token = Map<String?, String?>.from(result.data);

        print(token);

        return credentials;
      }

      // throw error
      else {
        throw Exception('Failed to get access. Please try again later');
      }
    }

    //
    catch (e) {
      networkErrorHandler(e, 'login user');
    }
  }

  @override
  Future refreshTokenPair() async {
    try {
      final result = await _dioClient.post('auth/users/');

      if (result.statusCode == 201) {
        // all good
        return WorkerProfile.fromJson(result.data);
      }

      // throw error
      else {
        throw Exception('Failed to register. Please try again later');
      }
    }

    //
    catch (e) {
      networkErrorHandler(e, 'register user');
    }
  }

  void networkErrorHandler(Object e, String status) {
    if (e is DioError) {
      final DioError err = e;

      switch (err.type) {
        case DioErrorType.badResponse:
          final errorData = err.response?.data as Map;
          throw Exception(errorData.values.first.first);

        case DioErrorType.connectionTimeout:
          throw Exception('Connection Timeout. Try again');

        case DioErrorType.receiveTimeout:
          throw Exception(
              'Connection Timeout while loading, please try again to reload');

        case DioErrorType.sendTimeout:
          throw Exception('Connection Timeout. Try again');

        default:
          throw Exception('Failed to $status. Please try again');
      }
    }

    // else
    else {
      throw Exception('Failed to $status. Please try again later');
    }
  }

  @override

  /// {}
  Future forgotPaswd(String email) async {
    try {
      // final result = await _dioClient.get(url);
      final postUrl = "$userService/api/v1/user-management/user/resetPassword?email=$email";
      final result = await _dioClient.post(postUrl);


      if (result.statusCode == 200) {
        // all good
        print('Password reset success: ${(result.data)}');

        Map<String?, String?> message = Map<String?, String?>.from(result.data);

        return message;
      }

      // throw error
      else {
        print('Password reset failed: ${(result.data)}');
        print('Status code: ${result.statusCode}');

        throw Exception('Failed to reset password. Please try again later');
      }

      // throw error
      // else {
      //   throw Exception();
      // }
    }

    //
    catch (e) {
      throw Exception();
    }
  }
}
