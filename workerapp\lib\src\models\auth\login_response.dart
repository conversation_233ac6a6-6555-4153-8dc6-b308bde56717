import 'package:freezed_annotation/freezed_annotation.dart';

import 'role.dart';

part 'login_response.freezed.dart';
part 'login_response.g.dart';

@freezed
abstract class LoginResponse with _$LoginResponse {
  factory LoginResponse({
    int? agentId,
    int? hascoId,
    int? clientId,
    int? workerId,
    List<Role?>? roles,
    int? id,
    String? firstName,
    String? lastName,
    @J<PERSON><PERSON><PERSON>(name: "access_token") String? accessToken,
    @Json<PERSON>ey(name: "token_type") String? tokenType,
    @<PERSON><PERSON><PERSON><PERSON>(name: "refresh_token") String? refreshToken,
    String? scope,
    @<PERSON><PERSON><PERSON><PERSON>(name: "expires_in") String? expiresIn,
  }) = _LoginResponse;

  factory LoginResponse.fromJson(Map<String, dynamic> json) =>
      _$LoginResponseFromJson(json);
}
