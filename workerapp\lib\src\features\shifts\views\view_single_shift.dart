import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:line_icons/line_icons.dart';
import 'package:ndialog/ndialog.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';

import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/features/shifts/data/shift_category.dart';
import 'package:work_link/src/features/shifts/logic/shift_reason_input.dart';
import 'package:work_link/src/models/shift/ShiftsPageResp.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../../models/shift/Shift.dart';
import '../../../utils/constants.dart';
import '../../notificationns/logic/select_agency_input.dart';
import '../../notificationns/logic/select_agency_input_booking.dart';
import 'trailing_header_widget.dart';

class ViewShift extends StatelessWidget {
  ViewShift({Key? key, required this.shift, required this.status})
      : super(key: key);

  final formKey = GlobalKey<FormBuilderState>();

  final Shift shift;
  final ShiftCategoryStatus status;

  Widget shiftRowDetail(
    BuildContext context,
    String title1,
    String? msg1,
    String title2,
    String? msg2, {
    Widget third = const SizedBox.shrink(),
  }) {
    return Padding(
      padding: const EdgeInsets.only(top: 5, left: 5, right: 5),
      child: Row(
        children: [
          Expanded(
            child: ShiftTextEntryField(
              ctx: context,
              title: title1,
              initialText: msg1 ?? '',
            ),
          ),
          Expanded(
            child: ShiftTextEntryField(
              ctx: context,
              title: title2,
              initialText: msg2 ?? '',
            ),
          ),
          third,
        ],
      ),
    );
  }

  Widget footerWidget(
      BuildContext context, ShiftCategoryStatus status, Shift shift) {
    var w;

    switch (status.status) {
      case 'CANCELLED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // ElevatedButton(
                //   style: ElevatedButton.styleFrom(
                //     shape: RoundedRectangleBorder(
                //         borderRadius: BorderRadius.all( Radius.circular(4),),
                //         side: BorderSide(color: welcomeTextColor)
                //     ),
                //     backgroundColor:  Colors.white,
                //   ),
                //   onPressed: () => routeBack(context),
                //   child: Text('Back', style: TextStyle(color: welcomeTextColor),),
                // ),
              ],
            );
          }),
        );
        break;

      case 'NEW':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Consumer(builder: (context1, watch, child) {
              final dialog =watch.watch(dialogProvider);
              final shiftRepo =watch.watch(shiftRepoProvider);
              final _profile =watch.watch(loginResponseProvider);

              bool tt = shift.requireApplicationByWorkers ?? false;
              String sType = tt ? 'Apply' : 'Book';
              String plural = '${sType}ing';
              String ed = '${sType}ed';

              return Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // ElevatedButton(
                  //   style: ElevatedButton.styleFrom(
                  //     shape: RoundedRectangleBorder(
                  //         borderRadius: BorderRadius.all( Radius.circular(4),),
                  //         side: BorderSide(color: welcomeTextColor)
                  //     ),
                  //     backgroundColor:  Colors.white,
                  //   ),
                  //   onPressed: () => routeBack(context),
                  //   child: Text('Back', style: TextStyle(color:welcomeTextColor),),
                  // ),
                  SizedBox(width: sx(50)),
                  ElevatedButton.icon(
                    onPressed: () async {
                      // book a shift
                      if(tt){
                        selectAgencyInput(context, formKey, 'Apply', shift);

                      }else{
                        selectAgencyInputBooking(context, formKey, 'Book', shift);

                      }
                      // if(tt){
                      // }else{
                      //   final result = await ProgressDialog.future(
                      //     context,
                      //     dismissable: false,
                      //     future: tt
                      //         ? shiftRepo.applyAShift(
                      //       shiftId: shift.id!,
                      //       workerId: _profile.state!.workerId ?? 1,
                      //       agencyId: _profile.state!.agentId ?? 1,
                      //     )
                      //         : shiftRepo.bookAShift(
                      //       shiftId: shift.id!,
                      //       workerId: _profile.state!.workerId ?? 1,
                      //       agencyId: _profile.state!.agentId ?? 1,
                      //     ),
                      //     message: Text("$plural a shift..").textColor(textColor),
                      //     title: Text("$sType Shift").textColor(textColor),
                      //     //backgroundColor: Colors.white70,
                      //     onProgressError: (err) {
                      //       Navigator.pop(context);
                      //     },
                      //     onProgressCancel: () => Navigator.pop(context),
                      //   );
                      //
                      //   // check result
                      //
                      //   if (result is bool) {
                      //     // added ok
                      //     dialog.showFloatingFlushbar(
                      //       context: context,
                      //       title: '$sType Shift',
                      //       message: 'Shift has been $ed successfully.',
                      //     );
                      //   }
                      //
                      //   // err
                      //   else {
                      //     // added ok
                      //     dialog.showFloatingFlushbar(
                      //       context: context,
                      //       title: '$sType Shift',
                      //       message: result.message,
                      //       warning: true,
                      //     );
                      //   }
                      // }




                    },
                    icon: Icon(LineIcons.book),
                    label: Text('$sType Shift'),
                  ),
                ],
              );
            });
          }),
        );
        break;

      case 'AWAITING_AUTHORIZATION':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // ElevatedButton(
                //   style: ElevatedButton.styleFrom(
                //     shape: RoundedRectangleBorder(
                //         borderRadius: BorderRadius.all( Radius.circular(4),),
                //         side: BorderSide(color: welcomeTextColor)
                //     ),
                //     backgroundColor:  Colors.white,
                //   ),
                //   onPressed: () => routeBack(context),
                //   child: Text('Back', style: TextStyle(color: welcomeTextColor),),
                // ),
                // SizedBox(width: sx(50)),
              ],
            );
          }),
        );
        break;

      case 'BOOKED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // ElevatedButton(
                //   style: ElevatedButton.styleFrom(
                //     shape: RoundedRectangleBorder(
                //         borderRadius: BorderRadius.all( Radius.circular(4),),
                //         side: BorderSide(color: welcomeTextColor)
                //     ),
                //     backgroundColor:  Colors.white,
                //   ),
                //   onPressed: () => routeBack(context),
                //   child: Text('Back', style: TextStyle(color: welcomeTextColor),),
                // ),
                // SizedBox(width: sx(50)),
                ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: secondaryAccent,
                  ),
                  onPressed: () =>
                      shiftReasonInput(context, formKey, 'Cancel', shift),
                  icon: Icon(LineIcons.ban),
                  label: Text('Cancel ' +(shift.bookingType?.toCapitalized()??"Shift")),
                ),
              ],
            );
          }),
        );
        break;

      case 'APPLIED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // ElevatedButton(
                //   style: ElevatedButton.styleFrom(
                //     shape: RoundedRectangleBorder(
                //         borderRadius: BorderRadius.all( Radius.circular(4),),
                //         side: BorderSide(color: welcomeTextColor)
                //     ),
                //     backgroundColor:  Colors.white,
                //   ),
                //   onPressed: () => routeBack(context),
                //   child: Text('Back', style: TextStyle(color: welcomeTextColor),),
                // ),
                // SizedBox(width: sx(50)),
                ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: welcomeTextColor,
                  ),
                  onPressed: () =>
                      shiftReasonInput(context, formKey, 'Cancel', shift),
                  icon: Icon(LineIcons.ban),
                  label: Text('Cancel Shift'),
                ),
              ],
            );
          }),
        );
        break;

      case 'AUTHORIZED':
        w = Padding(
          padding: const EdgeInsets.only(bottom: 15, right: 20),
          child: RelativeBuilder(builder: (context, height, width, sy, sx) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // ElevatedButton.icon(
                //   onPressed: () => routeBack(context),
                //   icon: Icon(LineIcons.chevronLeft),
                //   label: Text('Back'),
                // ),
                // if(shift.released != true && shift.shiftStatus != 'BILLED')SizedBox(width: sx(50)),
                if(shift.released != true && shift.shiftStatus != 'BILLED')ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: secondaryAccent,
                  ),
                  onPressed: () => shiftReasonInput(
                      context, formKey, 'Query', shift,
                      isQuery: true),
                  icon: Icon(LineIcons.ban),
                  label: Text('Query Shift'),
                ),
              ],
            );
          }),
        );
        break;

      case 'IN_QUERY':
        w = RelativeBuilder(builder: (context, height, width, sy, sx) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 10),
              ShiftTextEntryField(
                ctx: context,
                title: 'QUERY',
                fieldHeight: 180,
                maxLines: 5,
                initialText: shift.queriedReason,
              ),
              Padding(
                padding: const EdgeInsets.only(bottom: 15, right: 20),
                child:
                    RelativeBuilder(builder: (context, height, width, sy, sx) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // ElevatedButton.icon(
                      //   onPressed: () => routeBack(context),
                      //   icon: Icon(LineIcons.chevronLeft),
                      //   label: Text('Back'),
                      // ),
                    ],
                  );
                }),
              ),
            ],
          );
        });
        break;

      default:
        w = SizedBox.shrink();
    }

    return w;
  }

  Widget headerWidget(){
    return Column(
      children: [
        SizedBox(height: 10),
        Center(
          child: Text(status.category)
              .fontWeight(FontWeight.bold)
              .fontSize(23),
        ),
        SizedBox(height: 10),
        Container(
          color: accentPrimaryColor,
          height: 50,
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text((shift.bookingType?.toCapitalized()??"Shift")+' Details:'),
                SizedBox(width: 8),
                if(shift.bookingType!='TRANSPORT')Text(shift.id.toString()).fontWeight(FontWeight.bold),
                Spacer(),
                trailingHeaderWidget(status, shift),
              ],
            ),
          ),
        ),
      ],
    );
  }


  Widget shiftBodyWidget( BuildContext context){
    return Column(
      children: [
        if(shift.bookingType=="TRAINING")
          Padding(
            padding: const EdgeInsets.only(top: 5, left: 5, right: 5),
            child: ShiftTextEntryField(
              ctx: context,
              title:'TRAINER',
              initialText: shift.trainer,
            ),
          ),


        if(shift.bookingType=="TRAINING")
          shiftRowDetail(
            context,
            'TRAINING',
            shift.trainingName,
            'LOCATION',
            shift.shiftLocation,
          ),


        shift.bookingType=="TRAINING" ?

        Padding(
            padding: const EdgeInsets.only(top: 5, left: 5, right: 5),
            child: ShiftTextEntryField(
              ctx: context,
              title:'ADDRESS',
              initialText: shift.directorate,
            ) ):
        shiftRowDetail(
          context,
          'SHIFT DIRECTORATE',
          shift.directorate,
          'LOCATION NAME',
          shift.shiftLocation,
        ),



        shiftRowDetail(
          context,
          'POSTCODE',
          shift.postCode,
          'PHONE NUMBER',
          shift.phoneNumber,

        ),
        shiftRowDetail(
            context,
            (shift.bookingType?.toUpperCase()??"SHIFT")+' DATE',
            dateFormat.format(shift.start!),
            (shift.bookingType?.toUpperCase()??"SHIFT")+" END",
            (shift.end!=null?dateFormat.format(shift.end!):'')

        ),
        shiftRowDetail(
          context,
          'START TIME',
          (shift.start!=null?timeFormat.format(shift.start!):''),
          'END TIME',
          (shift.end!=null?timeFormat.format(shift.end!):''),
          third: Expanded(
            child: ShiftTextEntryField(
              ctx: context,
              title: 'BREAK TIME',
              initialText: shift.breakTime?.toString()??'',
            ),
          ),
        ),

        if(shift.bookingType=="TRAINING") shiftRowDetail(
            context,
            'COST',
            '£'+(shift?.cost??''),
            "PAYER",
            shift.payer

        ),
        shift.showNoteToFw ?? false
            ? Padding(
          padding: EdgeInsets.only(bottom: 10),
          child: ShiftTextEntryField(
            ctx: context,
            title: 'NOTES',
            fieldHeight: 180,
            maxLines: 5,
            initialText: shift.notes,
          ),
        )
            : const SizedBox.shrink(),
      ],
    );
  }


  Widget transportBodyWidget( BuildContext context){
    return Column(
      children: [

        shiftRowDetail(
          context,
          'SHIFT DIRECTORATE BOY',
          shift.directorate,
          'LOCATION NAME',
          shift.shiftLocation,
        ),



        shiftRowDetail(
          context,
          'POSTCODE',
          shift.postCode,
          'PHONE NUMBER',
          shift.phoneNumber,

        ),
        shiftRowDetail(
            context,
            (shift.bookingType?.toUpperCase()??"SHIFT")+' DATE',
            dateFormat.format(shift.start!),
            (shift.bookingType?.toUpperCase()??"SHIFT")+" END",
            (shift.end!=null?dateFormat.format(shift.end!):'')

        ),
        shiftRowDetail(
          context,
          'START TIME',
          (shift.start!=null?timeFormat.format(shift.start!):''),
          'END TIME',
          (shift.end!=null?timeFormat.format(shift.end!):''),
          third: Expanded(
            child: ShiftTextEntryField(
              ctx: context,
              title: 'BREAK TIME',
              initialText: shift.breakTime?.toString()??'',
            ),
          ),
        ),

        if(shift.bookingType=="TRAINING") shiftRowDetail(
            context,
            'COST',
            '£'+(shift?.cost??''),
            "PAYER",
            shift.payer

        ),
        shift.showNoteToFw ?? false
            ? Padding(
          padding: EdgeInsets.only(bottom: 10),
          child: ShiftTextEntryField(
            ctx: context,
            title: 'NOTES',
            fieldHeight: 180,
            maxLines: 5,
            initialText: shift.notes,
          ),
        )
            : const SizedBox.shrink(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarDefault(
        context,shift.bookingType?.toLowerCase()=='transport'?"Secure Transport":shift.bookingType?.toCapitalized()??"Shift",
        leading: IconButton(
          onPressed: () => routeBack(context),
          icon: Icon(
            Icons.chevron_left,
            color: welcomeTextColor,
            size: 35,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: RelativeBuilder(builder: (context, height, width, sy, sx) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              headerWidget(),
              shiftBodyWidget(context),
              footerWidget(context, status, shift),
            ],
          );
        }),
      ),
    );
  }
}
