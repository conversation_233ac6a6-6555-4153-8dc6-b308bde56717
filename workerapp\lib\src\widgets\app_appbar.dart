import 'package:badges/badges.dart' as bg;
import 'package:flutter/material.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/features/profile/views/profile_view.dart';
import 'package:work_link/src/utils/index.dart';

import '../features/notificationns/views/notifications_home_view.dart';

PreferredSizeWidget? AppAppBar(BuildContext context, {Widget? leading}) {
  return AppBar(
    backgroundColor: Colors.white,
    leading: leading,
    title: Padding(
      padding: EdgeInsets.symmetric(horizontal: 60),
      child: Image.asset(
        'assets/images/logo.png',
        height: kToolbarHeight * 0.7,
      ),
    ),
    centerTitle: true,
    actions: [
      Padding(
        padding: const EdgeInsets.only(right: 13.0,top: 8),
        child: IconButton(
          onPressed: () => routeTo(context, NotificationsHomeView()),
          icon: Icon(
            Icons.announcement,
            color: welcomeTextColor,
          ),),
      ),
      /*IconButton(
        onPressed: () => routeTo(context, ProfileView()),
        icon: Icon(
          Icons.person_pin,
          color: welcomeTextColor,
        ),
      ),*/
    ],
  );
}
