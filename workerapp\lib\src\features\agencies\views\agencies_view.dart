import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/models/custom_exception.dart';
import 'package:work_link/src/models/worker-agencies/worker_agency.dart';

import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

final _agenciesProvider = AutoDisposeFutureProvider(
    (ref) => ref.watch(dataRepoProvider).getWorkerAgencies());

class AgenciesView extends ConsumerWidget {
  AgenciesView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context,  watch) {
    final agencies = watch.watch(_agenciesProvider);

    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: deepBlueColor,
          elevation: 1.0,
          centerTitle: true,
          leading: Container(
            height: 30,
            width: 30,
            child: Padding(
              padding: const EdgeInsets.only(left: 15.0, right: 15),
              child: InkWell(
                child: Icon(   Icons.arrow_back,
                  color: welcomeTextColor,
                  size: 20,
                ),
                onTap: () {
                  routeBack(context);
                },
              ),
            ),
          ),
          title: Text(
            'Agencies',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w700,
              fontSize: 20.0,
            ),
          ),
        ),


        body: RelativeBuilder(builder: (context, height, width, sy, sx) {
          return agencies.when(
            data: (ag) {
              //  print(ag);
              if (ag != null) {
                return ag.content!.isNotEmpty
                    ? ListView.separated(
                        padding: EdgeInsets.all(sx(20)),
                        itemBuilder: (_, index) {
                          Content wa = ag.content![index];

                          return ListTile(
                            leading: CircleAvatar(
                              child: Icon(Icons.groups),
                            ),
                            tileColor: tileColor,
                            title: Text(wa.name!).fontWeight(FontWeight.w600),
                            subtitle: Text(wa.email!),
                          );
                        },
                        separatorBuilder: (_, x) => SizedBox(height: sy(15)),
                        itemCount: ag.content!.length,
                      )
                    : Center(
                        child:
                            Text('You do not have any available agencies yet!'),
                      );
              }

              return ErrorPage(
                error: 'Failed to get your linked Agencies', 
                    stackTrace: StackTrace.current,
                onTryAgain: () => watch.refresh(_agenciesProvider),
              );
            },
            loading: () => Center(
              child: CircularProgressIndicator(),
            ),
            error: (e, st) => ErrorPage(
              error: e is CustomException ? e.message : e.toString(),
              stackTrace: st,onTryAgain: () => watch.refresh(_agenciesProvider),
            ),
          );
        }),
      ),
    );
  }
}
