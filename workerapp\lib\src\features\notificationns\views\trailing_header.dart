import 'package:flutter/material.dart';
import 'package:work_link/src/features/shifts/data/shift_category.dart';

Widget trailingHeading(ShiftCategoryStatus status) {
  var w;

  switch (status.status) {
    case 'NEW':
      w = Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: const EdgeInsets.only(right: 15),
          child: Text('View\t\t\tBook\t\t\t\tApply'),
        ),
      );
      break;

    case 'CANCELLED':
      w = Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: const EdgeInsets.only(right: 25),
          child: Text('View\t\t\t\t\t\t\t\t\tStatus'),
        ),
      );
      break;

    case 'AWAITING_AUTHORIZATION':
      w = Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: const EdgeInsets.only(right: 25),
          child: Text('View'),
        ),
      );
      break;

    case 'BOOKED':
      w = Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: const EdgeInsets.only(right: 23),
          child: Text('View\t\t\t\t\t\t\t\t\t\t\t\tCancel'),
        ),
      );
      break;

    case 'APPLIED':
      w = Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: const EdgeInsets.only(right: 25),
          child: Text('View\t\t\tCancel'),
        ),
      );
      break;

    case 'AUTHORIZED':
      w = Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: const EdgeInsets.only(right: 24),
          child: Text('View\t\t\tQuery\t\t\tRelease'),
        ),
      );
      break;

    case 'IN_QUERY':
      w = Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: const EdgeInsets.only(right: 25),
          child: Text('View'),
        ),
      );
      break;

    default:
      w = SizedBox.shrink();
  }

  return w;
}
