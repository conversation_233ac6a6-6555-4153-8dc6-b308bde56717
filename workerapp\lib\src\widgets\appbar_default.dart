import 'package:badges/badges.dart' as bg;
import 'package:flutter/material.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/features/profile/views/profile_view.dart';
import 'package:work_link/src/utils/index.dart';

import '../features/notificationns/views/notifications_home_view.dart';

PreferredSizeWidget? AppBarDefault(BuildContext context, String title, {Widget? leading, List<Widget>? actions}) {
  return AppBar(
    backgroundColor: Colors.white,
    leading: leading,
    shadowColor: Colors.transparent,
    title: Text(title, style: TextStyle(color: welcomeTextColor),),
    centerTitle: true,
      bottom: PreferredSize(
          preferredSize: const Size.fromHeight(4.0),
          child: Container(
            color: welcomeTextColor,
            height: 4.0,
          ),
      ),
    actions: actions??[],
  );
}
