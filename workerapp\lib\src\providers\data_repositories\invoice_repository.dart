import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/utils/constants.dart';
import '../../utils/UserPreference.dart';
import '../../models/invoice/invoice.dart';
import '../../models/profile/worker_profile.dart';
import '../../models/shift_controller_shift_model.dart';

final invoiceProvider = AutoDisposeFutureProviderFamily<Invoice?, int>((ref, invoiceId) {final _payslip = ref.watch(invoiceRepoProvider);
  return _payslip.getInvoice(invoiceId);
});

class InvoiceRepository {
  Dio _dioClient;
  final _reader;
  InvoiceRepository(this._reader) : _dioClient = _reader(dioProvider);


  Future<Invoice?> getInvoice(int invoiceId) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    int workerId= int.parse(prefs.getString(UserPreference.WORKER_ID)??"1");
    WorkerProfile worker =  await getWorkerProfile(workerId);

    final options = _reader(accessKeyOptionsProvider);
    final result = await _dioClient.get(
        '$dataService/api/v1/invoice/$invoiceId',
        options: options);



    if (result.statusCode == 200) {
      Invoice res =  Invoice.fromJson(result.data);

      res =  res.copyWith(payer: worker);
      res = res.copyWith( payee: await getAgency(res.agentId??1));

      return res;
    } else throw Exception('Error getting invoice. Try again later');

  }


  Future<WorkerProfile> getWorkerProfile(int id) async {
    final options = _reader(accessKeyOptionsProvider);

    final result = await _dioClient.get(
      '$dataService/api/v1/worker/$id',
      options: options,
    );

    if (result.statusCode == 200) {
      return WorkerProfile.fromJson(result.data);
    }

    // throw error
    else {
      throw Exception(
          'There was a problem getting profile. Please try again later');
    }
  }




  Future<Agency> getAgency(int id) async {
    final options = _reader(accessKeyOptionsProvider);

    final result = await _dioClient.get(
      '$dataService/api/v1/agency/$id',
      options: options,
    );

    if (result.statusCode == 200) {
      return Agency.fromMap(
          result.data
      );
    }

    // throw error
    else {
      throw Exception(
          'There was a problem getting profile. Please try again later');
    }
  }

}


