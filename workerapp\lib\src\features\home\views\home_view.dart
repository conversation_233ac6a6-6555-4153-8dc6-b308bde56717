import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/features/home/<USER>/training_feedback_form.dart';
import 'package:work_link/src/features/profile/views/profile_view.dart';
import 'package:work_link/src/features/shift-filter/views/shifts_home_view.dart';
import 'package:work_link/src/features/shifts/views/shifts_home_view.dart';
import 'package:work_link/src/models/training_feedback/TrainingFeedback.dart';
import 'package:work_link/src/models/worker_training_session/WorkerTrainingSession.dart';
import 'package:work_link/src/providers/data_repositories/worker_training_session_repository.dart';
import 'package:work_link/src/providers/data_repositories/data_repository.dart';
import 'package:work_link/src/utils/dialog_service.dart';

import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../../utils/UserPreference.dart';
import '../../../widgets/drawer_common.dart';
import '../../../utils/responsive.dart';
import '../../agencies/views/agency_view.dart';
import '../../clients/views/client_viewnew.dart';
import '../../payrol/view/payrol_base.dart';
import '../../trainings/training_info_widget.dart';



class HomeCardItem {
  final IconData icon;
  final String title;
  final Widget? routeTo;
  final bool isLogout;

  HomeCardItem({
    this.isLogout = false,
    required this.icon,
    required this.title,
    this.routeTo,
  });
}

final List<HomeCardItem> items = [
  HomeCardItem(
    icon: Icons.apps_outlined,
    title: 'Bookings',
    routeTo: ShiftsHomeView(),
    //routeTo: ShiftsHomeFilterView(),
  ),
  HomeCardItem(
    icon: Icons.payments_outlined,
    title: 'Payroll',
    routeTo: payrol(),
  ),
  HomeCardItem(
    icon: Icons.book,
    title: 'Trainings & Qualifications',
    routeTo: TrainingInfoWidget(),
  ),
  HomeCardItem(
    icon: Icons.person,
    title: 'Profile',
    routeTo: CommonDrawer(),
  ),
  HomeCardItem(
    icon: Icons.groups,
    title: 'Businesses',
    routeTo: AgencyView(),
  ),
  HomeCardItem(
    icon: Icons.supervisor_account_outlined,
    title: 'Clients',
    routeTo: ClientViewNew(),
  ),

  // HomeCardItem(
  //   icon: Icons.logout,
  //   title: 'Logout',
  //   routeTo: SplashView(),
  //   isLogout: true,
  // ),
];




class HomeView extends ConsumerStatefulWidget {
  @override
  HomeViewState createState() => HomeViewState();
}

class HomeViewState extends ConsumerState<HomeView> {
  final GlobalKey<ScaffoldState> _scaffoldKey =  GlobalKey<ScaffoldState>();

  String workerName = "";
  activateNotifications() async{

  }


  init() async {

    SharedPreferences prefs = await SharedPreferences.getInstance();
    workerName=  prefs.getString(UserPreference.firstName)??"";
    var afterLogin =  prefs.getBool(UserPreference.afterLogin)??true;

    // try {
      final fcmToken = await FirebaseMessaging.instance.getToken();
      registerDevice(fcmToken: fcmToken);
    // }catch(e){}

    if(!kIsWeb){
      await Permission.notification.isDenied.then((value) async {

        if (value && afterLogin==true) {
          prefs.setBool(UserPreference.afterLogin, false);

          bool isShown = await Permission.notification.shouldShowRequestRationale;
          Permission.notification.request();

          var res = await showDialog<bool?>(
              context: context,
              builder: (ctx) {
                return AlertDialog(
                  contentPadding : const EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 0.0),
                  content: Container(
                    height: 185,
                    child: Column(
                      children: [
                        Container(
                            height: 40,
                            width: double.infinity,
                            color: welcomeTextColor,
                            child: Padding(
                              padding: const EdgeInsets.only(top:10.0),
                              child: Text('Notifications',textAlign: TextAlign.center,style: TextStyle(fontSize: 14,color: Colors.white,fontWeight: FontWeight.w500),),
                            )),
                        Padding(
                            padding: const EdgeInsets.only(top:25.0,bottom: 25, left: 10, right: 10),
                            child:   Text('Notifications are currently disabled?. Allow notifications and never miss a shift.')),

                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [


                            ElevatedButton.icon(
                              label: Text('OK'),
                              icon: Icon(Icons.done),

                              style: ElevatedButton.styleFrom(
                                backgroundColor: welcomeTextColor,
                              ), onPressed: () {
                              Navigator.pop(context, false);

                            },
                            ),
                          ],)
                      ],
                    ),
                  ),

                );
              });

          print(res);

        }
      });
    }


    setState(() {

    });
  }

  bool isFirstBuild = true;
  void initState() {
    // TODO: implement initState
    super.initState();
    ref.refresh(trainingFeedBackProvider);

    activateNotifications();
    init();
  }


  @override
  Widget build(BuildContext context) {
    // final profile =watch.watch(workerProfileProvider);





    _feedbackForm(WorkerTrainingSession booking){

      return showDialog<bool?>(
          context: context,
          builder: (ctx) {
            return AlertDialog(
              contentPadding : const EdgeInsets.all(0),
              content: Container(
                child: SingleChildScrollView(
                  child: TrainingFeedbackForm(booking),
                ),
              ),

            );
          });
    }


    _feebackFormAlert(WorkerTrainingSession booking){
      return showDialog<bool?>(
          context: context,
          builder: (ctx) {
            return AlertDialog(
              contentPadding : const EdgeInsets.fromLTRB(0.0, 0.0, 0.0, 0.0),
              content: Container(

                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Container(
                          width: double.infinity,
                          color: welcomeTextColor,
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: Text('Training feedback',textAlign: TextAlign.center,style: TextStyle(fontSize: 14,color: Colors.white,fontWeight: FontWeight.w500),),
                          )),
                      Padding(
                          padding: const EdgeInsets.only(top:35.0,bottom: 30),
                          child:  Column(
                            children: [
                              Text('May you kindly share feedback on:\n'
                                  +(booking.trainingSession?.training?.name??'')+'\n'
                                  'that you attended in ${booking.trainingSession?.shiftLocation?.name??""} on\n'
                                  +(booking.trainingSession?.startDateTime??'')+' \n'
                                  , overflow: TextOverflow.ellipsis ,),

                            ],
                          )
                      ),

                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [

                          Padding(
                            padding: const EdgeInsets.only(right:15.0),
                            child: ElevatedButton.icon(
                              icon: Icon(Icons.cancel_outlined),
                              label: Text('No'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: denyRed,
                              ),
                              onPressed: () {
                                // TODO process
                                Navigator.pop(context, false);
                              },
                            ),
                          ),
                          ElevatedButton.icon(
                            label: Text('Yes'),
                            icon: Icon(Icons.done),

                            style: ElevatedButton.styleFrom(
                              backgroundColor: welcomeTextColor,
                            ),
                            onPressed: ()
                            async {

                              Navigator.of(context).pop();
                              // context.pop();
                              _feedbackForm(booking);

                            },
                          ),

                        ],),

                      SizedBox( height: 10)
                    ],
                  ),
                ),
              ),

            );
          });
    }



    return SafeArea(
      child: RelativeBuilder(builder: (context, height, width, sy, sx) {
        return Scaffold(

          key: _scaffoldKey,
          appBar: AppAppBar(context ),

          body: Center(
            child: Container(
              constraints: BoxConstraints(minWidth: 200, maxWidth: 600),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    Center(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Text('Hi, ${workerName}')
                            .textColor(welcomeTextColor)
                            .fontWeight(FontWeight.w600)
                            .fontSize(Responsive.isMobile(context)?sx(35): sx(15)),
                      ),
                    ),
                    Consumer(builder: (context1, watch, child) {

                      return watch.watch(trainingFeedBackProvider).when(
                          data: (data){
                            if(data!=null && isFirstBuild){
                              isFirstBuild = false;
                              SchedulerBinding.instance!.addPostFrameCallback((_) {
                                _feebackFormAlert(data);
                              });
                            }
                            return SizedBox();
                          },
                          loading: () =>
                              SizedBox(),
                          error: (e, st) {
                            print(e.toString());
                            return SizedBox();
                          });
                    }),

                    Expanded(
                      child: GridView.count(
                        // childAspectRatio: 0.8,
                        crossAxisCount: 2,
                        mainAxisSpacing: 10,
                        crossAxisSpacing: 10,
                        padding: EdgeInsets.all(20),
                        children: items
                            .map(
                              (e) => GestureDetector(
                            onTap: () {
                              if (e.isLogout) {
                                routeBackWithClear(context);
                              } else if (e.routeTo != null) {
                                routeTo(context, e.routeTo!);
                              }
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: welcomeTextColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child:  Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // SizedBox(height: 20,),
                                    Icon(e.icon)
                                        .iconColor(Colors.white)
                                        .iconSize((Responsive.isMobile(context))?sx(60):(Responsive.isTablet(context))?sx(40):sx(20)),
                                    // SizedBox(height: 10,),
                                    Padding(
                                      padding: EdgeInsets.symmetric(horizontal: 5),
                                      child: Text(e.title, textAlign: TextAlign.center ,style: (TextStyle(overflow: TextOverflow.visible)),)
                                        .textColor(Colors.white)
                                        .fontSize((Responsive.isMobile(context))?sx(22): sx(12))
                                    ),
                                  ],
                                ),

                            ),
                          ),
                        )
                            .toList(),
                      ),
                    ),

                    // Column(
                    //   children: [
                    //     SmallFaqWidget(limitFaqs: true,),
                    //
                    //
                    //   ],
                    // ),



                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }
}

