import 'package:flutter/material.dart';

const primaryColor = Color(0xFF2697FF);
//const secondaryColor = Color(0xFF2A2D3E);
//const bgColor = Color(0xFF212132);

const secondaryColor = Colors.white;
const bgColor = Colors.white;
const inputBgColor = Color(0xFFDCDCDC);
const darkgreenColor = Color(0xFF2c614f);
const greenColor = Color(0xFF6bab58);

const defaultPadding = 15.0;
const double defaultBorderRadius = 5;
const double smallBorderRadius = 3;

class ColorConstants {
  static Color blue = Color(0xFF0D46BB);
}

class Palette {
  static const Color background = Color(0xFFEDEEF2);
  static const Color wrapperBg = Colors.white;
}
