
import 'package:freezed_annotation/freezed_annotation.dart';
 import '../../../features/agencies/model/AgencyModel.dart';
import '../../Pageable.dart';
import '../invoice.dart';

part 'invoice_result.freezed.dart';
part 'invoice_result.g.dart';

@freezed
abstract class InvoiceResult with _$InvoiceResult {

  factory InvoiceResult({



    List<Invoice>? content,
    bool? empty,
    bool? first,
    bool? last,
    int? number,
    int? numberOfElements,
    Pageable? pageable,
    int? size,
    Sort? sort,
    int? totalElements,
    int? totalPages,


  }) = _InvoiceResult;



  factory InvoiceResult.fromJson(Map<String, dynamic> json) =>
      _$InvoiceResultFromJson(json);

}

