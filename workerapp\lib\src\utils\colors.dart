import 'package:flutter/material.dart';

final Color background = Color(0xFFF3F3F3);
final Color buttonBgColor1 = Color(0xFF35D1E0);
final Color buttonBgColor2 = Color(0xFF0077E3);
final Color deepBlueColor = Color(0xFF2144C4);

final Color welcomeTextColor = Color.fromRGBO(5, 69, 156, 1);
final Color orangeAccent = Color.fromRGBO(241, 180, 45, 1);
final Color shiftTeal = Color.fromRGBO(0, 196, 189, 1.0);
final Color welcomeTextColorDark = Color.fromRGBO(4, 37, 80, 1.0);
final Color accentPrimaryColor = Color.fromRGBO(223, 240, 255, 1);
final Color dividerColor = Colors.grey.shade100;
final Color lightgreyColor = Colors.grey.shade600;
final Color loginButonColor = Color.fromRGBO(5, 69, 156, 1);
final Color denyRed = Color.fromRGBO(255, 0, 140, 1);
final Color secondaryAccent = Color.fromRGBO(255, 0, 140, 1);

final Color confirmGreen = Color(0xFF41D468);

final Color homeCardColor = Color(0xFF436EF9);
final Color logoBlue = Color(0xFF1D7BB7);
final Color lightBlue = Color(0xff005DFF);

final textColor = Colors.black54;
final blackColor = Colors.black;

final Color tileColor = Color(0xFFD1F5F7);

final Color shiftDetailsColor = Color(0xFFECE8E8);

final Color shiftFieldDetailsColor = Color(0xFFB6EEF2);

