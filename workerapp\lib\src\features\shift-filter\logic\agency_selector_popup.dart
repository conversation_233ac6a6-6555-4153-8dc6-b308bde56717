import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/models/custom_exception.dart';
import 'package:work_link/src/models/worker-agencies/worker_agency.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

final _agencyProvider = AutoDisposeFutureProvider((ref) {
  final api = ref.read(dataRepoProvider);

  return api.getWorkerAgencies();
});

agencySelector(BuildContext context) async {
  return await showMaterialModalBottomSheet(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(30),
        topRight: Radius.circular(30),
      ),
    ),
    context: context,
    useRootNavigator: true,
    builder: (context) => Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: SingleChildScrollView(
        controller: ModalScrollController.of(context),
        child: Consumer(
          builder: (context1, watch, child) {
            final api =watch.watch(_agencyProvider);

            return RelativeBuilder(builder: (context, height, width, sy, sx) {
              return Container(
                height: 300,
                width: width,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: const Radius.circular(20.0),
                    topRight: const Radius.circular(20.0),
                  ),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    //  mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.all(15.0),
                        child: Text('Select Agency')
                            .textColor(Colors.black54)
                            .fontSize(sx(25)),
                      ),
                      api.when(
                        data: (data) {
                          final WorkerAgency wa = data;

                          return wa.content!.isEmpty
                              ? Center(
                                  child: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 50),
                                  child: Text('no matched agency found')
                                      .textColor(Colors.grey),
                                ))
                              : ListView.separated(
                            physics: NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemBuilder: (ctx, index) {
                              var item = wa.content![index];

                              return ListTile(
                                onTap: () => Navigator.pop(context, {
                                  "id": item.agencyId!,
                                  "name": item.name!,
                                }),
                                leading: CircleAvatar(
                                  child: Icon(Icons.groups),
                                ),
                                tileColor: tileColor,
                                title: Text(item.name!)
                                    .fontWeight(FontWeight.w600),
                                subtitle: Text(item.service!),
                              );
                            },
                            separatorBuilder: (ctx, x) => Divider(height: 10),
                            itemCount: wa.content!.length,
                          );
                        },
                        loading: () => Center(
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: CircularProgressIndicator(),
                          ),
                        ),
                        error: (e, st) {
                          if (e is CustomException) {
                            return ErrorPage(
                              error: e.message,
                              stackTrace: st,
                              onTryAgain: () =>
                                  watch.refresh(_agencyProvider),
                            );
                          } else {
                            return ErrorPage(
                              stackTrace: st,
                              error: 'failed to get agencies',
                              onTryAgain: () =>
                                  watch.refresh(_agencyProvider),
                            );
                          }
                        },
                      ),
                    ],
                  ),
                ),
              );
            });
          },
        ),
      ),
    ),
  );
}
