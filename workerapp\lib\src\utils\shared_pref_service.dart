import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'UserPreference.dart';

final sharedPreferencesServiceProvider =
    Provider<SharedPreferencesService>((ref) => throw UnimplementedError());

class SharedPreferencesService {
  SharedPreferencesService(this.sharedPreferences);
  final SharedPreferences sharedPreferences;

  static const onboardingCompleteKey = 'onboardingComplete';
  static const kUserPwdKey = 'kUserPwdKey';

  Future<void> setOnboardingComplete() async {
    await sharedPreferences.setBool(onboardingCompleteKey, true);
  }

  Future<void> cacheUserCredentials(Map<String, dynamic> auth) async {
    await sharedPreferences.setString(kUserPwdKey, json.encode(auth));
  }

  Future<void> resetUserCredentials() async {
    await sharedPreferences.remove(kUserPwdKey);

    await sharedPreferences.remove(UserPreference.WORKER_ID);
    await sharedPreferences.remove(UserPreference.firstName);
    await sharedPreferences.remove(UserPreference.lastName);
    await sharedPreferences.remove(UserPreference.agentId);
    await sharedPreferences.remove(UserPreference.accessToken);
    await sharedPreferences.remove(UserPreference.clientId);
    await sharedPreferences.remove(UserPreference.id);
    await sharedPreferences.remove(UserPreference.afterLogin);
    await sharedPreferences.remove(UserPreference.assignmentCodeId);
    await sharedPreferences.clear();
  }

  Map<String, dynamic>? getCachedUserCredentials() {
    final res = sharedPreferences.getString(kUserPwdKey);

    if (res != null) {
      return json.decode(res) as Map<String, dynamic>;
    }

    return null;
  }

  String? getAccessToken() {
    final res = sharedPreferences.getString(UserPreference.accessToken);

    if (res != null) {
      return res;
    }

    return null;
  }

  bool isOnboardingComplete() =>
      sharedPreferences.getBool(onboardingCompleteKey) ?? false;
}
