import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:work_link/src/features/payrol/view/pay_advice_row.dart';

import '../../../providers/app_providers.dart';
import '../../../models/custom_exception.dart';
import '../../../models/payadvice/payadvices_response.dart';
import '../../../widgets/error_page.dart';

final _payslipProvider =
AutoDisposeFutureProviderFamily<PayAdvicesResponse?, String>(
        (ref, status) {
      final _payslip = ref.watch(payslipRepoProvider);

      return _payslip.getPayAdvices();
    });

class PayAdvicesWidget extends ConsumerWidget {

  PayAdvicesWidget({Key? key}) : super();

  @override
  Widget build(BuildContext context,   watch) {
    return Column(
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 13.0, left: 10, right: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 9.0),
                    child: Text(
                      "PAY DATE",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 13,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  flex: 1,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 9.0),
                    child: Text(
                      "TOTAL HRS",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 13,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  flex: 1,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 9.0),
                    child: Text(
                      "GROSS PAY",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 13,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  flex: 1,
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 9.0, right: 10),
                    child: Text(
                      "VIEW",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 13,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  flex: 0,
                ),
              ],
            ),
          ),
          flex: 0,
        ),
       watch.watch(_payslipProvider('payslip')).when(
          data: (PayAdvicesResponse? value) {
            if (value != null) {
              final shifts = value.content;

              if (shifts == null) {
                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      'failed to get notifications',
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                );
              }

              return shifts.isEmpty
                  ? Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    'no payslips available',
                    style: TextStyle(
                      fontStyle: FontStyle.italic,
                      color: Colors.grey,
                    ),
                  ),
                ),
              )
                  :
              Column(
                children: [
                  // trailingHeading(status),
                  SingleChildScrollView(

                    child: RefreshIndicator(
                      onRefresh: () async {
                        await watch.refresh(_payslipProvider('payslip'));
                      },
                      child: Container(
                        padding: EdgeInsets.all(10),
                        child:ListView.builder(
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          final Content shift = shifts[index]!;

                          return
                            Container(
                              color: index%2==0?Color(0xffE6E6E6):Colors.white,
                              padding: EdgeInsets.only(left: 5, bottom: 5),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,

                                children: [
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 9.0),
                                      child: Text(
                                        shift.payAdviceDate ?? 'Not Specified',
                                        style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                    flex: 1,
                                  ),
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 9.0, left: 5),
                                      child: Text(
                                        double.parse(shift.totalHrs??'0').toStringAsFixed(2) ?? 'Not specified',
                                        style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                    flex: 1,
                                  ),
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 9.0, left: 5),
                                      child: Text(
                                        shift.totalAmount?.toStringAsFixed(2) ?? 'Not Specified',
                                        style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                    flex: 1,
                                  ),
                                  PayAdvicesRow(shift: shift,),
                                ],
                              ),
                            );
                        },
                        // separatorBuilder: (_, x) => SizedBox(height: 20),
                        itemCount: shifts.length,
                      ),),
                    ),
                  ),

                  Center(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 10, bottom: 20),
                      child: Text(
                        'pull down to refresh',
                        style: TextStyle(
                          fontStyle: FontStyle.italic,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                ],
              );
            }
            // err
            else {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    'failed to get payslips',
                    style: TextStyle(
                      fontStyle: FontStyle.italic,
                      color: Colors.grey,
                    ),
                  ),
                ),
              );
            }
          },
          loading: () => Center(
            child: CircularProgressIndicator(),
          ),
          error: (e, st) => Center(
            child: ErrorPage(
              error: e is CustomException ? e.message : e.toString(),
              stackTrace: st,onTryAgain: () => watch.refresh(_payslipProvider('payslip')),
            ),
          ),
        ),


      ],
    );
  }
}
