import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:work_link/src/models/carpooling_workers/CarPoolingWorker.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/utils/constants.dart';
import 'package:work_link/src/features/shift-filter/data/filter_model.dart';
import 'package:work_link/src/models/agency_expense_rate/AgencyExpenseRate.dart';
import 'package:work_link/src/models/filter-shift/filtered_shift.dart';
import 'package:work_link/src/models/shift/ShiftsPageResp.dart';

import '../../models/shift/Shift.dart';
import '../../utils/UserPreference.dart';
import '../../utils/dio_client.dart';
import '../../utils/index.dart';



class ShiftRepository {
  Dio _dioClient;
  final   _reader;
  ShiftRepository(this._reader) : _dioClient = _reader(dioProvider);


  Future<ShiftsPageResp?> getShiftByStatus({int workerId = 1, String status = 'NEW',}) async {
    final options = _reader(accessKeyOptionsProvider);

    bool isApplied = (status == 'APPLIED');
    String status_ = isApplied ? 'NEW' : status;

    if(isApplied){
      try {
        final result = await _dioClient.get('$dataService/api/v1/worker-applied-shifts/$workerId/0/100', options: options);

        if (result.statusCode == 200) {
          var l = result.data as List;


          if (l.isEmpty) {
            return ShiftsPageResp(content: []);
          }

          final List<Map<String, dynamic>> shiftsR = List.from(l);


          List<Shift> shifts = shiftsR.map((e) => Shift.fromJson(e)).toList();

          var activeShifts = shifts.where((sh) {
            // print(sh.toJson());
            if (sh.shiftStatus == null) {
              return true;
            }

            // bool aa = (sh.shiftStatus?.toLowerCase() != 'expired' &&
            //     sh.appliedStatus == null);
            bool aa = sh.shiftStatus?.toLowerCase() != 'expired';

            return aa;
          }).toList();


          if (isApplied) {
            final appliedShifts = activeShifts.where((sh) {
              return sh.appliedStatus != null || sh.bookingType== 'TRANSPORT';
            }).toList();

            return ShiftsPageResp(content: appliedShifts);
          }

          return ShiftsPageResp(content: activeShifts);
        }

        // throw error
        else {
          throw Exception(
              'Error getting available $status shifts. Try again later');
        }
      }
      catch (e, stacktrace) {
        // 
        throw exceptionHandler(e, 'worker $status shifts', stacktrace);
      }
    }else{
            try {
        final result = await _dioClient.get(
            '$dataService/api/v1/worker-agency-shift-status/$workerId/0/100/$status_',
            options: options);
        if (result.statusCode == 200) {
          var l = result.data as List;
          if (l.isEmpty) {
            return ShiftsPageResp(content: []);
          }
          final List<Map<String, dynamic>> shiftsR = List.from(l);
          List<Shift> shifts = shiftsR.map((e) => Shift.fromJson(e)).toList();

          var activeShifts = shifts.where((sh) {
            if (sh.shiftStatus == null) {
              return true;
            }
            bool aa = sh.shiftStatus?.toLowerCase() != 'expired';

            return aa;
          }).toList();

          if (isApplied) {
            final appliedShifts = activeShifts.where((sh) {
              return sh.appliedStatus != null;
            }).toList();

            return ShiftsPageResp(content: appliedShifts);
          }

          return ShiftsPageResp(content: activeShifts);
        }

        // throw error
        else {
          throw Exception(
              'Error getting available $status shifts. Try again later');
        }
      }
      catch ( e, stacktrace) {
        throw exceptionHandler(e, 'worker $status shifts',stacktrace);
      }
    }
  }


  Future<List<AgencyExpenseRate>?> getAgencyExpenseRates(int agencyId) async {
    final options = _reader(accessKeyOptionsProvider);

      try {
        final result = await Dio().get(
            '$dataService/api/v1/agency/expense-rate/$agencyId',
            options: options);


        if (result.statusCode == 200) {
          var l = result.data as List;

          if (l.isEmpty) return [];

          final List<Map<String, dynamic>> rates = List.from(l);

          return rates.map((e) => AgencyExpenseRate.fromJson(e)).toList();
        }

        else {
          throw Exception(
              'Error getting available shifts. Try again later');
        }
      }
      catch (e) {
        
        throw exceptionHandler(e, 'worker  shifts',StackTrace.current);
      }

  }

  Future getFilteredShiftByStatus(FilterModel filters) async {
    final options = _reader(accessKeyOptionsProvider);

    bool isApplied = filters.status == 'APPLIED';
    String status_ = isApplied ? 'NEW' : filters.status;

    try {
      final result = await _dioClient.post(
        '$dataService/api/v1/shift-filter/${filters.workerId}/0/300?agentId=${filters.agentId}&clientId=${filters.clientId}&endDate=${filters.endDate}&location=${filters.location}&startDate=${filters.startDate}&status=$status_',
        options: options,
      );

      if (result.statusCode == 200) {
        var fData = result.data as List;

        var shifts = fData.map((e) => FilteredShift.fromJson(e)).toList();

        var activeShifts = shifts.where((sh) {
          // print(sh.toJson());
          if (sh.shiftStatus == null) {
            return true;
          }

          return sh.shiftStatus?.toLowerCase() != 'expired';
        }).toList();

        if (isApplied) {
          final appliedShifts = activeShifts.where((sh) {
            return sh.appliedStatus != null;
          }).toList();

          return appliedShifts;
        }

        return activeShifts;
      }

      // throw error
      else {
        throw Exception(
            'Error getting available filtered ${filters.status} shifts. Try again later');
      }
    }

    //
    catch (e) {
      
      return exceptionHandler(e, 'filtered worker ${filters.status} shifts',StackTrace.current);
    }
  }

  Future registerDevice({required String? fcmToken}) async {
    final options = _reader(accessKeyOptionsProvider);
    final _worker = _reader(loginResponseProvider).state;
    // bool isApplied = filters.status == 'APPLIED';
    // String status_ = isApplied ? 'NEW' : filters.status;

    try {
      final result = await _dioClient.post(
        '$dataService/api/v1/device',
        data: {"workerId": _worker?.workerId ?? 1,
        "fcmToken": fcmToken},
        options: options,
      );

      if (result.statusCode == 200) {
        print('Succefully registered notifications token');

    }

      // throw error
      else {
            print('Error registering notifications token');
      }
    }

    //
    catch (e) {
      print('[register device] $e');
      return exceptionHandler(e, 'register device',StackTrace.current);
    }
  }


  Future bookAShift({int agencyId = 1, required int shiftId, required int workerId,}) async {
    final options = _reader(accessKeyOptionsProvider);

    try {
      final result = await _dioClient.put(
        '$dataService/api/v1/book-shift/$shiftId/$workerId/$agencyId',
        options: options,
      );


      if (result.statusCode == 200) {
        return true;
      }

      // throw error
      else {
        throw Exception(
            'There was a problem while booking a shift with id: $shiftId. Please try again later');
      }
    }

    //
    catch (e) {
      return exceptionHandler(e, 'book shift',StackTrace.current);
    }
  }



  Future applyTraining({int agencyId = 1, required int shiftId, required int workerId,}) async {
    final options = _reader(accessKeyOptionsProvider);

    try {
      final result = await _dioClient.put(
        '$dataService/api/v1/training-booking/apply/$agencyId/$shiftId/$workerId',
        options: options,
      );


      if (result.statusCode == 200) {
        return true;
      }

      // throw error
      else {
        throw Exception(
            'There was a problem while applying with id: $shiftId. Please try again later');
      }
    }

    //
    catch (e) {
      return exceptionHandler(e, 'apply',StackTrace.current);
    }
  }

  Future releaseShift({required int shiftId}) async {
    final options = _reader(accessKeyOptionsProvider);
    final _worker = _reader(loginResponseProvider).state;
    final workerId = _worker?.workerId ?? 1;
    try {
      final result = await _dioClient.put(
        '$dataService/api/v1/release-shift/$shiftId/$workerId',
        options: options,
      );


      if (result.statusCode == 200) {
        return true;
      }

      // throw error
      else {
        throw Exception(
            'There was a problem while booking a shift with id: $shiftId. Please try again later');
      }
    }

    //
    catch (e) {
      return exceptionHandler(e, 'book shift',StackTrace.current);
    }
  }


  Future remindAuthorisation({required int shiftId}) async {
    final options = _reader(accessKeyOptionsProvider);
    try{
      await _dioClient.put(
          '$dataService/api/v1/shift/worker/remind-authorisation/$shiftId', options: options);
    }catch (e) {
      
      throw exceptionHandler(e, 'Authorization reminder',StackTrace.current);
    }
  }

  /// query a shift
  Future queryAShift({
    required int shiftId,
    required int workerId,
    String reason = 'shift query',
  }) async {
    final options = _reader(accessKeyOptionsProvider);

    try {
      final result = await _dioClient.put(
        '$dataService/api/v1/query-shift/$shiftId/$workerId/$reason',
        options: options,
      );


      if (result.statusCode == 200) {
        return true;
      }

      // throw error
      else {
        throw Exception(
            'There was a problem while querying a shift with id: $shiftId. Please try again later');
      }
    }

    //
    catch (e) {
      
      return exceptionHandler(e, 'query shift',StackTrace.current);
    }
  }

  /// apply a shift
  Future applyAShift({
    required int agencyId,
    required int shiftId,
    required int workerId,
  }) async {
    final options = _reader(accessKeyOptionsProvider);

    try {
      final result = await _dioClient.put(
        '$dataService/api/v1/apply-shift/$shiftId/$workerId/$agencyId',
        options: options,
      );


      if (result.statusCode == 200) {
        return true;
      }

      // throw error
      else {
        throw Exception(
            'There was a problem while applying a shift with id: $shiftId. Please try again later');
      }
    }

    //
    catch (e) {
      
      return exceptionHandler(e, 'apply shift',StackTrace.current);
    }
  }

  /// cancel a shift
  Future cancelAShift({
    required int workerId,
    required int shiftId,
    String reason = 'cancel shift reason',
  }) async {
    final options = _reader(accessKeyOptionsProvider);

    try {
      final result = await _dioClient.put(
        '$dataService/api/v1/cancel-shift-by-worker/$shiftId/$workerId/$reason',
        options: options,
      );


      if (result.statusCode == 200) {
        return true;
      }

      // throw error
      else {
        throw Exception(
            'There was a problem while cancelling a shift with id: $shiftId. Please try again later');
      }
    }

    //
    catch (e) {
      
      return exceptionHandler(e, 'cancel shift',StackTrace.current);
    }
  }


  /// cancel a shift
  Future cancelTrainingBooking({
    required int workerId,
    required int shiftId,
    String reason = 'cancel shift reason',
  }) async {
    final options = _reader(accessKeyOptionsProvider);

    try {
      final result = await _dioClient.delete(
        '$dataService/api/v1/training-booking/$shiftId',
        options: options,
      );


      if (result.statusCode == 204) {
        return true;
      }

      // throw error
      else {

        throw Exception(
            'There was a problem while cancelling a training booking with id: $shiftId. Please try again later');
      }
    }

    //
    catch (e) {
      
      return exceptionHandler(e, 'cancel shift',StackTrace.current);
    }
  }
}
