// To parse this JSON data, do
//
//     final agenciesWorker = agenciesWorkerFromMap(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';

import 'package:work_link/src/models/Pageable.dart';
import 'package:work_link/src/models/Sort.dart';

import 'agency_worker.dart';

part 'agencies_worker.freezed.dart';
part 'agencies_worker.g.dart';

@freezed
abstract class AgenciesWorker with _$AgenciesWorker {
  factory AgenciesWorker({
    List<AgencyWorker>? content,
    Pageable? pageable,
    bool? last,
    int? totalPages,
    int? totalElements,
    int? size,
    int? number,
    Sort? sort,
    int? numberOfElements,
    bool? first,
    bool? empty,
  }) = _AgenciesWorker;

  factory AgenciesWorker.fromJson(Map<String, dynamic> json) =>
      _$AgenciesWorkerFromJson(json);
}
