import 'dart:io';

import 'package:flutter/material.dart';
// import 'package:month_year_picker/month_year_picker.dart'; // Temporarily disabled
import 'package:work_link/src/features/payrol/view/pay_advices_widget.dart';
import 'package:work_link/src/features/payrol/view/payslips_widget.dart';
import 'package:work_link/src/features/payrol/view/payslips_widget.dart';

import 'package:work_link/src/utils/index.dart';

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'expense_claimed_widget.dart';

// Helper function to replace showMonthYearPicker
Future<DateTime?> _showMonthYearPicker({
  required BuildContext context,
  required DateTime firstDate,
  required DateTime lastDate,
  DateTime? initialDate,
}) async {
  final DateTime? picked = await showDatePicker(
    context: context,
    initialDate: initialDate ?? DateTime.now(),
    firstDate: firstDate,
    lastDate: lastDate,
    initialDatePickerMode: DatePickerMode.year,
  );
  return picked;
}

class payrol extends StatefulWidget {
  @override
  payrolState createState() => payrolState();
}

class payrolState extends State<payrol> {
  TextEditingController monthSelected = TextEditingController(text: "Mar");
  List months = [
    'Jan',
    'Feb',
    'Mar',
    'April',
    'May',
    'Jun',
    'July',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec'
  ];
  String? _dropDownValue = "";

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.white,
          leading: Container(
            // height: 30,
            // width: 30,
            child: Padding(
              padding: const EdgeInsets.only(left: 15.0, right: 15),
              child: InkWell(
                child: Icon(   Icons.arrow_back,
                  color: welcomeTextColor,
                  size: 20,
                ),
                onTap: () {
                  routeBack(context);
                },
              ),
            ),
          ),
          title: Text("Payroll", style: TextStyle(color: welcomeTextColor),),
          centerTitle: true,
          bottom: TabBar(
            isScrollable: true,
            unselectedLabelColor: Colors.grey,
            labelColor: welcomeTextColor,
            indicatorColor: welcomeTextColor,
            tabs: [
              Tab(icon: Text("Payslips")),
              Tab(icon: Text("Pay Advices")),
              Tab(icon: Text("Expenses Claimed")),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Expanded(
                      child: Padding(
                          padding: const EdgeInsets.only(
                              left: 10.0, right: 10, top: 20),
                          child: Container(
                              height: 50,
                              decoration: BoxDecoration(
                                color: Color(0xffE6E6E6),
                                borderRadius:
                                BorderRadius.all(Radius.circular(0)),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.only(
                                    left: 10.0, right: 10, top: 10, bottom: 10),
                                child: Container(
                                  child: Row(
                                    children: [
                                      Expanded(
                                          flex: 0,
                                          child: Padding(
                                            padding: const EdgeInsets.only(
                                                left: 0.0, right: 10),
                                            child: Text(
                                              "Filter by",
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  color: Colors.black,
                                                  fontWeight: FontWeight.w500),
                                            ),
                                          )),
                                      Expanded(
                                          flex: 0,
                                          child: Tooltip(
                                            message: "Date",
                                            child: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 0.0, right: 10),
                                                child: Image.asset(
                                                  "assets/drawer/availibality.png",
                                                  height: 23.0,
                                                  width: 23.0,
                                                )
                                            ),
                                          )
                                      ),
                                      Expanded(
                                          flex: 1,
                                          child: Container(
                                            height: 40,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              border: Border.all(
                                                  color: Colors.grey),
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(5)),
                                            ),
                                            child: GestureDetector(
                                                onTap: () async{

                                                  final selected = await _showMonthYearPicker(
                                                    context: context,
                                                    firstDate: DateTime(
                                                        DateTime.now().year,
                                                        DateTime.now().month),
                                                    lastDate: DateTime(
                                                        DateTime.now().year +
                                                            10,
                                                        9),
                                                    initialDate: DateTime.now(),
                                                  );
                                                  monthSelected.text =
                                                      months[selected!.month - 1]!
                                                          .toString() +
                                                          " " +
                                                          selected!.year!
                                                              .toString();
                                                  setState(() {});

                                                  /*   showMonthPicker(
                                                    context: context,
                                                    firstDate: DateTime(
                                                        DateTime.now().year,
                                                        DateTime.now().month),
                                                    lastDate: DateTime(
                                                        DateTime.now().year +
                                                            10,
                                                        9),
                                                    initialDate: DateTime.now(),
                                                    locale: Locale("en"),
                                                  ).then((date) {
                                                    monthSelected.text =
                                                        months[date!.month - 1]!
                                                                .toString() +
                                                            " " +
                                                            date!.year!
                                                                .toString();
                                                    setState(() {});
                                                  });*/

                                                },
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      child: Center(
                                                          child: Text(
                                                              monthSelected
                                                                  .text)),
                                                      flex: 1,
                                                    ),
                                                    Expanded(
                                                      child: GestureDetector(
                                                        onTap: () async{

                                                          final selected = await _showMonthYearPicker(
                                                            context: context,
                                                            firstDate: DateTime(
                                                                DateTime.now().year,
                                                                DateTime.now().month),
                                                            lastDate: DateTime(
                                                                DateTime.now().year +
                                                                    10,
                                                                9),
                                                            initialDate: DateTime.now(),
                                                          );
                                                          monthSelected.text =
                                                              months[selected!.month - 1]!
                                                                  .toString() +
                                                                  " " +
                                                                  selected!.year!
                                                                      .toString();
                                                          setState(() {});


                                                          /*
                                                          showMonthPicker(
                                                            context: context,
                                                            firstDate: DateTime(
                                                                DateTime.now()
                                                                    .year,
                                                                DateTime.now()
                                                                    .month),
                                                            lastDate: DateTime(
                                                                DateTime.now()
                                                                        .year +
                                                                    10,
                                                                9),
                                                            initialDate:
                                                                DateTime.now(),
                                                            locale:
                                                                Locale("en"),
                                                          ).then((date) {
                                                            monthSelected
                                                                .text = months[
                                                                        date!.month -
                                                                            1]!
                                                                    .toString() +
                                                                " " +
                                                                date!.year!
                                                                    .toString();
                                                            setState(() {});
                                                          });*/

                                                        },
                                                        child: Padding(
                                                          padding: EdgeInsets
                                                              .fromLTRB(
                                                              0.0,
                                                              0.0,
                                                              0.0,
                                                              0.0),
                                                          child: Icon(
                                                            Icons
                                                                .arrow_drop_down_outlined,
                                                            size: 25,
                                                            color: Colors.black,
                                                          ),
                                                        ),
                                                      ),
                                                      flex: 0,
                                                    ),

                                                  ],
                                                )),
                                          )),
                                      Expanded(
                                          flex: 0,
                                          child: Tooltip(
                                            message: "Business",
                                            child: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 15.0, right: 10),
                                                child: Image.asset(
                                                  "assets/images/agency.png",
                                                  height: 23.0,
                                                  width: 23.0,
                                                )),
                                          )
                                      ),
                                      Expanded(
                                          flex: 1,
                                          child: Container(
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                border: Border.all(
                                                    color: Colors.grey),
                                                borderRadius: BorderRadius.all(
                                                    Radius.circular(5)),
                                              ),
                                              width: double.infinity,
                                              child:
                                              DropdownButtonHideUnderline(
                                                  child: DropdownButton(
                                                    hint: _dropDownValue == null
                                                        ? Text('Dropdown')
                                                        : Center(
                                                      child: Text(
                                                        _dropDownValue!,
                                                        style: TextStyle(
                                                            color:
                                                            Colors.black),
                                                      ),
                                                    ),
                                                    isExpanded: true,
                                                    iconSize: 30.0,
                                                    style: TextStyle(
                                                        color: Colors.blue),
                                                    iconEnabledColor: Colors.black,
                                                    items:
                                                    ['One', 'Two', 'Three'].map(
                                                          (val) {
                                                        return DropdownMenuItem<
                                                            String>(
                                                          value: val,
                                                          child: Text(
                                                            val,
                                                            style: TextStyle(
                                                                color:
                                                                Colors.black),
                                                          ),
                                                        );
                                                      },
                                                    ).toList(),
                                                    onChanged: (val) {
                                                      setState(
                                                            () {
                                                          _dropDownValue =
                                                              val.toString();
                                                        },
                                                      );
                                                    },
                                                  )))),
                                    ],
                                  ),
                                ),
                              ))),
                      flex: 0,
                    ),
                    Expanded(
                      child:   Container(child:PayslipsWidget()),flex: 1,),


                  ],
                )),
            Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Expanded(
                      child: Padding(
                          padding: const EdgeInsets.only(
                              left: 10.0, right: 10, top: 20),
                          child: Container(
                              height: 50,
                              decoration: BoxDecoration(
                                color: Color(0xffE6E6E6),
                                borderRadius:
                                BorderRadius.all(Radius.circular(0)),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.only(
                                    left: 10.0, right: 10, top: 10, bottom: 10),
                                child: Container(
                                  child: Row(
                                    children: [
                                      Expanded(
                                          flex: 0,
                                          child: Padding(
                                            padding: const EdgeInsets.only(
                                                left: 0.0, right: 10),
                                            child: Text(
                                              "Filter by",
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  color: Colors.black,
                                                  fontWeight: FontWeight.w500),
                                            ),
                                          )),
                                      Expanded(
                                          flex: 0,
                                          child: Tooltip(
                                            message: "Date",
                                            child: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 0.0, right: 10),
                                                child: Image.asset(
                                                  "assets/drawer/availibality.png",
                                                  height: 23.0,
                                                  width: 23.0,
                                                )
                                            ),
                                          )
                                      ),
                                      Expanded(
                                          flex: 1,
                                          child: Container(
                                            height: 40,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              border: Border.all(
                                                  color: Colors.grey),
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(5)),
                                            ),
                                            child: GestureDetector(
                                                onTap: () async{
                                                  final selected = await _showMonthYearPicker(
                                                    context: context,
                                                    firstDate: DateTime(
                                                        DateTime.now().year,
                                                        DateTime.now().month),
                                                    lastDate: DateTime(
                                                        DateTime.now().year +
                                                            10,
                                                        9),
                                                    initialDate: DateTime.now(),
                                                  );
                                                  monthSelected.text =
                                                      months[selected!.month - 1]!
                                                          .toString() +
                                                          " " +
                                                          selected!.year!
                                                              .toString();
                                                  setState(() {});
                                                },
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      child: Center(
                                                          child: Text(
                                                              monthSelected
                                                                  .text)),
                                                      flex: 1,
                                                    ),
                                                    Expanded(
                                                      child: GestureDetector(
                                                        onTap: () async{
                                                          final selected = await _showMonthYearPicker(
                                                            context: context,
                                                            firstDate: DateTime(
                                                                DateTime.now().year,
                                                                DateTime.now().month),
                                                            lastDate: DateTime(
                                                                DateTime.now().year +
                                                                    10,
                                                                9),
                                                            initialDate: DateTime.now(),
                                                          );
                                                          monthSelected.text =
                                                              months[selected!.month - 1]!
                                                                  .toString() +
                                                                  " " +
                                                                  selected!.year!
                                                                      .toString();
                                                          setState(() {});
                                                        },
                                                        child: Padding(
                                                          padding: EdgeInsets
                                                              .fromLTRB(
                                                              0.0,
                                                              0.0,
                                                              0.0,
                                                              0.0),
                                                          child: Icon(
                                                            Icons
                                                                .arrow_drop_down_outlined,
                                                            size: 25,
                                                            color: Colors.black,
                                                          ),
                                                        ),
                                                      ),
                                                      flex: 0,
                                                    ),

                                                  ],
                                                )),
                                          )),
                                      Expanded(
                                          flex: 0,
                                          child: Tooltip(
                                            message: "Business",
                                            child: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 15.0, right: 10),
                                                child: Image.asset(
                                                  "assets/images/agency.png",
                                                  height: 23.0,
                                                  width: 23.0,
                                                )),
                                          )),
                                      Expanded(
                                          flex: 1,
                                          child: Container(
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                border: Border.all(
                                                    color: Colors.grey),
                                                borderRadius: BorderRadius.all(
                                                    Radius.circular(5)),
                                              ),
                                              width: double.infinity,
                                              child:
                                              DropdownButtonHideUnderline(
                                                  child: DropdownButton(
                                                    hint: _dropDownValue == null
                                                        ? Text('Dropdown')
                                                        : Center(
                                                      child: Text(
                                                        _dropDownValue!,
                                                        style: TextStyle(
                                                            color:
                                                            Colors.black),
                                                      ),
                                                    ),
                                                    isExpanded: true,
                                                    iconSize: 30.0,
                                                    style: TextStyle(
                                                        color: Colors.blue),
                                                    iconEnabledColor: Colors.black,
                                                    items:
                                                    ['One', 'Two', 'Three'].map(
                                                          (val) {
                                                        return DropdownMenuItem<
                                                            String>(
                                                          value: val,
                                                          child: Text(
                                                            val,
                                                            style: TextStyle(
                                                                color:
                                                                Colors.black),
                                                          ),
                                                        );
                                                      },
                                                    ).toList(),
                                                    onChanged: (val) {
                                                      setState(
                                                            () {
                                                          _dropDownValue =
                                                              val.toString();
                                                        },
                                                      );
                                                    },
                                                  )))),
                                    ],
                                  ),
                                ),
                              ))),
                      flex: 0,
                    ),
                    Expanded(
                      child:   SingleChildScrollView(child: PayAdvicesWidget(),),flex: 1,),


                  ],
                )),
            Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Expanded(
                      child: Padding(
                          padding: const EdgeInsets.only(
                              left: 10.0, right: 10, top: 20),
                          child: Container(
                              height: 50,
                              decoration: BoxDecoration(
                                color: Color(0xffE6E6E6),
                                borderRadius:
                                BorderRadius.all(Radius.circular(0)),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.only(
                                    left: 10.0, right: 10, top: 10, bottom: 10),
                                child: Container(
                                  child: Row(
                                    children: [
                                      Expanded(
                                          flex: 0,
                                          child: Padding(
                                            padding: const EdgeInsets.only(
                                                left: 0.0, right: 10),
                                            child: Text(
                                              "Filter by",
                                              style: TextStyle(
                                                  fontSize: 16,
                                                  color: Colors.black,
                                                  fontWeight: FontWeight.w500),
                                            ),
                                          )),
                                      Expanded(
                                          flex: 0,
                                          child: Tooltip(
                                            message: "Date",
                                            child: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 0.0, right: 10),
                                                child: Image.asset(
                                                  "assets/drawer/availibality.png",
                                                  height: 23.0,
                                                  width: 23.0,
                                                )
                                            ),
                                          )),
                                      Expanded(
                                          flex: 1,
                                          child: Container(
                                            height: 40,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              border: Border.all(
                                                  color: Colors.grey),
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(5)),
                                            ),
                                            child: GestureDetector(
                                                onTap: () async{
                                                  final selected = await _showMonthYearPicker(
                                                    context: context,
                                                    firstDate: DateTime(
                                                        DateTime.now().year,
                                                        DateTime.now().month),
                                                    lastDate: DateTime(
                                                        DateTime.now().year +
                                                            10,
                                                        9),
                                                    initialDate: DateTime.now(),
                                                  );
                                                  monthSelected.text =
                                                      months[selected!.month - 1]!
                                                          .toString() +
                                                          " " +
                                                          selected!.year!
                                                              .toString();
                                                  setState(() {});
                                                },
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      child: Center(
                                                          child: Text(
                                                              monthSelected
                                                                  .text)),
                                                      flex: 1,
                                                    ),
                                                    Expanded(
                                                      child: GestureDetector(
                                                        onTap: () async{
                                                          final selected = await _showMonthYearPicker(
                                                            context: context,
                                                            firstDate: DateTime(
                                                                DateTime.now().year,
                                                                DateTime.now().month),
                                                            lastDate: DateTime(
                                                                DateTime.now().year +
                                                                    10,
                                                                9),
                                                            initialDate: DateTime.now(),
                                                          );
                                                          monthSelected.text =
                                                              months[selected!.month - 1]!
                                                                  .toString() +
                                                                  " " +
                                                                  selected!.year!
                                                                      .toString();
                                                          setState(() {});
                                                        },
                                                        child: Padding(
                                                          padding: EdgeInsets
                                                              .fromLTRB(
                                                              0.0,
                                                              0.0,
                                                              0.0,
                                                              0.0),
                                                          child: Icon(
                                                            Icons
                                                                .arrow_drop_down_outlined,
                                                            size: 25,
                                                            color: Colors.black,
                                                          ),
                                                        ),
                                                      ),
                                                      flex: 0,
                                                    ),

                                                  ],
                                                )),
                                          )),
                                      Expanded(
                                          flex: 0,
                                          child: Tooltip(
                                            message: "Business",
                                            child: Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 15.0, right: 10),
                                                child: Image.asset(
                                                  "assets/images/agency.png",
                                                  height: 23.0,
                                                  width: 23.0,
                                                )),
                                          )),
                                      Expanded(
                                          flex: 1,
                                          child: Container(
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                border: Border.all(
                                                    color: Colors.grey),
                                                borderRadius: BorderRadius.all(
                                                    Radius.circular(5)),
                                              ),
                                              width: double.infinity,
                                              child:
                                              DropdownButtonHideUnderline(
                                                  child: DropdownButton(
                                                    hint: _dropDownValue == null
                                                        ? Text('Dropdown')
                                                        : Center(
                                                      child: Text(
                                                        _dropDownValue!,
                                                        style: TextStyle(
                                                            color:
                                                            Colors.black),
                                                      ),
                                                    ),
                                                    isExpanded: true,
                                                    iconSize: 30.0,
                                                    style: TextStyle(
                                                        color: Colors.blue),
                                                    iconEnabledColor: Colors.black,
                                                    items:
                                                    ['One', 'Two', 'Three'].map(
                                                          (val) {
                                                        return DropdownMenuItem<
                                                            String>(
                                                          value: val,
                                                          child: Text(
                                                            val,
                                                            style: TextStyle(
                                                                color:
                                                                Colors.black),
                                                          ),
                                                        );
                                                      },
                                                    ).toList(),
                                                    onChanged: (val) {
                                                      setState(
                                                            () {
                                                          _dropDownValue =
                                                              val.toString();
                                                        },
                                                      );
                                                    },
                                                  )))),
                                    ],
                                  ),
                                ),
                              ))),
                      flex: 0,
                    ),
                    Expanded(
                      child:   Container(child:ExpenseClaimedWidget()),flex: 1,),


                  ],
                )),
          ],
        ),
      ),
    );
  }
}
