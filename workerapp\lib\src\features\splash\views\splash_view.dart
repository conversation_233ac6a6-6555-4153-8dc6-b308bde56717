// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:work_link/src/features/login/views/login_view.dart';
// import 'package:work_link/src/services/index.dart';
// import 'package:work_link/src/utils/index.dart';
//
// class SplashView extends ConsumerWidget {
//   const SplashView({Key? key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context,   watch) {
//     return SafeArea(
//       child: Scaffold(
//         backgroundColor: Colors.white,
//         body: ProviderListener<int?>(
//           onChange: (context, elapsed) {
//             if (elapsed == 2) {
//               // goto another page
//               routeToWithClear(
//                 context,
//                 LoginView(),
//               );
//             }
//           },
//           provider: clockProvider,
//           child: Stack(
//             fit: StackFit.expand,
//             children: [
//               Column(
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 children: [
//                   Expanded(
//                     flex: 7,
//                     child: Container(
//                       child: Column(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: <Widget>[
//                           Padding(
//                             padding:  EdgeInsets.all(90.0),
//                             child: Image.asset(
//                               'assets/images/logo.png',
//                               // height: MediaQuery.of(context).size.height * 0.5,
//                               // width: MediaQuery.of(context).size.width,
//                             ),
//                           ),
//                           Padding(
//                             padding: EdgeInsets.only(top: 15),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                   Expanded(
//                     child: Column(
//                       children: <Widget>[
//                         CircularProgressIndicator( ),
//                         Container(
//                           height: 10,
//                         ),
//                       ],
//                     ),
//                   )
//                 ],
//               )
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
