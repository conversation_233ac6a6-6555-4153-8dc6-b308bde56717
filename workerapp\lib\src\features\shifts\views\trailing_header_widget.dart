import 'package:flutter/material.dart';
import 'package:duration/duration.dart';
import 'package:intl/intl.dart';
import 'package:work_link/src/features/shifts/data/shift_category.dart';
import 'package:work_link/src/models/shift/ShiftsPageResp.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:styled_widget/styled_widget.dart';

import '../../../models/shift/Shift.dart';
import '../../../utils/constants.dart';

String _formatDuration(Duration duration) {
  if (duration.inDays > 0) {
    return '${duration.inDays}d ${duration.inHours % 24}h';
  } else if (duration.inHours > 0) {
    return '${duration.inHours}h ${duration.inMinutes % 60}m';
  } else {
    return '${duration.inMinutes}m';
  }
}

Widget trailingHeaderWidget(ShiftCategoryStatus status, Shift shift) {
  var w;

  switch (status.status) {
    case 'CANCELLED':
      w = Text('Shift Closed').textColor(secondaryAccent);
      break;

    case 'BOOKED':
      final _shiftD = dateTimeFormat.format(shift.start!);
      final _fd = DateFormat('dd/MM/yyyy HH:mm').parse(_shiftD);

      final duration = _fd.difference(DateTime.now());
      final _dd = _formatDuration(duration);

      if(_fd.compareTo(DateTime.now()) > 0){
        w = RichText(
          text: TextSpan(style: TextStyle(color: Colors.white),
            text: (shift.bookingType?.toCapitalized()??"Shift")+' starts in ',
            children: <TextSpan>[
              TextSpan(
                text: _dd,
              ),
            ],
          ),
        );

      }else {
        w = RichText(
            text: TextSpan(
                text: (shift.bookingType?.toCapitalized()??"Shift")+' started',
                style: TextStyle(color: textColor),
                children: <TextSpan>[
                  TextSpan(
                  )
                ]

            )
        );
      }

      break;

    default:
      w = SizedBox.shrink();
  }

  return w;
}
