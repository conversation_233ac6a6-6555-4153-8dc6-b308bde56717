
import 'package:freezed_annotation/freezed_annotation.dart';

part 'Training.freezed.dart';
part 'Training.g.dart';

@freezed
abstract class Training with _$Training {
  factory Training({

    String? agencyId,
    String? code,
    String? description,
    int? id,
    String? name,
    String? serviceId,
    String? trainingDate,
    String? trainingExpiry,
    String? workerId,
    String? trainingId,


  }) = _Training;

  factory Training.fromJson(Map<String, dynamic> json) =>
      _$TrainingFromJson(json);
}
