class FilterModel {
  final int agentId;
  final int clientId;
  final int workerId;
  final String startDate;
  final String endDate;
  final String location;
  final String status;

  FilterModel({
    required this.agentId,
    this.clientId = 1,
    required this.workerId,
    required this.startDate,
    required this.endDate,
    required this.location,
    this.status = 'NEW',
  });

  @override
  String toString() {
    return 'FilterModel(agentId: $agentId, clientId: $clientId, workerId: $workerId, startDate: $startDate, endDate: $endDate, location: $location, status: $status)';
  }
}
