

import '../../../models/Pageable.dart';

class ClientModel {
  List<Content>? content;
  Pageable? pageable;
  int? totalPages;
  int? totalElements;
  bool? last;
  int? size;
  int? number;
  Sort? sort;
  int? numberOfElements;
  bool? first;
  bool? empty;

  ClientModel(
      {this.content,
        this.pageable,
        this.totalPages,
        this.totalElements,
        this.last,
        this.size,
        this.number,
        this.sort,
        this.numberOfElements,
        this.first,
        this.empty});

  ClientModel.fromJson(Map<String, dynamic> json) {
    if (json['content'] != null) {
      content = <Content>[];
      json['content'].forEach((v) {
        content!.add(new Content.fromJson(v));
      });
    }
    pageable = json['pageable'] != null
        ? new Pageable.fromJson(json['pageable'])
        : null;
    totalPages = json['totalPages'];
    totalElements = json['totalElements'];
    last = json['last'];
    size = json['size'];
    number = json['number'];
    sort = json['sort'] != null ? new Sort.fromJson(json['sort']) : null;
    numberOfElements = json['numberOfElements'];
    first = json['first'];
    empty = json['empty'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.content != null) {
      data['content'] = this.content!.map((v) => v.toJson()).toList();
    }
    if (this.pageable != null) {
      data['pageable'] = this.pageable!.toJson();
    }
    data['totalPages'] = this.totalPages;
    data['totalElements'] = this.totalElements;
    data['last'] = this.last;
    data['size'] = this.size;
    data['number'] = this.number;
    if (this.sort != null) {
      data['sort'] = this.sort!.toJson();
    }
    data['numberOfElements'] = this.numberOfElements;
    data['first'] = this.first;
    data['empty'] = this.empty;
    return data;
  }
}

class Content {
  String? name;
  int? id;
  String? status;
  String? logo;
  String? firstLine;
  String? town;
  String? telephone;
  String? email;
  String? billingEmail;
  String? service;
  String? postCode;
  String? createdBy;
  Null? county;

  Content(
      {this.name,
        this.id,
        this.status,
        this.logo,
        this.firstLine,
        this.town,
        this.telephone,
        this.email,
        this.billingEmail,
        this.service,
        this.postCode,
        this.createdBy,
        this.county});

  Content.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    id = json['id'];
    status = json['status'];
    logo = json['logo'];
    firstLine = json['firstLine'];
    town = json['town'];
    telephone = json['telephone'];
    email = json['email'];
    billingEmail = json['billingEmail'];
    service = json['service'];
    postCode = json['postCode'];
    createdBy = json['createdBy'];
    county = json['county'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['id'] = this.id;
    data['status'] = this.status;
    data['logo'] = this.logo;
    data['firstLine'] = this.firstLine;
    data['town'] = this.town;
    data['telephone'] = this.telephone;
    data['email'] = this.email;
    data['billingEmail'] = this.billingEmail;
    data['service'] = this.service;
    data['postCode'] = this.postCode;
    data['createdBy'] = this.createdBy;
    data['county'] = this.county;
    return data;
  }
}


class Sort {
  bool? sorted;
  bool? unsorted;
  bool? empty;

  Sort({this.sorted, this.unsorted, this.empty});

  Sort.fromJson(Map<String, dynamic> json) {
    sorted = json['sorted'];
    unsorted = json['unsorted'];
    empty = json['empty'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['sorted'] = this.sorted;
    data['unsorted'] = this.unsorted;
    data['empty'] = this.empty;
    return data;
  }
}
