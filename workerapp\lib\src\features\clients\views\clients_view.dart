import 'package:flutter/cupertino.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/features/clients/views/client_directors.dart';
import 'package:work_link/src/models/agencies-worker/agency_worker.dart';
import 'package:work_link/src/models/custom_exception.dart';

import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

final _clientsProvider = AutoDisposeFutureProvider(
    (ref) => ref.watch(dataRepoProvider).getWorkerClients());

class ClientsView extends ConsumerWidget {
  ClientsView({Key? key}) : super(key: key);

  TextEditingController searchController = new TextEditingController();
  List<AgencyWorker>? agencyClientList = [];
  bool isSearching =false;

  @override
  Widget build(BuildContext context,   watch) {
    final clients = watch.watch(_clientsProvider);

    return SafeArea(
      child: Scaffold(
        appBar: AppAppBar(context,
            leading: IconButton(
              onPressed: () => routeBack(context),
              icon: Icon(
                Icons.chevron_left,
                color: welcomeTextColor,
                size: 35,
              ),
            )),
        body: RelativeBuilder(builder: (context, height, width, sy, sx) {
          return clients.when(
            data: (ag) {
              //  print(ag);
              if (ag != null) {
                return ag.content!.isNotEmpty
                    ? Column(
                        children: [
                          Expanded(
                            child: Padding(
                              padding:  EdgeInsets.only(left:10.0,right:10,top:20,bottom: 20),
                              child: TextField(
                                onChanged: (value) {


                                },
                                controller: searchController,
                                decoration: InputDecoration(
                                    labelText: "Search Client",
                                    hintText: "Write here...",
                                    labelStyle:
                                        TextStyle(color: welcomeTextColor),
                                    prefixIcon: Icon(
                                      Icons.search,
                                      color: welcomeTextColor,
                                    ),
                                    border: OutlineInputBorder(
                                        borderSide:
                                            BorderSide(color: welcomeTextColor),
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(15.0))),
                                    focusedBorder: OutlineInputBorder(
                                        borderSide:
                                            BorderSide(color: welcomeTextColor),
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(15.0))),
                                    enabledBorder: OutlineInputBorder(
                                        borderSide:
                                            BorderSide(color: welcomeTextColor),
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(15.0)))),
                              ),
                            ),
                            flex: 0,
                          ),
                          Expanded(
                            child: ListView(
                              children: [
                                Column(
                                    children: List.generate(
                                  ag!.content!.length,
                                  (int index) {
                                    AgencyWorker cli = ag.content![index];
                                    return Padding(
                                      padding: const EdgeInsets.only(
                                          left: 10.0, right: 10, top: 10),
                                      child: Card(
                                        margin: const EdgeInsets.only(top: 0.0),
                                        elevation: 1.0,
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(top: 6.0),
                                          child: ListTile(
                                            title: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  children: [
                                                    Icon(
                                                      Icons
                                                          .account_circle_outlined,
                                                      color: Colors.grey,
                                                      size: 20.0,
                                                    ),
                                                    SizedBox(
                                                      width: 10.0,
                                                    ),
                                                    Expanded(
                                                      child: Text(
                                                        cli.name.toString(),
                                                        style: GoogleFonts.viga(
                                                            fontStyle: FontStyle
                                                                .normal,
                                                            fontSize: 15.0,
                                                            color:
                                                                Colors.black87),
                                                      ),
                                                      flex: 1,
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(
                                                  height: 5.0,
                                                ),
                                                Row(
                                                  children: [
                                                    Text(
                                                      'Service',
                                                      style: GoogleFonts.lato(
                                                          fontStyle:
                                                              FontStyle.normal,
                                                          fontSize: 14.0,
                                                          color:
                                                              Colors.black45),
                                                    ),
                                                    SizedBox(
                                                      width: 10.0,
                                                    ),
                                                    Text(
                                                      cli.service.toString(),
                                                      style: GoogleFonts.lato(
                                                          fontStyle:
                                                              FontStyle.normal,
                                                          fontSize: 14.0,
                                                          color:
                                                              Colors.black87),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(
                                                  height: 10.0,
                                                ),
                                              ],
                                            ),
                                            onTap: () {
                                              routeTo(
                                                  context,
                                                  ClientDirector(
                                                      cli.id.toString()));
                                            },
                                            trailing: IconButton(
                                              onPressed: () {
                                                routeTo(
                                                    context,
                                                    ClientDirector(
                                                        cli.id.toString()));
                                              },
                                              icon: Icon(Icons.info),
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                )),
                              ],
                            ),
                            flex: 1,
                          ),
                        ],
                      )
                    : Center(
                        child:
                            Text('You do not have any available clients yet!'));
              }

              return ErrorPage(
                error: 'Failed to get your linked Clients',
                stackTrace: StackTrace.current,
                onTryAgain: () => watch.refresh(_clientsProvider),
              );
            },
            loading: () => Center(
              child: CircularProgressIndicator(),
            ),
            error: (e, st) => ErrorPage(
              error: e is CustomException ? e.message : e.toString(),
              stackTrace: st,onTryAgain: () => watch.refresh(_clientsProvider),
            ),
          );
        }),
      ),
    );
  }
}
