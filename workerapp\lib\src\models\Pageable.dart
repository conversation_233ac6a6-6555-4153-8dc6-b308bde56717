import 'package:freezed_annotation/freezed_annotation.dart';

import 'Sort.dart';

part 'Pageable.freezed.dart';
part 'Pageable.g.dart';

@freezed
abstract class Pageable with _$Pageable {
  factory Pageable({
    int? offset,
    int? pageNumber,
    int? pageSize,
    bool? paged,
    Sort? sort,
    bool? unpaged,
  }) = _Pageable;

  factory Pageable.fromJson(Map<String, dynamic> json) =>
      _$PageableFromJson(json);
}
