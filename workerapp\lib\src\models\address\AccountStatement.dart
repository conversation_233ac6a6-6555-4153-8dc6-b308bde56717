import 'package:freezed_annotation/freezed_annotation.dart';

part 'AccountStatement.freezed.dart';
part 'AccountStatement.g.dart';

@freezed
sealed class AccountStatement with _$AccountStatement {
  const factory AccountStatement({
    String? currencySymbol,
    double? balance,
    String? balanceDescription,
  }) = _AccountStatement;

  factory AccountStatement.fromJson(Map<String, dynamic> json) =>
      _$AccountStatementFromJson(json);
}
