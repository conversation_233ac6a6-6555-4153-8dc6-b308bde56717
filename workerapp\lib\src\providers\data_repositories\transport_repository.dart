import 'package:dio/dio.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/utils/constants.dart';
import '../../utils/index.dart';

class TransportRepository {
  Dio _dioClient;
  final   _reader;
  TransportRepository(this._reader) : _dioClient = _reader(dioProvider);


  Future bookATransport({int agencyId = 1, required int transportId, required int workerId,}) async {
    final options = _reader(accessKeyOptionsProvider);

    try {
      final result = await _dioClient.put(
        '$dataService/api/v1/book-transport/$transportId/$workerId/$agencyId',
        options: options,
      );


      if (result.statusCode == 200) {
        return true;
      }

      // throw error
      else {
        throw Exception(
            'There was a problem while booking a transport with id: $transportId. Please try again later');
      }
    }

    //
    catch (e) {
      return exceptionHandler(e, 'book transport',StackTrace.current);
    }
  }

  Future applyTraining({int agencyId = 1, required int transportId, required int workerId,}) async {
    final options = _reader(accessKeyOptionsProvider);

    try {
      final result = await _dioClient.put(
        '$dataService/api/v1/training-booking/apply/$agencyId/$transportId/$workerId',
        options: options,
      );


      if (result.statusCode == 200) {
        return true;
      }

      // throw error
      else {
        throw Exception(
            'There was a problem while applying with id: $transportId. Please try again later');
      }
    }

    //
    catch (e) {
      return exceptionHandler(e, 'apply',StackTrace.current);
    }
  }

  Future releaseTransport({required int transportId}) async {
    final options = _reader(accessKeyOptionsProvider);
    final _worker = _reader(loginResponseProvider).state;
    final workerId = _worker?.workerId ?? 1;
    try {
      final result = await _dioClient.put(
        '$dataService/api/v1/release-transport/$transportId/$workerId',
        options: options,
      );

      if (result.statusCode == 200) {
        return true;
      }

      // throw error
      else {
        throw Exception(
            'There was a problem while booking a transport with id: $transportId. Please try again later');
      }
    }

    //
    catch (e) {
      return exceptionHandler(e, 'book transport',StackTrace.current);
    }
  }

  Future remindAuthorisation({required int transportId}) async {
    final options = _reader(accessKeyOptionsProvider);
    try{
      await _dioClient.put(
          '$dataService/api/v1/transport/worker/remind-authorisation/$transportId', options: options);
    }catch (e) {
      
      throw exceptionHandler(e, 'Authorization reminder',StackTrace.current);
    }
  }

  Future queryATransport({required int transportId, required int workerId, String reason = 'transport query',}) async {
    final options = _reader(accessKeyOptionsProvider);

    try {
      final result = await _dioClient.put(
        '$dataService/api/v1/query-transport/$transportId/$workerId/$reason',
        options: options,
      );


      if (result.statusCode == 200) {
        return true;
      }

      // throw error
      else {
        throw Exception(
            'There was a problem while querying a transport with id: $transportId. Please try again later');
      }
    }

    //
    catch (e) {
      
      return exceptionHandler(e, 'query transport',StackTrace.current);
    }
  }

  Future<void> applyATransport(int transportId, int workerId) async {
    final options = _reader(accessKeyOptionsProvider);
    try {
      await _dioClient.put('$dataService/api/v1/transport-booking/$workerId/$transportId', options: options,);
    }
    catch (e) {
      throw exceptionHandler(e, 'apply transport',StackTrace.current);
    }
  }

  Future<void> cancelATransport({required int workerId, required int transportId, String reason = 'none',}) async {
    final options = _reader(accessKeyOptionsProvider);
    try {
      await _dioClient.delete('$dataService/api/v1/transport-booking/cancel/$transportId?reason=${reason}', options: options,);
    }
    catch (e) {
      throw exceptionHandler(e, 'cancel transport',StackTrace.current);
    }
  }

  Future cancelTrainingBooking({required int workerId, required int transportId, String reason = 'cancel transport reason',}) async {
    final options = _reader(accessKeyOptionsProvider);

    try {
      final result = await _dioClient.delete(
        '$dataService/api/v1/training-booking/$transportId',
        options: options,
      );


      if (result.statusCode == 204) {
        return true;
      }

      // throw error
      else {

        throw Exception(
            'There was a problem while cancelling a training booking with id: $transportId. Please try again later');
      }
    }

    //
    catch (e) {
      
      return exceptionHandler(e, 'cancel transport',StackTrace.current);
    }
  }
}
