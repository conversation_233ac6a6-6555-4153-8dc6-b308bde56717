import 'package:flutter/material.dart';
//import 'package:flutter_webview_plugin/flutter_webview_plugin.dart';

import '../utils/index.dart';


class WebViewWidget extends StatefulWidget {
  static String tag = 'login-page';

  String url,title;

  WebViewWidget(this.url,this.title);

  @override
  WebViewState createState() =>     WebViewState();
}

class WebViewState extends State<WebViewWidget> {

@override
  void initState() {
    // TODO: implement initState
  if(widget.url.toLowerCase().contains("http")) {

  }else{
    widget.url="https://myworklink.uk/privacy-policy/";

  }

  print("URL+++++++++++++"+widget.url);
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return     Scaffold(
      appBar: AppBar(
        backgroundColor: deepBlueColor,
        elevation: 1.0,
        centerTitle: true,
        title: Text(
         widget.title,
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w700,
            fontSize: 20.0,
          ),
        ),
      ),
      body:     Column(
        children: <Widget>[
          Padding(
              padding: EdgeInsets.fromLTRB(0.0, 1.0, 0.0, 0.0),
              child:  Container(
                height: 0.5,
                color: Colors.grey,
              )),

           /*   Expanded(
            child: WebviewScaffold(
              url: widget.url,
              withJavascript: true,
              hidden: true,


            ),
            flex: 1,
          )*/
        ],
      ),
    );
  }
}
