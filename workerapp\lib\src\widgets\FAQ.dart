import 'package:flutter/material.dart';
import 'package:work_link/src/widgets/small_faq.dart';

import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

class Faq extends StatefulWidget {
  @override
  _FaqState createState() => _FaqState();
}

class _FaqState extends State<Faq> {

  bool isQ1=false;
  bool isQ2=false;
  bool isQ3=false;
  bool isQ4=false;
  bool isQ5=false;
  bool isQ6=false;
  bool isQ7=false;
  bool isQ8=false;
  bool isQ9=false;
  bool isQ10=false;


  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  Widget build(BuildContext context) {
    refresh(){
      isQ1=false;
      isQ2=false;
      isQ3=false;
      isQ4=false;
      isQ5=false;
      isQ6=false;
      isQ7=false;
      isQ8=false;
      isQ9=false;
      isQ10=false;
    }
    return SafeArea(
      child: Scaffold(


        body: Container(
          color: Colors.white,
          child: Column(
            children: [
              Expanded(child:Container(
                height: 50,
                child: Row(children: [
                  Container(
                    height: 30,
                    width: 30,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 15.0,right: 15),
                      child: InkWell(
                        child:  Icon(Icons.arrow_back_ios,color: welcomeTextColor,),
                        onTap: () {
                          routeBack(context);
                        },
                      ),
                    ),
                  ),
                InkWell(
                onTap: () {
          routeBack(context);
          },
                  child:  Padding(
                    padding: const EdgeInsets.only(left:15.0,right: 15),
                    child: Image.asset(
                      "assets/images/question.png",
                      height: 23.0,
                      width: 23.0,
                    ),
                  )),
                  Text(
                    "FAQs",
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.w700,
                      fontSize: 20.0,
                    ),
                  )
                ],),
              ),flex: 0,),


              Expanded(child:ListView(children: [
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment:MainAxisAlignment.start ,
                    children: [

                      Padding(
                          padding: const EdgeInsets.only(left: 0.0,right: 0,top: 10,bottom: 20),
                          child: Text("Check out the below commonly asked questions for instant answers.Feel free to contact our support team if you are not fully answerd",style: px18TextStyleNormaBlack,)
                      ),

                      SmallFaqWidget(),


                      Padding(
                        padding: const EdgeInsets.only(top:13.0),
                        child: Container(
                          decoration: BoxDecoration(
                              color: Color(0xFFBDBDBD).withOpacity(.2),
                              border: Border.all(
                                color: Color(0xFFBDBDBD).withOpacity(.2),
                              ),
                              borderRadius: BorderRadius.all(Radius.circular(5))
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment:MainAxisAlignment.start ,
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: Column(
                                  children: [

                                    Row(children: [
                                      Icon(Icons.water_outlined,size: 25,),
                                      Padding(
                                          padding: const EdgeInsets.all(10.0),
                                          child: Text("Notes",style: px18TextStyleBold,)),
                                    ],),
                                    Padding(
                                      padding: const EdgeInsets.only(top:8.0),
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(top:8.0,left: 8),
                                            child: Container(
                                              height: 6,width: 6,
                                              decoration: BoxDecoration(
                                                  color:   Colors.pink,
                                                  border: Border.all(
                                                    color: Colors.pink,
                                                  ),
                                                  borderRadius: BorderRadius.all(Radius.circular(20))
                                              ),),
                                          ),
                                          Expanded(child: Padding(
                                              padding: const EdgeInsets.only(left: 10.0),
                                              child: Text("Always check for updates on MyWorkLink app from your agencies or clients.",style: px14TextStyleNormal,))),
                                        ],
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(top:5.0),
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(top:8.0,left: 8),
                                            child: Container(
                                              height: 6,width: 6,
                                              decoration: BoxDecoration(
                                                  color:   Colors.pink,
                                                  border: Border.all(
                                                    color: Colors.pink,
                                                  ),
                                                  borderRadius: BorderRadius.all(Radius.circular(20))
                                              ),),
                                          ),
                                          Expanded(child: Padding(
                                              padding: const EdgeInsets.only(left: 10.0),
                                              child: Text("Always check your booked shift times.",style: px14TextStyleNormal,))),
                                        ],
                                      ),
                                    ),

                                  ],
                                ),
                              ),





                            ],
                          ),
                        ),
                      ),

                      Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: Text(Faq11,style: px14TextStyleNormal,)),
                      Padding(
                          padding: const EdgeInsets.only(top:0.0,left: 7),
                          child:Row(children: [
                            Icon(Icons.email,color:Colors.pinkAccent,size: 18,),
                            Padding(
                                padding: const EdgeInsets.only(top:0.0,left: 10),
                                child: Text("<EMAIL>",style: px14TextStyleNormalblue,)),
                          ],)),
                      Padding(
                          padding: const EdgeInsets.only(top:10.0,left: 7),
                          child:Row(children: [
                            Icon(Icons.chat,color:Colors.green,size: 18,),
                            Padding(
                                padding: const EdgeInsets.only(top:0.0,left: 10),
                                child: Text("07933272455",style: px14TextStyleNormalblue,)),
                          ],)),


                    ],
                  ),
                ),


              ],) ,flex: 1,)
              ,
            ],
          ),
        ),
      ),
    );
  }


}
