
import 'package:freezed_annotation/freezed_annotation.dart';

part 'available_training.freezed.dart';
part 'available_training.g.dart';

@freezed
abstract class AvailableTrainining with _$AvailableTrainining {
  factory AvailableTrainining({

    int? id,
    String? trainingName,
    int? availableTrainings,


  }) = _AvailableTrainining;

  factory AvailableTrainining.fromJson(Map<String, dynamic> json) =>
      _$AvailableTraininingFromJson(json);
}
