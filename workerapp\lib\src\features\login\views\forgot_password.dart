// import 'package:alan_voice/alan_voice.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/features/auth/logic/auth_provider.dart';
import 'package:work_link/src/features/home/<USER>/home_view.dart';
import 'package:work_link/src/features/login/views/login_view.dart';
import 'package:work_link/src/models/profile/worker_profile.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

final rememberMeProvider = StateProvider<bool>((_) => false);


class ForgotPasswordView extends ConsumerWidget {
  ForgotPasswordView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context,   watch) {
    final authProvider = watch.watch(authNotifierProvider);
    final dialog = watch.watch(dialogProvider);
    final sharedPref = watch.watch(sharedPreferencesServiceProvider);

    return SafeArea(
      child: Scaffold(
        body: RelativeBuilder(builder: (context, height, width, sy, sx) {
          return Center(
            child: Container(
              height: height,
              width: width,
              constraints: BoxConstraints(minWidth: 200, maxWidth: 600),
              child: sharedPref.getCachedUserCredentials() != null
                  ? FutureBuilder(
                future: watch.watch(authRepositoryProvider)
                    .login(sharedPref.getCachedUserCredentials()),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    final wp = snapshot.data;

                    if (wp is WorkerProfile) {



                      SchedulerBinding.instance!.addPostFrameCallback((_) {
                        return routeToWithClear(context, HomeView());
                      });
                    }
                  } else if (snapshot.hasError) {
                    return Center(
                      child: Column(
                        children: [
                          Text('Failed to login'),
                          SizedBox(height: 20),
                          ElevatedButton(
                              onPressed: () async {
                                await sharedPref.resetUserCredentials();

                                SchedulerBinding.instance!
                                    .addPostFrameCallback((_) {
                                  return routeToWithClear(
                                      context, LoginView());
                                });
                              },
                              child: Text('Retry')),
                        ],
                      ),
                    );
                  }

                  return Center(
                      child: Container(child: CircularProgressIndicator()));
                },
              )
                  : Column(
                children: [
                  Expanded(
                    flex: 3,
                    child: Padding(
                        padding: EdgeInsets.only(left: 40, right: 40),
                        child: Image.asset('assets/images/logo.png')
                    ),
                  ),
                  Text('Reset Password')
                      .textColor(welcomeTextColor)
                      .fontWeight(FontWeight.bold)
                      .fontSize(sx(52)),
                  Text('A reset link will be sent to your email.')
                      .textColor(welcomeTextColor)
                      .fontWeight(FontWeight.w400)
                      .fontSize(sx(27)),
                  SizedBox(height: 30),
                  Expanded(
                    flex: 5,
                    child: authProvider.when(
                      initial: () => _Reset(),
                      loading: () =>
                          Center(child: CircularProgressIndicator()),
                      data: (data) {
                        // print(data);
                        SchedulerBinding.instance!
                            .addPostFrameCallback((_) {
                          dialog.showFloatingFlushbar(
                            context: context,
                            title: 'Reset',
                            message: 'A reset link has been send to your email.',
                          );
                        });



                        SchedulerBinding.instance!
                            .addPostFrameCallback((_) {
                          return routeToWithClear(context, HomeView());
                        });

                        return _Reset();
                      },
                      loaded: (data) {
                        // print(data);
                        SchedulerBinding.instance!
                            .addPostFrameCallback((_) {
                          dialog.showFloatingFlushbar(
                            context: context,
                            title: 'Reset',
                            message: 'A reset link has been send to your email.',
                          );
                        });



                        SchedulerBinding.instance!
                            .addPostFrameCallback((_) {
                          return routeToWithClear(context, LoginView());
                        });

                        return _Reset();
                      },
                      // loaded: (loaded) => Text(loaded.toString()),
                      error: (e) {
                        SchedulerBinding.instance!
                            .addPostFrameCallback((_) {
                          dialog.showFloatingFlushbar(
                            context: context,
                            title: 'Reset',
                            message: e.toString(),
                            warning: true,
                          );
                        });
                        // dialog.showFloatingFlushbar(context: context, title: 'Login', message: e.toString());

                        return _Reset();
                      },
                    ),
                  )
                ],
              ),
            ),
          );
        }),
      ),
    );
  }
}

class _Reset extends ConsumerWidget {
  _Reset({Key? key}) : super(key: key);

  @override
  void initState() {
    // TODO: implement initState

    // AlanVoice.addButton("7b078269080cd1a2d6432d3ff250c1f02e956eca572e1d8b807a3e2338fdd0dc/stage");
    // /// Handle commands from Alan Studio
    // AlanVoice.onCommand.add((command) {
    //   debugPrint("got new command ${command.toString()}");
    // });
  }

  final formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context,   watch) {
    final authProvider = watch.watch(authNotifierProvider);
    final rememberMe = watch.watch(rememberMeProvider);

    return RelativeBuilder(builder: (context, height, width, sy, sx) {
      return SingleChildScrollView(
        padding: EdgeInsets.all(sx(10)),
        child: FormBuilder(
          key: formKey,
          child: Column(
            children: [
              formEntryField(
                context: context,
                keyboardType: TextInputType.emailAddress,
                validator: FormBuilderValidators.compose(
                  [
                    FormBuilderValidators.required(errorText: 'required'),
                    FormBuilderValidators.email(errorText: 'Please enter valid email.'),
                  ],
                ),
                title: 'Enter Email Address',
                formName: 'email',
              ),


              SizedBox(height: sy(20)),

              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 163,height: 47,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: loginButonColor,
                        elevation: 5,
                        fixedSize: Size(width * 0.9, sy(30)),
                      ),
                      onPressed: () async {
                        // watch.read(rememberMeProvider) =true;
                        if (formKey.currentState!.validate()) {
                          formKey.currentState?.save();

                          final _data = formKey.currentState?.value;

                          final payload =_data!['email'];

                          watch.read(authNotifierProvider.notifier)
                              .resetState();

                          if (!authProvider.isLoading) {
                            await watch.read(authNotifierProvider.notifier)
                                .resetPwd(
                                payload
                            );
                          }
                        }
                      },
                      child: Text(
                        'Reset',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: sx(25),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 20),
                  Container(
                    width: 163,height: 47,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: loginButonColor,
                        elevation: 5,
                        fixedSize: Size(width * 0.9, sy(30)),
                      ),
                      onPressed: () {
                        Navigator.pop(context);

                      },
                      child: Text(
                        'Sign In',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: sx(25),
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      );
    });
  }
}
