import 'package:flutter/material.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/utils/index.dart';

class ErrorPage extends StatelessWidget {
  const ErrorPage(
      {Key? key, this.error = 'There was a problem!', this.onTryAgain,required this.stackTrace})
      : super(key: key);

  final String? error;
  final StackTrace? stackTrace;
  final Function()? onTryAgain;

  @override
  Widget build(BuildContext context) {
    print(error);
    print(stackTrace);
    return RelativeBuilder(builder: (context, height, width, sy, sx) {
      return Container(
        child: Center(
          child: Column(
            children: [
              Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: sx(50), vertical: sy(20)),
                child: Text(
                  error!,
                  style: TextStyle(fontStyle: FontStyle.italic),
                ).textColor(textColor),
              ),
              Sized<PERSON><PERSON>(height: sy(30)),
              ElevatedButton.icon(
                onPressed: onTryAgain,
                icon: Icon(Icons.refresh),
                label: Text('Try Again'),
              ),
            ],
          ),
        ),
      );
    });
  }
}
