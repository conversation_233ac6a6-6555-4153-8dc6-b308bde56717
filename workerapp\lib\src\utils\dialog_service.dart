import 'package:flutter/material.dart';

class DialogService {
  void showInfoDialog(BuildContext context, String info, String title) async {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            info,
            style: TextStyle(
              fontSize: 14,
            ),
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('CLOSE'),
            ),
          ],
        );
      },
    );
  }

  Future<bool?> showDialogConfirmation(
      BuildContext context, String info, String title) async {
    bool? res = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => AlertDialog(
        title: Text(title),
        content: Text(info),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(true);
            },
            child: Text(
              'YES',
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(false);
            },
            child: Text(
              'NO',
            ),
          ),
        ],
      ),
    );

    return res;
  }

  void showFloatingFlushbar({
    BuildContext? context,
    String? title,
    String? message,
    bool showOnTop = true,
    bool warning = false, // to change color of popup
    bool autoDismiss = false,
  }) {
    if (context == null) return;

    final snackBar = SnackBar(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null && title.isNotEmpty)
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          if (message != null && message.isNotEmpty)
            Text(
              message,
              style: TextStyle(
                fontSize: 14,
                color: Colors.white,
              ),
            ),
        ],
      ),
      backgroundColor: warning
          ? Colors.red.shade800
          : Colors.green.shade800,
      duration: Duration(milliseconds: 7000),
      behavior: SnackBarBehavior.floating,
      margin: EdgeInsets.all(8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      action: SnackBarAction(
        label: 'DISMISS',
        textColor: Colors.white,
        onPressed: () {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
        },
      ),
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}
