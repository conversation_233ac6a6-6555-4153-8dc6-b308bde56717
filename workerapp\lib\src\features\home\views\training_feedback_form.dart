import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:work_link/src/utils/index.dart';
import '../../../widgets/edit_text.dart';
import '../../../models/training_feedback/TrainingFeedback.dart';
import '../../../models/worker_training_session/WorkerTrainingSession.dart';
import '../../../providers/data_repositories/worker_training_session_repository.dart';
import '../../../utils/dialog_service.dart';
import '../../../utils/constants.dart';
import '../../../widgets/appbar_default.dart';

class TrainingFeedbackForm extends StatefulWidget {
  @override
  _TrainingFeedbackFormState createState() => _TrainingFeedbackFormState();

  TrainingFeedbackForm( this.booking, {this.feedback1});

  final WorkerTrainingSession booking;
      TrainingFeedback? feedback1;
}

class _TrainingFeedbackFormState extends State<TrainingFeedbackForm> {

  TrainingFeedback feedback = new TrainingFeedback();
  @override
  void initState() {
    feedback = new TrainingFeedback(booking: widget.booking);
    super.initState();
  }

  Widget build(BuildContext context) {
    {


      _cardHeaderRow(String title, String value){
        return Row(
          children: [
            SizedBox(width: 90, child: Text(title),),
            Text(value, style: TextStyle(fontWeight: FontWeight.bold),)
          ],
        );
      }

      _ratingButton(int num, String title, int current,  void Function() onClick){
        Color col = Colors.white;
        if(current==num) col =  secondaryAccent;
        if(title=="Yes"|| title=="No") col = Colors.white;
        if(title=="Yes" && current == num) col = welcomeTextColor;
        if(title=="No" && current==num) col = secondaryAccent;
        // if(num==current){
        //   if(current==1) col =  Colors.red;
        //   if(current==2) col =  Colors.orange;
        //   if(current==3) col =  Colors.yellow;
        //   if(current==4) col =  Colors.blue;
        //   if(current==5) col =  Colors.green;
        // }

        return GestureDetector(
          child:Container(
            width: 40,
            height: 40,
            decoration: new BoxDecoration(
              // borderRadius: BorderRadius.circular(30),

              color: col,
              shape: BoxShape.circle,
              border: Border.all(width: 1, color: Colors.transparent),


            ),
            child:  Center(
                child: Text(title, style: TextStyle(color: current==num? Colors.white: Colors.black),)
            ),

          ),
          onTap: (){
            onClick();
            setState(() {

            });
          },
        );
      }


      rateCard(title, List<Widget> rates, {bool? hideFooter}){
        return Padding(
          padding: EdgeInsets.only(bottom: 10),
          child: Card(
            color: Colors.white70,
            child: Padding(
                padding: EdgeInsets.all(5),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(title, style: TextStyle(fontWeight: FontWeight.bold),),
                    SizedBox(height: 10),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: rates,
                    ),
                    SizedBox(height: 10),
                    if(hideFooter==null||!hideFooter)Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text("Not good"),
                        Text("Excellent"),
                      ],
                    )
                  ],
                )
            ),
          ),
        );
      }


      return Container(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                  width: double.infinity,
                  color: welcomeTextColor,
                  child: Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Text('Training content',textAlign: TextAlign.center,style: TextStyle(fontSize: 14,color: Colors.white,fontWeight: FontWeight.w500),),
                  )),
              Padding(
                  padding: const EdgeInsets.all(10),
                  child:  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      _cardHeaderRow(" Training",(widget.booking.trainingSession?.training?.name??"")+": "+(widget.booking.trainingSession?.id.toString()??"")) ,
                      _cardHeaderRow(" Location",widget.booking.trainingSession?.shiftLocation?.name??""),
                      _cardHeaderRow(" Date", widget.booking.trainingSession?.startDateTime??""),
                      _cardHeaderRow(" Trainer", widget.booking.trainingSession?.trainer?.name??""),



                      rateCard("Training content",
                          [
                            _ratingButton(1,"1",feedback.content??0,()=> feedback = feedback.copyWith(content: 1)),
                            _ratingButton(2,"2",feedback.content??0,()=> feedback = feedback.copyWith(content: 2)),
                            _ratingButton(3,"3",feedback.content??0,()=> feedback = feedback.copyWith(content: 3)),
                            _ratingButton(4,"4",feedback.content??0,()=> feedback = feedback.copyWith(content: 4)),
                            _ratingButton(5,"5",feedback.content??0,()=> feedback = feedback.copyWith(content: 5)),
                          ]
                      ),


                      rateCard("Training facility",
                        [
                          _ratingButton(1,"1",feedback.facility??0,()=> feedback = feedback.copyWith(facility: 1)),
                          _ratingButton(2,"2",feedback.facility??0,()=> feedback = feedback.copyWith(facility: 2)),
                          _ratingButton(3,"3",feedback.facility??0,()=> feedback = feedback.copyWith(facility: 3)),
                          _ratingButton(4,"4",feedback.facility??0,()=> feedback = feedback.copyWith(facility: 4)),
                          _ratingButton(5,"5",feedback.facility??0,()=> feedback = feedback.copyWith(facility: 5)),
                        ],
                      ),

                      rateCard("Trainer",
                          [
                            _ratingButton(1, "1",feedback.trainer??0,()=> feedback = feedback.copyWith(trainer: 1)),
                            _ratingButton(2, "2",feedback.trainer??0,()=> feedback = feedback.copyWith(trainer: 2)),
                            _ratingButton(3, "3",feedback.trainer??0,()=> feedback = feedback.copyWith(trainer: 3)),
                            _ratingButton(4, "4",feedback.trainer??0,()=> feedback = feedback.copyWith(trainer: 4)),
                            _ratingButton(5, "5",feedback.trainer??0,()=> feedback = feedback.copyWith(trainer: 5)),
                          ]
                      ),


                      rateCard("Training delivery method",
                          [
                            _ratingButton(1, "1",feedback.method??0,()=> feedback = feedback.copyWith(method: 1)),
                            _ratingButton(2, "2",feedback.method??0,()=> feedback = feedback.copyWith(method: 2)),
                            _ratingButton(3, "3",feedback.method??0,()=> feedback = feedback.copyWith(method: 3)),
                            _ratingButton(4, "4",feedback.method??0,()=> feedback = feedback.copyWith(method: 4)),
                            _ratingButton(5, "5",feedback.method??0,()=> feedback = feedback.copyWith(method: 5)),
                          ]
                      ),



                      rateCard("Do you feel the training helped to develop your skills?",
                          [
                            _ratingButton(5,"Yes",feedback.skillsDev==true?5:0,()=> feedback = feedback.copyWith(skillsDev: true)),
                            _ratingButton(1,"No",feedback.skillsDev==false?1:0,()=> feedback = feedback.copyWith(skillsDev: false)),

                          ], hideFooter: true
                      ),


                      rateCard("Was the training relevant to your work??",
                          [
                            _ratingButton(5, "Yes",feedback.isRelevant==true?5:0,()=> feedback = feedback.copyWith(isRelevant: true)),
                            _ratingButton(1, "No",feedback.isRelevant==false?1:0,()=> feedback = feedback.copyWith(isRelevant: false)),

                          ], hideFooter: true
                      ),

                    ],
                  )
              ),

              Padding(
                padding: EdgeInsets.only(left: 10),
                child: Text("Overall comment (optional)"),
              ),

              EditText(
                keyboardType: TextInputType.text,
                maxLength: 400,
                hint: "Enter comment",
                color: background,
                textColor: Colors.black,
                borderColor: background,
                hgt: 120.0,
                isHide: false,
                isValidation: true,
                isReadOnly: false,
                onChanged: (value) {
                  feedback = feedback.copyWith(comment: value);
                },
              ),


              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Consumer(builder: (context1, watch, child) {
                    return ElevatedButton(
                      child: Text('Submit'),
                      onPressed: () {
                        watch.watch(workerTrainingSessionRepositoryProvider).createTrainingFeedback(feedback);
                        DialogService().showFloatingFlushbar(
                          context: context,
                          title: 'Feedback Sent',
                          message: "Thank you for your feedback.",
                        );
                        Navigator.of(context).pop();
                      },
                    );
                  }),
                ],),

              SizedBox( height: 10)
            ],
          ),
        ),
      );
    }
  }
}
