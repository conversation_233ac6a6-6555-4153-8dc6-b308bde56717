import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:ndialog/ndialog.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/models/shift/ShiftsPageResp.dart';

import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../../models/shift/Shift.dart';

void shiftReasonInput(BuildContext context, var formKey, String title, Shift shift, {bool isQuery = false}) {
  showMaterialModalBottomSheet(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(30),
        topRight: Radius.circular(30),
      ),
    ),
    context: context,
    isDismissible: false,
    enableDrag: false,
    useRootNavigator: true,
    builder: (context) =>
        Padding(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: SingleChildScrollView(
            controller: ModalScrollController.of(context),
            child: Consumer(
              builder: (context1, watch, child) {
                final dialog =watch.watch(dialogProvider);
                final shiftRepo =watch.watch(shiftRepoProvider);
                final tranportRepo =watch.watch(transportRepoProvider);
                final _profile =watch.watch(loginResponseProvider);

                return RelativeBuilder(builder: (context, height, width, sy, sx) {
                  return Container(
                    width: width,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: const Radius.circular(20.0),
                        topRight: const Radius.circular(20.0),
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: FormBuilder(
                        key: formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Center(
                              child: Padding(
                                padding: const EdgeInsets.only(top:15.0),
                                child: Text('$title Reason')
                                    .textColor(welcomeTextColor)
                                    .fontSize(sx(27)),
                              ),
                            ),
                            formEntryField(
                              context: context,
                              formName: 'reason',
                              maxLines: 3,
                              unfocus: true,
                              title: 'Reason',
                              validator: FormBuilderValidators.compose(
                                [
                                  FormBuilderValidators.required(
                                      errorText: ''),
                                ],
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Center(
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: ElevatedButton(
                                        style: ElevatedButton.styleFrom(elevation: 8,),
                                        onPressed: () async {
                                          // formKey.currentState?.save();
                                          final _data = formKey.currentState?.value;
                                          final result = await ProgressDialog.future(
                                            context,
                                            dismissable: false,
                                            future:

                                            isQuery ?
                                            shiftRepo.queryAShift(
                                              shiftId: shift.id!,
                                              workerId: _profile!.workerId ?? 0,
                                              reason: _data['reason'],
                                            ):
                                            shift.bookingType=="TRAINING"?
                                            shiftRepo.cancelTrainingBooking(
                                              shiftId: shift.id!,
                                              workerId:
                                              _profile!.workerId!,
                                              reason: _data['reason'],
                                            ):
                                            shift.bookingType=="TRANSPORT"?
                                            tranportRepo.cancelATransport(
                                              transportId: shift.id!,
                                              workerId:
                                              _profile!.workerId!,
                                              reason: _data['reason'],
                                            ):
                                            shiftRepo.cancelAShift(
                                              shiftId: shift.id!,
                                              workerId:
                                              _profile!.workerId!,
                                              reason: _data['reason'],
                                            ),
                                            message: Text("$title a "+(shift.bookingType?.toCapitalized()??"Shift")+"..")
                                                .textColor(textColor),
                                            title: Text("$title "+(shift.bookingType?.toCapitalized()??"Shift"))
                                                .textColor(textColor),
                                            onProgressError: (err) {
                                              print(err);

                                              Navigator.pop(context);
                                            },
                                            onProgressCancel: () =>
                                                Navigator.pop(context),
                                          );

                                          // check result
                                          if (result is bool) {
                                            Navigator.pop(context);

                                            // added ok
                                            dialog.showFloatingFlushbar(
                                              context: context,
                                              title: '$title '+(shift.bookingType?.toCapitalized()??"Shift"),
                                              message:
                                              (shift.bookingType?.toCapitalized()??"Shift")+' has been $title successfully.',
                                            );
                                          }

                                          // err
                                          else {
                                            // added ok
                                            dialog.showFloatingFlushbar(
                                              context: context,
                                              title: '$title '+(shift.bookingType?.toCapitalized()??"Shift"),
                                              message: result.message,
                                              warning: true,
                                            );
                                          }

                                        },
                                        child: Text(
                                          '$title '+(shift.bookingType?.toCapitalized()??"Shift"),
                                          style: TextStyle(
                                            color: Colors.white,
                                            // fontSize: sx(16),
                                          ),
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: sx(20)),
                                    Expanded(
                                      child: ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                          shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.all( Radius.circular(4),),
                                              side: BorderSide(color: welcomeTextColor)
                                          ),
                                          backgroundColor:  Colors.white,
                                        ),
                                        onPressed: () {
                                          Navigator.pop(context);
                                        },
                                        child: Text(
                                          'Back',
                                          style: TextStyle(
                                            color: welcomeTextColor,
                                            // fontSize: sx(16),
                                          ),
                                        ),
                                      ),
                                    ),


                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                });
              },
            ),
          ),
        ),
  );
}
