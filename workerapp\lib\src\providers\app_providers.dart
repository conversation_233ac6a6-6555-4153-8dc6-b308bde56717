import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:work_link/src/providers/data_repositories/invoice_repository.dart';
import 'package:work_link/src/providers/data_repositories/transport_repository.dart';
import 'package:work_link/src/utils/constants.dart';
import 'package:work_link/src/models/auth/login_response.dart';
import 'package:work_link/src/models/custom_exception.dart';
import 'package:work_link/src/models/profile/worker_profile.dart';
import 'package:work_link/src/models/payslip/payslip_response.dart';
import 'package:work_link/src/providers/data_repositories/notification_repository.dart';
import 'package:work_link/src/providers/data_repositories/payslip_repository.dart';
import 'package:work_link/src/providers/data_repositories/shift_repository.dart';

import '../models/invoice/invoice.dart';
import '../utils/dialog_service.dart';
import 'data_repositories/data_repository.dart';

final requestStateProvider = StateProvider((_) => RequestState.None);

final loaderStateProvider = StateProvider<Loader?>((ref) {
  return Loader.None;
});

/// for toggling anything that needs true / false
final toggleProvider = StateProvider<bool?>((ref) {
  return false;
});

/// signup individual or comoany
final accTypeProvider = StateProvider<bool>((ref) {
  return true;
});

final dialogProvider = Provider<DialogService>((_) => DialogService());

final dataRepoProvider =
    Provider<DataRepository>((_) => DataRepository(_.read));

final shiftRepoProvider = Provider<ShiftRepository>((_) => ShiftRepository(_.read));

final transportRepoProvider = Provider<TransportRepository>((_) => TransportRepository(_.read));

final invoiceRepoProvider =
    Provider<InvoiceRepository>((_) => InvoiceRepository(_.read));



final notificationRepoProvider =
Provider<NotificationRepository>((_) => NotificationRepository(_.read));

final payslipRepoProvider =
Provider<PayslipRepository>((_) => PayslipRepository(_.read));

/// loader message
final loaderMessageProvider = StateProvider<String>((ref) {
  return 'loading..';
});

final loggerProvider = Provider<Logger>(
  (ref) => Logger(
    printer: PrettyPrinter(
      methodCount: 1,
      printEmojis: false,
    ),
  ),
);

final customExceptionProvider = StateProvider<CustomException?>((ref) {
  return CustomException();
});

/// user token provider map with refresh & access token
final authKeyProvider = StateProvider<String>((ref) => '');

final workerProfileProvider = StateProvider<WorkerProfile?>((ref) => null);

final payslipProvider = StateProvider<PayslipResponse?>((ref) => null);

final loginResponseProvider = StateProvider<LoginResponse?>((_) => null);

final accessKeyOptionsProvider = StateProvider<Options?>((ref) {
  final authKey = ref.watch(authKeyProvider);

  return Options(headers: {"Authorization": "Bearer $authKey"});
});

/// dio base provider
final dioProvider = Provider<Dio>((ref) {
  return Dio(
    BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: Duration(seconds: 60),
      receiveTimeout: Duration(seconds: 50),
    ),
  );
});
