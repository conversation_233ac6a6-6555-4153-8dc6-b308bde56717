

class WorkerModel {
  int? id;
  String? firstname;
  String? lastname;
  String? gender;
  String? phoneNumber;
  String? email;
  String? username;
  int? assignmentCodeId;
  String? assignmentCode;
  String? employmentNumber;
  String? postcode;
  String? address;
  String? profilePic;
  String? nationality;
  String? dob;
  String? cv;
  String? assignmentName;
  String? status;
  String? agencyName;
  String? createdBy;

  WorkerModel(
      {this.id,
        this.firstname,
        this.lastname,
        this.gender,
        this.phoneNumber,
        this.email,
        this.username,
        this.assignmentCodeId,
        this.assignmentCode,
        this.employmentNumber,
        this.postcode,
        this.address,
        this.profilePic,
        this.nationality,
        this.dob,
        this.cv,
        this.assignmentName,
        this.status,
        this.agencyName,
        this.createdBy});

  WorkerModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstname = json['firstname'].toString();
    lastname = json['lastname'].toString();
    gender = json['gender'].toString();
    phoneNumber = json['phoneNumber'].toString();
    email = json['email'].toString();
    username = json['username'].toString();
    assignmentCodeId = json['assignmentCodeId'];
    assignmentCode = json['assignmentCode'].toString();
    employmentNumber = json['employmentNumber'].toString();
    postcode = json['postcode'].toString();
    address = json['address'].toString();
    profilePic = json['profilePic'].toString();
    nationality = json['nationality'].toString();
    dob = json['dob'].toString();
    cv = json['cv'].toString();
    assignmentName = json['assignmentName'].toString();
    status = json['status'].toString();
    agencyName = json['agencyName'].toString();
    createdBy = json['createdBy'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['firstname'] = this.firstname;
    data['lastname'] = this.lastname;
    data['gender'] = this.gender;
    data['phoneNumber'] = this.phoneNumber;
    data['email'] = this.email;
    data['username'] = this.username;
    data['assignmentCodeId'] = this.assignmentCodeId;
    data['assignmentCode'] = this.assignmentCode;
    data['employmentNumber'] = this.employmentNumber;
    data['postcode'] = this.postcode;
    data['address'] = this.address;
    data['profilePic'] = this.profilePic;
    data['nationality'] = this.nationality;
    data['dob'] = this.dob;
    data['cv'] = this.cv;
    data['assignmentName'] = this.assignmentName;
    data['status'] = this.status;
    data['agencyName'] = this.agencyName;
    data['createdBy'] = this.createdBy;
    return data;
  }
}