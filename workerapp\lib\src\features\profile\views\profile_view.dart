import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ndialog/ndialog.dart';
//import 'package:image_picker/image_picker.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/features/login/views/login_view.dart';
import 'package:work_link/src/features/splash/views/splash_view.dart';
import 'package:work_link/src/models/custom_exception.dart';

import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

import 'avatar_hero.dart';

//final profileImageProvider = StateProvider<XFile?>((_) => null);

final _editProfileProvider = AutoDisposeStateProvider((_) => false);

final options = [
  'Edit Profile',
  'Compliance Information',
  'Training & Qualifications',
  'FAQs',
  'Change Password',
];

class ProfileView extends ConsumerWidget {
  ProfileView({Key? key}) : super(key: key);

  final formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context,   watch) {
    //final _img =watch.watch(profileImageProvider);
    final _editProfile =watch.watch(_editProfileProvider);
    final sharedPref =watch.watch(sharedPreferencesServiceProvider);
    final profile =watch.watch(workerProfileProvider);

    final _dialog =watch.watch(dialogProvider);

    final updaterApi =watch.watch(dataRepoProvider);

    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: welcomeTextColor,
          leading: IconButton(
            onPressed: () => routeBack(context),
            icon: Icon(
              Icons.chevron_left,
              color: Colors.white,
              size: 35,
            ),
          ),
          title: Text(_editProfile ? 'Edit Profile' : 'My Profile'),
          // actions: [
          //   _editProfile.state
          //       ? SizedBox.shrink()
          //       : IconButton(
          //           tooltip: 'edit profile',
          //           onPressed: () =>
          //               watch.read(_editProfileProvider).state = true,
          //           icon: Icon(Icons.edit),
          //         ),
          // ],
          actions: [
            IconButton(
              tooltip: 'logout',
              onPressed: () async {
                final res = await showDialog<bool?>(
                    context: context,
                    builder: (context) {
                      return AlertDialog(
                        title: Text('Log Out'),
                        content: Text('Are you sure you want to logout?'),
                        actions: [
                          TextButton(
                              onPressed: () {
                                Navigator.pop(context, true);
                              },
                              child: Text('Yes')),
                          TextButton(
                              onPressed: () {
                                Navigator.pop(context, false);
                              },
                              child: Text('No')),
                        ],
                      );
                    });

                if (res == null) {
                  return;
                }

                if (res) {
                  await sharedPref.resetUserCredentials();

                  SchedulerBinding.instance!.addPostFrameCallback((_) {
                    return routeToWithClear(context, LoginView());
                  });
                }
              },
              icon: Icon(Icons.logout),
            ),
            PopupMenuButton(
              onSelected: (item) {},
              itemBuilder: (_) => options
                  .map(
                    (e) => PopupMenuItem(
                      child: Text(e),
                      onTap: () {
                        // perform selected option
                        if (e == 'Edit Profile') {
                          watch.watch(_editProfileProvider.notifier).state =
                              !_editProfile;
                        }
                      },
                    ),
                  )
                  .toList(),
            ),
          ],
        ),
        body: RelativeBuilder(builder: (context, height, width, sy, sx) {
          return SingleChildScrollView(
            child: FormBuilder(
              key: formKey,
              enabled: _editProfile,
              initialValue: profile!.toJson(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  FractionallySizedBox(
                    widthFactor: 1,
                    child: Container(
                      height: height * 0.3,
                      // alignment: Alignment.center,
                      child: Stack(
                        alignment: Alignment.topCenter,
                        children: [
                          Container(
                            height: height * 0.2,
                            color: welcomeTextColor,
                          ),
                          Positioned(
                            bottom: 3,
                            child: Container(
                              height: sx(260),
                              width: sx(260),
                              padding: const EdgeInsets.all(20.0),
                              child: AvatarHero(
                                // FIXME: pass profile user image if available
                                contactInfo: profile!.firstname![0]
                                        .toUpperCase() +
                                    profile!.lastname![0].toUpperCase(),
                                indexTag: 'user-id',
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: height * 0.3 * 0.15,
                            right: width * 0.3,
                            child: CircleAvatar(
                              backgroundColor: welcomeTextColor,
                              child: IconButton(
                                onPressed: () {
                                  // TODO: update profile pic if available
                                },
                                icon: Icon(
                                  Icons.camera,
                                  size: sx(32),
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  formEntryField(
                    readOnly: true,
                    context: context,
                    formName: 'firstname',
                    title: 'First Name',
                    unfocus: true,
                    validator: FormBuilderValidators.compose(
                      [
                        FormBuilderValidators.required(errorText: ''),
                      ],
                    ),
                  ),
                  formEntryField(
                    readOnly: true,
                    context: context,
                    formName: 'lastname',
                    title: 'Last Name',
                    unfocus: true,
                    validator: FormBuilderValidators.compose(
                      [
                        FormBuilderValidators.required(errorText: ''),
                      ],
                    ),
                  ),
                  formEntryField(
                    context: context,
                    formName: 'gender',
                    title: 'Gender',
                    unfocus: true,
                    readOnly: true,
                    validator: FormBuilderValidators.compose(
                      [
                        FormBuilderValidators.required(errorText: ''),
                      ],
                    ),
                  ),
                  formEntryField(
                    context: context,
                    formName: 'status',
                    title: 'Account Status',
                    unfocus: true,
                    readOnly: true,
                    validator: FormBuilderValidators.compose(
                      [
                        FormBuilderValidators.required(errorText: ''),
                      ],
                    ),
                  ),
                  SizedBox(height: sy(15)),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text('Contact Details')
                        .textColor(textColor)
                        .fontWeight(FontWeight.w600)
                        .fontSize(sx(32)),
                  ),
                  Divider(),
                  SizedBox(height: sy(5)),
                  formEntryField(
                    context: context,
                    formName: 'phoneNumber',
                    title: 'Phone',
                    unfocus: true,
                    keyboardType: TextInputType.number,
                    validator: FormBuilderValidators.compose(
                      [
                        FormBuilderValidators.required(errorText: ''),
                      ],
                    ),
                  ),
                  formEntryField(
                    context: context,
                    formName: 'email',
                    readOnly: true,
                    title: 'Email Address',
                    unfocus: true,
                    keyboardType: TextInputType.emailAddress,
                    validator: FormBuilderValidators.compose(
                      [
                        FormBuilderValidators.required(errorText: ''),
                        FormBuilderValidators.email(errorText: ''),
                      ],
                    ),
                  ),
                  SizedBox(height: sy(10)),
                  _editProfile
                      ? Padding(
                          padding: EdgeInsets.all(sx(20)),
                          child: Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: confirmGreen,
                                    elevation: 8,
                                    // fixedSize: Size(width, sy(35)),
                                  ),
                                  onPressed: () async {
                                    if (formKey.currentState!.validate()) {
                                      formKey.currentState!.save();
                                      final _data = formKey.currentState!.value;

                                      final profileCopy =
                                          profile?.copyWith(
                                        phoneNumber: _data['phoneNumber'],
                                      );

                                      final result =
                                          await ProgressDialog.future(
                                        context,
                                        dismissable: false,
                                        future: updaterApi
                                            .updateWorker(profileCopy!),
                                        message: Text("updating..")
                                            .textColor(textColor),
                                        title: Text("Profile update")
                                            .textColor(textColor),
                                        //backgroundColor: Colors.white70,
                                        onProgressError: (err) {
                                          Navigator.pop(context);
                                        },
                                        onProgressCancel: () =>
                                            Navigator.pop(context),
                                      );

                                      // disable edit after finishing updating
                                      watch.read(_editProfileProvider.notifier).state =
                                          false;

                                      if (result is bool) {
                                        _dialog.showFloatingFlushbar(
                                          context: context,
                                          title: 'Profile',
                                          message: 'Update successful',
                                        );
                                      }

                                      //

                                      else {
                                        _dialog.showFloatingFlushbar(
                                          context: context,
                                          title: 'Profile',
                                          warning: true,
                                          message: result is CustomException
                                              ? result.message
                                              : 'Update failed. Try again later',
                                        );
                                      }
                                    }
                                  },
                                  icon: Icon(Icons.done),
                                  label: Text('Save')
                                      .fontWeight(FontWeight.w600)
                                      .fontSize(sx(23)),
                                ),
                              ),
                              SizedBox(width: sx(30)),
                              Expanded(
                                child: ElevatedButton.icon(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: denyRed,
                                    elevation: 8,
                                    //  fixedSize: Size(width, sy(35)),
                                  ),
                                  onPressed: () => watch.watch(_editProfileProvider.notifier).state = false,
                                  icon: Icon(Icons.close),
                                  label: Text('Cancel')
                                      .fontWeight(FontWeight.w600)
                                      .fontSize(sx(23)),
                                ),
                              ),
                            ],
                          ),
                        )
                      : SizedBox.shrink(),
                  SizedBox(height: sy(10)),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }
}
