import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// import 'package:flowder_sample/file_models/file_model.dart';
// import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:percent_indicator/percent_indicator.dart';


import 'package:url_launcher/url_launcher.dart';
import 'package:work_link/src/features/payrol/view/payslip_row.dart';
import 'package:work_link/src/features/payrol/view/view_payslip.dart';
import 'package:work_link/src/utils/constants.dart';
import 'package:work_link/src/utils/index.dart';
import '../../../providers/app_providers.dart';
import '../../../models/custom_exception.dart';
import '../../../models/payslip/payslip_response.dart';
import '../../../widgets/error_page.dart';
final _payslipProvider =
AutoDisposeFutureProviderFamily<PayslipResponse?, String>(
        (ref, status) {
      final _payslip = ref.watch(payslipRepoProvider);

      return _payslip.getPayslips();
    });




class PayslipsWidget extends ConsumerWidget {

  PayslipsWidget({Key? key}) : super();




  @override
  Widget build(BuildContext context,   watch) {

    return Column(children: [
      Expanded(
        child: Padding(
          padding: const EdgeInsets.only(top:13.0,left: 25,right: 25),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(child: SizedBox(),flex: 1,),
              Expanded(child: Padding(
                padding: const EdgeInsets.only(top:9.0,left: 22),
                child: Text("Download",

                  style: TextStyle(color: Colors.black),),
              ),flex: 0,),
            ],),
        ),
        flex: 0,
      ),

     watch.watch(_payslipProvider('payslip')).when(
        data: (PayslipResponse? value) {
          if (value != null) {
            final shifts = value.content;

            if (shifts == null) {
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    'failed to get notifications',
                    style: TextStyle(
                      fontStyle: FontStyle.italic,
                      color: Colors.grey,
                    ),
                  ),
                ),
              );
            }

            return shifts.isEmpty
                ? Center(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  'no payslips available',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    color: Colors.grey,
                  ),
                ),
              ),
            )
                :
            Column(
              children: [
                // trailingHeading(status),
                SingleChildScrollView(

                  child: RefreshIndicator(
                    onRefresh: () async {
                      await watch.refresh(_payslipProvider('payslip'));
                    },
                    child: ListView.separated(
                      shrinkWrap: true,
                      padding: EdgeInsets.all(8),
                      itemBuilder: (context, index) {
                        final Content shift = shifts[index]!;

                        return
                          Container(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,

                              children: [
                                Expanded(
                                  flex:5,
                                  child: Padding(

                                      padding: const EdgeInsets.only(right: 25),
                                      child:Container(
                                        height: 35,
                                        color:tileColor,
                                        child: Padding(
                                          padding: const EdgeInsets.only(top:9.0,left: 22),
                                          child: Text(shift.date ?? 'Date unavalable',

                                            style: TextStyle(color: Colors.black),),
                                        ),
                                      )
                                  ),
                                ),
                                PayslipRow(shift: shift,),
                              ],
                            ),
                          );
                      },
                      separatorBuilder: (_, x) => SizedBox(height: 20),
                      itemCount: shifts.length,
                    ),
                  ),
                ),

                Center(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 10, bottom: 20),
                    child: Text(
                      'pull down to refresh',
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
              ],
            );
          }
          // err
          else {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  'failed to get payslips',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    color: Colors.grey,
                  ),
                ),
              ),
            );
          }
        },
        loading: () => Center(
          child: CircularProgressIndicator(),
        ),
        error: (e, st) => Center(
          child: ErrorPage(
            error: e is CustomException ? e.message : e.toString(),
            stackTrace: st,onTryAgain: () => watch.refresh(_payslipProvider('payslip')),
          ),
        ),
      ),




    ],

    );
  }



}
Future<void> _launchUrl(url, String workerId, String payslipPdf) async {
  String _baseUrl = baseUrl;
  var _url = Uri.parse( _baseUrl + url+"/"+workerId+"/"+payslipPdf);
  if (!await launchUrl(_url)) {
    throw 'Could not launch $_url';
  }
}