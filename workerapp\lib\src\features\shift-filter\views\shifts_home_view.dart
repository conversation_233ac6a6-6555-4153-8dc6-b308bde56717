import 'package:flutter/material.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/features/shifts/data/status_shifts.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

import 'shift_view.dart';

class ShiftsHomeFilterView extends StatelessWidget {
  const ShiftsHomeFilterView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppAppBar(context,
          leading: IconButton(
            onPressed: () => routeBack(context),
            icon: Icon(
              Icons.chevron_left,
              color: welcomeTextColor,
              size: 35,
            ),
          )),
      body: RelativeBuilder(builder: (context, height, width, sy, sx) {
        return DefaultTabController(
          length: statusShifts.length,
          child: Scaffold(
            appBar: PreferredSize(
              preferredSize: const Size.fromHeight(kToolbarHeight + 16),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: TabBar(
                    indicatorColor: welcomeTextColor,
                    isScrollable: true,
                    indicatorWeight: 5,
                    tabs: statusShifts
                        .map(
                          (e) => Container(
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Text(e.category)
                                  .textColor(textColor)
                                  .fontSize(sx(25)),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ),
              ),
            ),
            body: TabBarView(
              children: statusShifts.map((e) => ShiftView(status: e)).toList(),
            ),
          ),
        );
      }),
    );
  }
}
