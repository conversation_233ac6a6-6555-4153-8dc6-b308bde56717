import 'package:freezed_annotation/freezed_annotation.dart';

part 'ShiftContent.freezed.dart';
part 'ShiftContent.g.dart';

// // date formats utils
// DateTime? createdOnFromJson(String pDate) => DateTime.parse(pDate);
// String createdOnToJson(DateTime? date) => date!.toIso8601String();

@freezed
abstract class ShiftContent with _$ShiftContent {
  factory ShiftContent({
    String? agency,
    bool? allowAgency,
        DateTime? authorizedDate,
        DateTime? bookedDate,
        DateTime? cancelledDate,
    String? cancelledReason,
    String? client,
    String? createdBy,
    String? directorate,
    String? gender,
    int? hoursBeforeBroadcasting,
    int? id,
    String? notes,
    String? primaryCode,
        DateTime? queriedDate,
    String? queriedReason,
    String? reason,
    String? secondaryCode,
    String? shiftDate,
    String? shiftEndTime,
    String? shiftLocation,
    String? shiftStartTime,
    String? shiftStatus,
    String? shiftType,
    bool? showNoteToAgency,
    bool? showNoteToFw,
    String? worker,
  }) = _ShiftContent;

  factory ShiftContent.fromJson(Map<String, dynamic> json) =>
      _$ShiftContentFromJson(json);
}
