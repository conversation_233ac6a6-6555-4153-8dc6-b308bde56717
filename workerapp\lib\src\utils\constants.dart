import 'package:intl/intl.dart';

const bool useTestUrl = true;

const String testBaseUrl = 'https://api.myworklink.uk/';
// const String testBaseUrl = 'http://localhost:8765/';
// const String testBaseUrl = 'http://********:8765/';
const String webBaseUrl = useTestUrl ? 'https://online.myworklink.uk/' : 'https://online.myworklink.uk/';
const String liveBaseUrl = 'https://api.myworklink.uk/';
const String baseUrl = useTestUrl ? testBaseUrl : liveBaseUrl;
const String worklinkApiUrl = baseUrl+dataService+'/api/v1/';
const String dataService = 'worklink-service'; // worklink-api-test // worklink-api-live
const String authService = 'oauth-service';
const String userService = 'user-service';
final dateTimeFormat = new DateFormat('dd/MM/yyyy hh:mm');
final dateFormat = new DateFormat('dd/MM/yyyy');
final timeFormat = new DateFormat('hh:mm');

/// loader state enum
enum Loader {
  None,
  Loading,
  Complete,
  Error,
}  

enum RequestState {
  None,
  Loaded,
  Error,
  Failed,
}
