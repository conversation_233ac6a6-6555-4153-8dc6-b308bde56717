import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:ndialog/ndialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smart_calendar/controller/smart_calendar_controller.dart'; // Temporarily disabled
// import 'package:smart_calendar/smart_calendar.dart'; // Temporarily disabled
import 'package:styled_widget/styled_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/widgets/ApiCallingWithoutProgressIndicator.dart';
import 'package:work_link/src/widgets/CustomProgressDialog.dart';
import 'package:work_link/src/features/trainings/book_physical_training_widget.dart';
import 'package:work_link/src/utils/colors.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../utils/UserPreference.dart';
import '../../models/training-session/training_session.dart';
import '../../utils/color_constants.dart';
import '../../utils/constants.dart';
import '../../models/training-session/training_session_response/training_sessions_response.dart';
import '../../providers/data_repositories/data_repository.dart';
import '../../utils/dialog_service.dart';
import '../drawer/model/training_model.dart';

class BookPhysicalTrainingsListWidget extends StatefulWidget {
  BookPhysicalTrainingsListWidget(
     this.title, this.trainingId
    );

  String title;
  int trainingId;

  @override
  BookPhysicalTrainingsListWidgetState createState() => BookPhysicalTrainingsListWidgetState();
}

class BookPhysicalTrainingsListWidgetState extends State<BookPhysicalTrainingsListWidget> {

  List<TrainingSession>? trainingSessionsList;
  SharedPreferences? prefs;
  bool registeredOnline = false;

  init() async {
    prefs = await SharedPreferences.getInstance();
    String? workerId = await prefs!.getString(UserPreference.WORKER_ID);
    registeredOnline =  prefs!.getString(UserPreference.hascoId)!=null?true:false;
    getTrainingData(workerId);
  }


  getTrainingData(workerId) async {
    try {
      CustomProgressLoader.showLoader(context);
      Response? response = await ApiCalling()
          .apiCall(context, "$dataService/api/v1/training-sessions/worker/${workerId}/${widget.trainingId}", "get");

      CustomProgressLoader.cancelLoader(context);
      if (response != null) {
        if (response.statusCode == 200) {
          var l = response.data as List;

          final List<Map<String, dynamic>> rates = List.from(l);

          trainingSessionsList =  rates.map((e) => TrainingSession.fromJson(e)).toList();

          print("Data");
          setState(() {

          });
        }
      }
    } catch (e) {
      print("issue shubh" + e.toString());
      rethrow;
    }
  }



  @override
  void initState() {
    // TODO: implement initState
    init();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {


    return Scaffold(
      appBar: AppBarDefault(context, "Book "+widget.title,
          leading: Container(
            height: 30,
            width: 30,
            child: Padding(
              padding: const EdgeInsets.only(left: 15.0, right: 15),
              child: InkWell(
                child: Icon(   Icons.arrow_back,
                  color: welcomeTextColor,
                  size: 20,
                ),
                onTap: () {
                  routeBack(context);
                },
              ),
            ),
          )),
      body: SafeArea(
          child: Container(
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    flex: 1,
                    child:  Padding(
                      padding: const EdgeInsets.all(defaultPadding),
                      child: SingleChildScrollView( child:Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only( ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Container(
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 9.0),
                                      child: Text(
                                        "DATE",
                                        style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                  ),
                                  flex: 2,
                                ),

                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 9.0),
                                    child: Text(
                                      "TIME" ,
                                      style: TextStyle(
                                          color: Colors.black,
                                          fontSize: 13,
                                          fontWeight: FontWeight.bold),
                                    ),
                                  ),
                                  flex: 2,
                                ),

                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 9.0),
                                    child: Text(
                                      "LOCATION" ,
                                      style: TextStyle(
                                          color: Colors.black,
                                          fontSize: 13,
                                          fontWeight: FontWeight.bold),
                                    ),
                                  ),
                                  flex: 2,
                                ),
                                Expanded(
                                  child: Container(
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 9.0),
                                      child: Text(
                                        "ACTION",textAlign: TextAlign.center,
                                        style: TextStyle(
                                            color: Colors.black,
                                            fontSize: 13,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                  ),
                                  flex: 1,
                                ),
                              ],
                            ),
                          ),
                          trainingSessionsList!=null&&trainingSessionsList!=null&&trainingSessionsList!.length>0
                              ?
                          Column(
                              children: List.generate(trainingSessionsList!.length, (int index) {
                                return Padding(
                                  padding: const EdgeInsets.only(left:0.0,right: 0),
                                  child: Container(
                                    color: index%2==0?Color(0xffE6E6E6):Colors.white,
                                    child: Padding(
                                      padding:
                                      const EdgeInsets.only( left: 5, right: 15,bottom: 7,top: 7),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [

                                          Expanded(
                                            child:  Padding(
                                              padding: EdgeInsets.only(top:4),
                                              // child: Text(wp![index]!.name!,
                                              child: Text((trainingSessionsList![index]!.startDateTime?.split(" ")[0]??"") +" to "
                                                  +(trainingSessionsList![index]!.endDateTime?.split(" ")[0]??""),
                                                  style: TextStyle(
                                                      color: Colors.black,
                                                      fontSize: 13,
                                                      fontWeight: FontWeight.normal)),
                                            ),

                                            flex: 2,
                                          ),
                                          Expanded(
                                            child:  Padding(
                                              padding: EdgeInsets.only(top:4),
                                              // child: Text(wp![index]!.name!,
                                              child: Text((trainingSessionsList![index]!.startDateTime?.split(" ")[1]??"") +" to "
                                                  +(trainingSessionsList![index]!.endDateTime?.split(" ")[1]??""),
                                                  style: TextStyle(
                                                      color: Colors.black,
                                                      fontSize: 13,
                                                      fontWeight: FontWeight.normal)),
                                            ),

                                            flex: 2,
                                          ),
                                          Expanded(
                                            child:  Padding(
                                              padding: EdgeInsets.only(top:4, left: 6),
                                              // child: Text(wp![index]!.name!,
                                              child: Text((trainingSessionsList![index]!.shiftLocationName ??""),
                                                  style: TextStyle(
                                                      color: Colors.black,
                                                      fontSize: 13,
                                                      fontWeight: FontWeight.normal)),
                                            ),

                                            flex: 2,
                                          ),

                                          Expanded(
                                            child:  Padding(
                                              padding: const EdgeInsets.only(left:8.0),
                                              child: InkWell(
                                                  onTap: (){
                                                    print("Well");
                                                    routeTo(context, BookPhysicalTrainingWidget(trainingSessionsList![index]!.id!));
                                                  },
                                                  child: Icon(
                                                    Icons.remove_red_eye,
                                                    color: welcomeTextColor,
                                                  )),
                                            ),

                                            flex: 1,
                                          ),


                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              }))
                              :SizedBox(height: 10,),




                        ],
                      ),),
                    ))
              ],
            ),
          )),
    );
  }
}
