// To parse this JSON data, do
//
//     final workerAgency = workerAgencyFromJson(jsonString);

import 'dart:convert';

WorkerAgency workerAgencyFromJson(String str) =>
    WorkerAgency.fromJson(json.decode(str));

class WorkerAgency {
  WorkerAgency({
    this.content,
  });

  final List<Content>? content;

  factory WorkerAgency.fromJson(Map<String, dynamic> json) => WorkerAgency(
        content:
            List<Content>.from(json["content"].map((x) => Content.fromJson(x))),
      );
}

class Content {
  Content({
    this.agencyId,
    this.name,
    this.email,
    this.telephone,
    this.address,
    this.logo,
    this.billingEmail,
    this.status,
    this.createdBy,
    this.service,
  });

  final int? agencyId;
  final String? name;
  final String? email;
  final String? telephone;
  final Address? address;
  final String? logo;
  final String? billingEmail;
  final String? status;
  final String? createdBy;
  final String? service;

  factory Content.fromJson(Map<String, dynamic> json) => Content(
        agencyId: json["agencyId"],
        name: json["name"],
        email: json["email"],
        telephone: json["telephone"],
        address: Address.fromJson(json["address"]),
        logo: json["logo"],
        billingEmail: json["billingEmail"],
        status: json["status"],
        createdBy: json["createdBy"],
        service: json["service"],
      );
}

class Address {
  Address({
    this.firstLine,
    this.town,
    this.postcode,
  });

  final String? firstLine;
  final String? town;
  final String? postcode;

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        firstLine: json["firstLine"],
        town: json["town"],
        postcode: json["postcode"],
      );

  Map<String, dynamic> toJson() => {
        "firstLine": firstLine,
        "town": town,
        "postcode": postcode,
      };
}
