import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:work_link/src/models/agency_expense_rate/AgencyExpenseRate.dart';
// import '../shift.dart';
import '../shift/Shift.dart';

part 'ShiftExpenseClaim.freezed.dart';
part 'ShiftExpenseClaim.g.dart';

@freezed
abstract class ShiftExpenseClaim with _$ShiftExpenseClaim {
  factory ShiftExpenseClaim({
   int? id,
   double? rate,
    double? amount,
   String? description,
   String? status,
   AgencyExpenseRate? agencyExpenseRate,
   Shift? shift,
  }) = _ShiftExpenseClaim;

  factory ShiftExpenseClaim.fromJson(Map<String, dynamic> json) =>
      _$ShiftExpenseClaimFromJson(json);
}
