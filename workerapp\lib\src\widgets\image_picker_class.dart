import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

import '../utils/index.dart';

class ImagePickerUtility{
  File? image;
  File? cameraImage;
  File? video;
  File? cameraVideo;
  File? document;
  BuildContext? context;

  bool isImage = true;
  bool isDoc = false;

  // This funcion will helps you to pick and Image from Gallery
  static Future<File?> getImageFromGallery() async {
    XFile? _image;
    _image = await ImagePicker().pickImage(source: ImageSource.gallery,imageQuality: 100);
    print('_image:: $_image');
    File? file= await getCroppedImage(_image);
    return file;
  }
  // This funcion will helps you to pick and Image from Camera
  static Future<File?> getImageFromCamera() async {
    XFile? _image;
    _image = await ImagePicker().pickImage(source: ImageSource.camera,imageQuality: 100);
    File? file= await getCroppedImage(_image);
    return file;
  }

  static Future<File?> getCroppedImage(XFile? image) async {
    if (image == null) return null;
    CroppedFile? croppedFile = await ImageCropper().cropImage(
        sourcePath: image.path,
        compressQuality: 100,
        compressFormat: ImageCompressFormat.png,
        uiSettings: [
          AndroidUiSettings(
              toolbarTitle: 'Cropper',
              toolbarColor: welcomeTextColor,
              toolbarWidgetColor: Colors.white,
              initAspectRatio: CropAspectRatioPreset.original,
              lockAspectRatio: true,
              aspectRatioPresets: [
                CropAspectRatioPreset.square,
                // CropAspectRatioPreset.ratio3x2,
                // CropAspectRatioPreset.original,
                // CropAspectRatioPreset.ratio4x3,
                // CropAspectRatioPreset.ratio16x9,
              ]),
          IOSUiSettings(
            minimumAspectRatio: 1.0,
            aspectRatioPresets: [
              CropAspectRatioPreset.square,
              // CropAspectRatioPreset.ratio3x2,
              // CropAspectRatioPreset.original,
              // CropAspectRatioPreset.ratio4x3,
              // CropAspectRatioPreset.ratio16x9,
            ]
          )
        ]
    );
    return croppedFile != null ? File(croppedFile.path) : null;
  }

}
