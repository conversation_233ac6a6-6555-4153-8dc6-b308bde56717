import '../../../models/Pageable.dart';

class ComplianceModel {
  List<Content>? content;
  bool? empty;
  bool? first;
  bool? last;
  int? number;
  int? numberOfElements;
  Pageable? pageable;
  int? size;
  Sort? sort;
  int? totalElements;
  int? totalPages;

  ComplianceModel(
      {this.content,
        this.empty,
        this.first,
        this.last,
        this.number,
        this.numberOfElements,
        this.pageable,
        this.size,
        this.sort,
        this.totalElements,
        this.totalPages});

  ComplianceModel.fromJson(Map<String, dynamic> json) {
    if (json['content'] != null) {
      content = <Content>[];
      json['content'].forEach((v) {
        content!.add(new Content.fromJson(v));
      });
    }
    empty = json['empty'];
    first = json['first'];
    last = json['last'];
    number = json['number'];
    numberOfElements = json['numberOfElements'];
    pageable = json['pageable'] != null
        ? new Pageable.fromJson(json['pageable'])
        : null;
    size = json['size'];
    sort = json['sort'] != null ? new Sort.fromJson(json['sort']) : null;
    totalElements = json['totalElements'];
    totalPages = json['totalPages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.content != null) {
      data['content'] = this.content!.map((v) => v.toJson()).toList();
    }
    data['empty'] = this.empty;
    data['first'] = this.first;
    data['last'] = this.last;
    data['number'] = this.number;
    data['numberOfElements'] = this.numberOfElements;
    if (this.pageable != null) {
      data['pageable'] = this.pageable!.toJson();
    }
    data['size'] = this.size;
    if (this.sort != null) {
      data['sort'] = this.sort!.toJson();
    }
    data['totalElements'] = this.totalElements;
    data['totalPages'] = this.totalPages;
    return data;
  }
}

class Content {
  String? agencyId;
  String? code;
  String? description;
  int? id;
  String? name;
  String? serviceId;
  String? complianceDate;
  String? complianceExpiry;
  String? workerId;
  String? complianceId;

  Content(
      {this.agencyId,
        this.code,
        this.description,
        this.id,
        this.name,
        this.serviceId,
        this.complianceDate,
        this.complianceExpiry,
        this.complianceId,
        this.workerId});

  Content.fromJson(Map<String, dynamic> json) {
    agencyId = json['agencyId'];
    code = json['code'];
    description = json['description'];
    id = json['id'];
    name = json['name'];
    serviceId = json['serviceId'];
    complianceDate = json['complianceDate'];
    complianceExpiry = json['complianceExpiry'];
    complianceId = json['complianceId'];
    workerId = json['workerId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['agencyId'] = this.agencyId;
    data['code'] = this.code;
    data['description'] = this.description;
    data['id'] = this.id;
    data['name'] = this.name;
    data['serviceId'] = this.serviceId;
    data['complianceDate'] = this.complianceDate;
    data['complianceExpiry'] = this.complianceExpiry;
    data['workerId'] = this.workerId;
    data['complianceId'] = this.complianceId;
    return data;
  }
}


class Sort {
  bool? empty;
  bool? sorted;
  bool? unsorted;

  Sort({this.empty, this.sorted, this.unsorted});

  Sort.fromJson(Map<String, dynamic> json) {
    empty = json['empty'];
    sorted = json['sorted'];
    unsorted = json['unsorted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['empty'] = this.empty;
    data['sorted'] = this.sorted;
    data['unsorted'] = this.unsorted;
    return data;
  }
}