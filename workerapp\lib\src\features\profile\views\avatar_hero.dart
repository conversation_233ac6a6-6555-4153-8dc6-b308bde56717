import 'package:flutter/material.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:styled_widget/styled_widget.dart';

class AvatarHero extends StatelessWidget {
  final String? contactInfo;
  final String? indexTag;
  final bool isName; // if true, show initials

  const AvatarHero({
    required this.contactInfo,
    this.indexTag,
    this.isName = true,
  });

  @override
  Widget build(BuildContext context) {
    final child = RelativeBuilder(builder: (context, height, width, sy, sx) {
      return CircleAvatar(
        radius: sx(50),
        backgroundColor: buttonBgColor1,
        child: isName
            ? Text(contactInfo!)
                .textColor(Colors.white)
                .fontWeight(FontWeight.bold)
                .fontSize(sx(43))
            : null,
        backgroundImage: isName
            ? null
            : NetworkImage(
                contactInfo!,
              ),
      );
    });

    return Hero(
      tag: 'image_$indexTag',
      createRectTween: (begin, end) {
        return RectTween(
          begin: Rect.fromCenter(
            center: begin!.center,
            width: begin.width,
            height: begin.height,
          ),
          end: Rect.fromCenter(
            center: end!.center,
            width: end.width,
            height: end.height,
          ),
        );
      },
      child: child,
    );
  }
}
