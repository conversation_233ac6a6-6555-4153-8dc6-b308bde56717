import 'package:flutter/material.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/utils/color_constants.dart';
import 'package:work_link/src/features/shift-filter/views/shifts_home_view.dart';
import 'package:work_link/src/features/shifts/data/status_shifts.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../../utils/responsive.dart';
import 'shift_view.dart';

class ShiftsHomeView extends StatelessWidget {
  const ShiftsHomeView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarDefault(context,"Bookings",
          leading: IconButton(
            onPressed: () => routeBack(context),
            icon: Icon(
              Icons.arrow_back,
              color: welcomeTextColor,
              size: 20,
            ),
          )),
      body: RelativeBuilder(builder: (context, height, width, sy, sx) {
        return DefaultTabController(
          length: statusShifts.length,
          child: Center(
            child: Container(
              // constraints: BoxConstraints(minWidth: 200, maxWidth: 600),
              child: Scaffold(

                appBar: PreferredSize(

                  preferredSize: const Size.fromHeight(kToolbarHeight ),
                  child: Container(
                    color: welcomeTextColor,

                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Align(
                        alignment: Alignment.center,
                        child: Container(
                          height: 40,
                          child: TabBar(
                            labelStyle: TextStyle(
                              fontSize: 16,
                                background: Paint()
                                  // ..strokeWidth = 100
                                  ..color = Colors.white
                                  ..style = PaintingStyle.fill
                                  ..strokeJoin = StrokeJoin.round
                                  ..strokeCap = StrokeCap.round

                            ),
                            labelColor: welcomeTextColor,
                            unselectedLabelColor: Colors.grey,

                            unselectedLabelStyle: TextStyle(
                              fontSize: 16,
                                background: Paint()
                                  // ..strokeWidth = 80
                                  ..color = Colors.white
                                  ..style = PaintingStyle.fill
                                  ..strokeJoin = StrokeJoin.round
                                  ..strokeCap = StrokeCap.round

                            ),
                            overlayColor: MaterialStateProperty.all<Color>(Colors.red),

                            isScrollable: true,
                            indicatorWeight: 0.0001,
                            tabs: statusShifts
                                .map(
                                  (e) => Container(
                                padding: EdgeInsets.all(5),
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.all(Radius.circular(5))
                                ),
                                child:  Text(e.category),
                              ),
                            )
                                .toList(),
                          ),
                        )
                    ),
                  ),
                ),
                body: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(17),
                        topRight: Radius.circular(17)
                    ),
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(color: welcomeTextColor, spreadRadius: 10),
                    ],
                  ),

                  child: TabBarView(
                    children: statusShifts.map((e) => ShiftView(status: e)).toList(),
                  ),
                ),
              ),
            ),
          ),
        );
      }),
    );
  }
}
