import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:ndialog/ndialog.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/models/worker-agencies/worker_agency.dart';

import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../../models/custom_exception.dart';
import '../../../models/shift/Shift.dart';
// import '../../../models/worker-agencies/worker_agency.dart';

const List<String> list = <String>['One', 'Two', 'Three', 'Four'];
int selected = 0;

final _agencyProvider = AutoDisposeFutureProvider((ref) {
  final api = ref.read(dataRepoProvider);

  return api.getWorkerAgencies();
});

Color getColor(int value){
  if(value == selected){
    return welcomeTextColor;
  }else{
    return Colors.black54;
  }
}
String dropdownValue = list.first;

void selectAgencyInput(
    BuildContext context, var formKey, String title, Shift shift,
    {bool isQuery = false}) {
      showMaterialModalBottomSheet(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(30),
        topRight: Radius.circular(30),
      ),
    ),
    context: context,
    isDismissible: false,
    enableDrag: false,
    useRootNavigator: true,
    builder: (context) => Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: SingleChildScrollView(
        controller: ModalScrollController.of(context),
        child: Consumer(
          builder: (context1, watch, child) {
            final dialog = watch.watch(dialogProvider);
            final shiftRepo = watch.watch(shiftRepoProvider);
            final _profile = watch.watch(loginResponseProvider);

            final api = watch.watch(_agencyProvider);

            return RelativeBuilder(builder: (context, height, width, sy, sx) {
              return Container(
                // height: 420,
                width: width,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: const Radius.circular(20.0),
                    topRight: const Radius.circular(20.0),
                  ),
                ),
                child: SingleChildScrollView(
                  child: FormBuilder(
                    key: formKey,
                    child: Column(
                      //  mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: Container(
                              height: 7,
                              width: 70,
                              decoration: BoxDecoration(
                                color: Colors.grey,
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                          ),
                        ),
                        if(shift.bookingType=="TRAINING" && shift.publishToAllAgencies != true)Center(
                          child: Padding(
                            padding: const EdgeInsets.all(15.0),
                            child: Text("Select Business")
                                .textColor(Colors.black54)
                                .fontSize(sx(25)),
                          ),
                        ),
                        StatefulBuilder(builder: (context, setState) =>



                        api.when(
                          data: (data) {
                            final WorkerAgency wa = data;

                            // if(shift.agencies != null) {
                            //   wa.content?.removeWhere((item) =>
                            //       !shift.agencies!.contains(item.agencyId));
                            // }

                            var s = shift.agencies;
                            if(shift.bookingType=="TRAINING" && shift.publishToAllAgencies != true){
                              wa.content?.removeWhere((item) =>
                              !shift.agencies!.contains(item.agencyId));
                            }else{
                              if(!wa.content!.isEmpty) {
                                  selected = wa.content![0]!.agencyId!;
                                }
                              }

                            return wa.content!.isEmpty
                                ? Center(
                                child: Padding(
                                  padding:
                                  const EdgeInsets.symmetric(vertical: 50),
                                  child: Text('no matched agency found')
                                      .textColor(Colors.grey),
                                ))
                                : shift.bookingType=="TRAINING" && shift.publishToAllAgencies != true?
                                  ListView.separated(
                                physics: NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemBuilder: (ctx, index) {
                                var item = wa.content![index];

                                  return ListTile(
                                    onTap: () {
                                      // This is called when the user selects an item.
                                      setState(() {
                                        selected = item.agencyId!;
                                      });
                                    },
                                    leading: CircleAvatar(
                                      child: Icon(Icons.groups),
                                    ),
                                    tileColor: tileColor,
                                    title: Text(item.name!)
                                        .fontWeight(FontWeight.w600)
                                        .textColor(getColor(item.agencyId!)),
                                    subtitle: Text(item.service!),
                                );
                              },
                                  separatorBuilder: (ctx, x) => Divider(height: 10),
                                  itemCount: wa.content!.length,
                            ):
                            SizedBox();
                          },
                          loading: () => Center(
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: CircularProgressIndicator(),
                            ),
                          ),
                          error: (e, st) {
                            if (e is CustomException) {
                              return ErrorPage(
                                stackTrace: StackTrace.current,
                                error: e.message,
                                onTryAgain: () =>
                                    watch.refresh(_agencyProvider),
                              );
                            } else {
                              return ErrorPage(
                                stackTrace: StackTrace.current,
                                error: 'failed to get agencies',
                                onTryAgain: () =>
                                    watch.refresh(_agencyProvider),
                              );
                            }
                          },
                        ),
                        ),

                        Center(
                          child: Padding(
                            padding: const EdgeInsets.all(15.0),
                          ),
                        ),

                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Center(
                            child: Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                      elevation: 8,
                                      // fixedSize: Size(width * 0.9, sy(40)),
                                    ),
                                    onPressed: () async {
                                      if(selected==0){
                                        return ProgressDialog.future(
                                          context,
                                          dismissable: false,
                                          future: shiftRepo.applyAShift(
                                            shiftId: shift.id!,
                                            workerId: _profile!.workerId ?? 1,
                                            agencyId: selected,
                                          ),
                                          message: Text("Please select an agency or try again.")
                                              .textColor(textColor),
                                          title: Text("Business selection").textColor(textColor),
                                          //backgroundColor: Colors.white70,
                                          onProgressError: (err) {
                                            print(err);
                                            Navigator.pop(context);
                                          },
                                          onProgressCancel: () => Navigator.pop(context),
                                        );
                                      }
                                      // apply a shift
                                      final result = await ProgressDialog.future(
                                        context,
                                        dismissable: false,
                                        future: shift.bookingType=="TRAINING" ?
                                        shiftRepo.applyTraining(
                                          shiftId: shift.id!,
                                          workerId: _profile!.workerId ?? 1,
                                          agencyId: selected,
                                        ) :
                                        shiftRepo.applyAShift(
                                          shiftId: shift.id!,
                                          workerId: _profile!.workerId ?? 1,
                                          agencyId: selected,
                                        ),
                                        message: Text("applying ...")
                                            .textColor(textColor),
                                        title: Text("Apply").textColor(textColor),
                                        //backgroundColor: Colors.white70,
                                        onProgressError: (err) {
                                          print(err);

                                          Navigator.pop(context);
                                        },
                                        onProgressCancel: () => Navigator.pop(context),
                                      );

                                      // check result
                                      if (result is bool) {
                                        // added ok
                                        dialog.showFloatingFlushbar(
                                          context: context,
                                          title: 'Apply',
                                          message:
                                          'Applied successfully.',
                                        );
                                        selected = 0;
                                        Navigator.pop(context);
                                      }

                                      // err
                                      else {
                                        // added ok
                                        dialog.showFloatingFlushbar(
                                          context: context,
                                          title: 'Apply',
                                          message: result.message,
                                          warning: true,
                                        );
                                        selected = 0;
                                        Navigator.pop(context);
                                      }
                                    },
                                    child: Text(
                                      '$title',
                                      style: TextStyle(
                                        color: Colors.white,
                                        // fontSize: sx(16),
                                      ),
                                    ),

                                  ),
                                ),
                                SizedBox(width: sx(20)),
                                Expanded(
                                  child: ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                      shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.all( Radius.circular(4),),
                                          side: BorderSide(color: welcomeTextColor)
                                      ),
                                      backgroundColor:  Colors.white,
                                    ),
                                    onPressed: () {
                                      selected = 0;
                                      Navigator.pop(context);
                                    },
                                    child: Text(
                                      'Back',
                                      style: TextStyle(
                                        color: welcomeTextColor,
                                        // fontSize: sx(16),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            });
          },
        ),
      ),
    ),
  );


}





