// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAuHS0I1Ur4rLKYWOWReSHbhrYY73Sp_Ho',
    appId: '1:415143296224:web:274ce2e286c916edc12485',
    messagingSenderId: '415143296224',
    projectId: 'worklink-f3bb5',
    authDomain: 'worklink-f3bb5.firebaseapp.com',
    storageBucket: 'worklink-f3bb5.appspot.com',
    measurementId: 'G-CZRYWWPGKX',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDtut074qSW4VNvZSnCUsh45Ykt82YLgl0',
    appId: '1:415143296224:android:dfe48b5c99fdb08bc12485',
    messagingSenderId: '415143296224',
    projectId: 'worklink-f3bb5',
    storageBucket: 'worklink-f3bb5.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyD3PqV-6wCO66VRNfz4ZV6N_QiRAK7BBtk',
    appId: '1:415143296224:ios:1c279d30e87fbfe0c12485',
    messagingSenderId: '415143296224',
    projectId: 'worklink-f3bb5',
    storageBucket: 'worklink-f3bb5.appspot.com',
    iosClientId: '415143296224-agmt02vf3ii3iahnlkvj8n3n7h0f1or4.apps.googleusercontent.com',
    iosBundleId: 'com.donnclab.workLink',
  );
}
