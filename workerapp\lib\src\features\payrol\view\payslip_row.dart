import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:path_provider/path_provider.dart';
import 'package:work_link/src/features/payrol/view/view_payslip.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../utils/constants.dart';
import '../../../models/payslip/payslip_response.dart';

import '../../../utils/colors.dart';

class PayslipRow extends StatefulWidget with WidgetsBindingObserver {
  PayslipRow({Key? key, required this.shift}) : super(key: key);

  final Content shift;

  @override
  _PayslipRowState createState() => _PayslipRowState();
}

class _PayslipRowState extends State<PayslipRow> {
  @override
  Widget build(BuildContext context) {
    Content shift = widget.shift;

    return Expanded(
      flex: 1,
      child: InkWell(
          onTap: () async {
            String url = shift.payslipPDF?? "" ;
            // _launchUrl(url);
            launch(url);

          },
          child: Icon(
            Icons.download,
            color: welcomeTextColor,
          )),
    );
  }

  Future<void> requestDownload(String _url, String _name) async {
    final dir =
        await getApplicationDocumentsDirectory(); //From path_provider package
    var _localPath = dir.path + _name;
    final savedDir = Directory(_localPath);
    await savedDir.create(recursive: true).then((value) async {
      String? _taskid = await FlutterDownloader.enqueue(
        url: _url,
        fileName: _name,
        savedDir: _localPath,
        showNotification: true,
        openFileFromNotification: false,
      );
      print(_taskid);
    });
  }
}

Future<void> _launchUrl(url) async {
  String _baseUrl = url;
  var _url = Uri.parse(_baseUrl);
  if (!await launchUrl(_url)) {
    throw 'Could not launch $_url';
  }
}
