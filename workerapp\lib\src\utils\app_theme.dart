import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'index.dart';

final ThemeData theme = ThemeData(
  tabBarTheme: TabBarTheme(labelColor: Colors.black),


  appBarTheme: AppBarTheme(backgroundColor: Colors.white, foregroundColor: welcomeTextColor, elevation: 0),
  textButtonTheme: TextButtonThemeData(style: TextButton.styleFrom(foregroundColor: welcomeTextColor),),
  primaryColor: welcomeTextColor,

  colorScheme: ColorScheme.fromSwatch().copyWith(primary: welcomeTextColor),
  visualDensity: VisualDensity.adaptivePlatformDensity,
  textTheme: GoogleFonts.latoTextTheme(),
);
