import 'shift_category.dart';

final List<ShiftCategoryStatus> statusShifts = [
  ShiftCategoryStatus(status: 'NEW', category: 'New'),

  ShiftCategoryStatus(status: 'APPLIED', category: 'Applied'),
  ShiftCategoryStatus(status: 'BOOKED', category: 'Booked'),
  ShiftCategoryStatus(
      status: 'AWAITING_AUTHORIZATION', category: 'Awaiting'),
  ShiftCategoryStatus(status: 'AUTHORIZED', category: 'Authorized'),
  ShiftCategoryStatus(status: 'IN_QUERY', category: 'Queried'),
  ShiftCategoryStatus(status: 'CANCELLED', category: 'Closed'),
];
