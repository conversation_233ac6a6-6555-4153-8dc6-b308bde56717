
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import '../../../widgets/ApiCallingWithoutProgressIndicator.dart';
import '../../../widgets/CustomProgressDialog.dart';
import '../../../utils/UserPreference.dart';
import '../../../utils/constants.dart';
import '../../../widgets/app_appbar.dart';
import '../model/DirectorateModel.dart';
import 'client_directors_detail.dart';


class ClientDirector extends StatefulWidget {
  String id;
  ClientDirector(this.id);
  @override
  ClientDirectorState createState() => ClientDirectorState();
}

class ClientDirectorState extends State<ClientDirector> {
  final TextEditingController _firstNameController = TextEditingController();


  final _formKey = GlobalKey<FormState>();
  DirectorateModel? wp;

  getProfileData() async {
    try {
      CustomProgressLoader.showLoader(context);
      Response? response = await ApiCalling()
          .apiCall(context, "$dataService/api/v1/shift-directorates-client/${widget.id}/0/300", "get");
      CustomProgressLoader.cancelLoader(context);
      print("response++" + response.toString());
      if (response != null) {
        if (response.statusCode == 200) {
          wp = DirectorateModel.fromJson(response.data);
          setState(() {

          });
        }
      }
    } catch (e) {

      print("issue shubh" + e.toString());
      return null;
    }
  }


  SharedPreferences? prefs;
  init() async {
    prefs = await SharedPreferences.getInstance();
    getProfileData();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    init();
  }


  bool AtzFilter = true;
  bool likedData = false;

  Widget build(BuildContext context) {




    MediaQueryData mediaQuery = MediaQuery.of(context);
    return Scaffold(
      appBar:   AppBarDefault(context ,"Client Directorates", leading:
      Container(
        height: 30,
        width: 30,
        child: Padding(
          padding: const EdgeInsets.only(left: 15.0,right: 15),
          child: InkWell(
            child:  Icon(Icons.arrow_back_ios,color: welcomeTextColor,),
            onTap: () {
              routeBack(context);
            },
          ),
        ),
      ) ),
      body: wp!=null?Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // Padding(
          //   padding: const EdgeInsets.fromLTRB(10.0,20,10,5),
          //   child: Text("Client Directorates",style: TextStyle(fontSize: 16,fontWeight:FontWeight.bold )),
          // ),
          // Padding(
          //     padding: const EdgeInsets.fromLTRB(10.0,5,10,0),
          //     child:Container(
          //       height: 60,
          //       color: lightgreyColor.withOpacity(.2),
          //       child: Row(children: [
          //         Expanded(child: Padding(
          //           padding: const EdgeInsets.only(left: 13.0),
          //           child: Text("Sort By",style: TextStyle(fontSize: 18),),
          //         ) ,flex: 1,),
          //
          //
          //         Expanded(child: Row(
          //           mainAxisAlignment: MainAxisAlignment.center
          //           ,children: [
          //           Image.asset(
          //             "assets/images/heart.png",
          //             height: 20.0,
          //             width: 20.0,
          //           ),
          //           Container(
          //
          //               child: new Checkbox(
          //                   value: likedData,
          //                   onChanged: (e){
          //                     setState(() {
          //                       if(likedData){
          //                         likedData=false;
          //                       }else{
          //                         likedData=true;
          //                       }
          //                     });
          //                   },
          //                   activeColor: Colors.blue))
          //         ],) ,flex: 1,),
          //
          //         Expanded(child: Row(
          //           mainAxisAlignment: MainAxisAlignment.center,
          //           children: [
          //             Text("AZ",style: TextStyle(fontSize: 18),),
          //             Container(
          //                 width:50,
          //                 child: new Checkbox(
          //                     value: AtzFilter,
          //                     onChanged: (e){
          //                       setState(() {
          //                         if(AtzFilter){
          //                           AtzFilter=false;
          //                         }else{
          //                           AtzFilter=true;
          //                         }
          //                       });
          //                     },
          //                     activeColor: Colors.blue)),
          //           ],) ,flex: 1,),
          //
          //       ],),
          //     )),
          Column(children:List.generate(
              wp!.content!.length, (int index) {

            Content cli = wp!.content![index];
            return Padding(
              padding: const EdgeInsets.only(right: 35.0,top: 10),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(flex: 1,
                    child: Card(
                      margin: const EdgeInsets.only(left:10,right:10,top:8.0),
                      elevation: 1.0,
                      color: accentPrimaryColor,
                      child:   Padding(
                        padding: const EdgeInsets.only(left:20.0,top: 10,bottom: 10),
                        child:Text(
                          cli.name.toString(),
                          textAlign: TextAlign.start,
                          style: GoogleFonts.viga(
                              fontStyle: FontStyle.normal,
                              fontSize: 15.0,
                              color: Colors.black87),
                        ),
                      ),
                    ),
                  ),

                  Expanded(child:   Padding(
                    padding: const EdgeInsets.only(top:15.0),
                    child: Row(children: [
                      InkWell(
                          onTap: (){
                            routeTo(context, ClientDirectorDetail(cli));
                          },
                          child: Icon(Icons.info,color:welcomeTextColor ,size: 30,)),
                      // Padding(
                      //   padding: const EdgeInsets.only(left: 6,right: 5),
                      //   child: Image.asset(
                      //     "assets/images/heart.png",
                      //     height: 28.0,
                      //     width: 28.0,
                      //   ),
                      // ),
                    ],),
                  ),flex: 0,),
                ],
              ),
            )
            ;

          }),),
        ],
      ): SizedBox(),
    )
    ;
  }


}
