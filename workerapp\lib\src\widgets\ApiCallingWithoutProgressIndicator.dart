import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:work_link/src/utils/constants.dart';

import 'dart:async';
import 'dart:io';
import 'dart:ui';

import '../utils/UserPreference.dart';


class ApiCalling {


  //========================================= Api Calling ============================
  Future<Response?> apiCall(context1, url, type) async {
    final response = await apiCallForUserProfile(url, type);
    return response;
  }

  Future<Response?> apiCallForUserProfile(url, type) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? accessToken=  prefs.getString(UserPreference.accessToken);

      var dio = Dio();
      dio.options.baseUrl = baseUrl;
      dio.options.connectTimeout = Duration(seconds: 30); //5s
      dio.options.receiveTimeout = Duration(seconds: 30);
      dio.options.headers = {'user-agent': 'dio'};
      dio.options.headers = {'Accept': 'application/json'};
      dio.options.headers = {'Content-Type': 'application/json'};
      dio.options.headers = {'Authorization': 'Bearer $accessToken'};
      print("accesstoken+++"+accessToken!);
      // Make API call
      if (type == 'get') {
        return await dio.get(url);
      } else {
        return await dio.post(url);
      }

    } catch (e) {
      print("response++error++" + e.toString());
      if (e.toString().contains("401")) {}
      print(e);
      return null;
    }
  }



  Future<Response?> apiCallpost(context1, url,map, type) async {
    final response = await apiCallForPostD(url, type,map);
    return response;
  }

  Future<Response?> apiCallForPostD(url, type,map) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? accessToken=  prefs.getString(UserPreference.accessToken);
      var dio = Dio();
      dio.options.baseUrl = baseUrl;
      dio.options.connectTimeout = Duration(seconds: 30); //5s
      dio.options.receiveTimeout = Duration(seconds: 30);
      dio.options.headers = {'user-agent': 'dio'};
      dio.options.headers = {'Accept': 'application/json'};
      dio.options.headers = {'Content-Type': 'application/json'};
      dio.options.headers = {'Authorization': 'Bearer $accessToken'};
      print("accesstoken+++"+accessToken!);
      // Make API call
      if (type == 'get') {
        return await dio.get(url);
      } else {
        return  await dio.post(url, data: json.encode(map));
      }

    } catch (e) {
      print("response++error++" + e.toString());
      if (e.toString().contains("401")) {}
      print(e);
      return null;
    }
  }

  //============================================Api Calling put with Param ===============================

  Future<Response?> apiCallPutWithMapData(context1, url, map) async {
    final response = await apiCallPut(url, map);
    return response;
  }

  Future<Response?>  apiCallPut(url, map) async {
      try {
        SharedPreferences prefs = await SharedPreferences.getInstance();
        String? accessToken=  prefs.getString(UserPreference.accessToken);


        var dio =  Dio();

        dio.options.baseUrl = baseUrl;
        dio.options.connectTimeout = Duration(seconds: 30); //5s
        dio.options.receiveTimeout = Duration(seconds: 30);
        dio.options.headers = {'user-agent': 'dio'};
        dio.options.headers = {'Accept': 'application/json'};
        dio.options.headers = {'Content-Type': 'application/json'};
        dio.options.headers = {'Authorization': 'Bearer $accessToken'};
        print("accesstoken+++"+accessToken!);
       return  await dio.put(url, data: json.encode(map));




      } catch (e) {
        return null;
      }

  }


  Future<Response?> apiCallFormData(context1, url, map) async {
    final response = await apiCallData(url, map);
    return response;
  }

  Future<Response?>  apiCallData(url, dynamic data) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? accessToken=  prefs.getString(UserPreference.accessToken);



      var dio =  Dio();

      dio.options.baseUrl = baseUrl;
      dio.options.connectTimeout = Duration(seconds: 30); //5s
      dio.options.receiveTimeout = Duration(seconds: 30);
      dio.options.headers = {'user-agent': 'dio'};
      dio.options.headers = {'Accept': 'application/json'};
      dio.options.headers = {'Content-Type': 'application/json'};
      dio.options.headers = {'Authorization': 'Bearer $accessToken'};
      print("accesstoken+++"+accessToken!);
      return  await dio.put(url, data: data);




    } catch (e) {
      return null;
    }

  }
}
