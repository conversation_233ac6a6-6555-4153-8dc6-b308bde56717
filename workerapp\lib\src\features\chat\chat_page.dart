import 'package:flutter/material.dart';
import 'package:work_link/src/utils/color_constants.dart';
import 'package:work_link/src/widgets/appbar_default.dart';

import '../../utils/colors.dart';


class ChatPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarDefault(  context,  'Carpooling Chat',),
      body: Column(
        children: [
          earnPoints(),
          ShiftDetails(),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              '3 Participants',
              style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: ListView(
              children: [
                ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.pink,
                    child: Text('RC'),
                  ),
                  title: Text('Accepted your carpooling request!'),
                ),
                ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.blue,
                    child: Text('EZ'),
                  ),
                  title: Text('Is requesting to join the trip to work'),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ElevatedButton(
                        onPressed: () {},
                        child: Text('Reject'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.pink,
                        ),
                      ),
                      SizedBox(width: 8.0),
                      ElevatedButton(
                        onPressed: () {},
                        child: Text('Admit'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ),
                MessageBubble(
                  senderInitials: 'TM',
                  messageText: 'Good day, what time do we leave?',
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Message...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send),
                  onPressed: () {},
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

Widget earnPoints()=>        Container(
  color: Colors.green,
  padding: EdgeInsets.all(8.0),
  child: Row(
    children: [
      Icon(Icons.eco, color: Colors.white),
      SizedBox(width: 8.0),
      Expanded(
        child: Text(
          'Earn points every time you use Worklink Carpooling feature!',
          style: TextStyle(color: Colors.white),
        ),
      ),
      Icon(Icons.arrow_forward_ios_rounded, color: Colors.white),
    ],
  ),
);


class ShiftDetails extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return       Container(
       color: Colors.grey.shade200,
      padding: EdgeInsets.all( defaultPadding),
      child:Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [

   Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                decoration: BoxDecoration(
                  color: Colors.teal,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Text(
                  'Shift',
                  style: TextStyle(color: Colors.white),
                ),
              ),
              SizedBox(width: 8.0),
              Text(
                '08 OCT 2023 | 08:00 - 15:30',
                style: TextStyle(fontSize: 16.0),
              ),
            ],
          ),

        SizedBox(height: 8.0),
        Text(
          'Kingstone',
          style: TextStyle(fontSize: 16.0),
        ),
      ],
      ),);
  }
}






class MessageBubble extends StatelessWidget {
  final String senderInitials;
  final String messageText;

  MessageBubble({required this.senderInitials, required this.messageText});

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.symmetric(horizontal: defaultPadding+2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            backgroundColor: Colors.yellow,
            child: Text(
              senderInitials,
              style: TextStyle(color: Colors.white),
            ),
          ),
          SizedBox(width: 10),
          Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              messageText,
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }
}
