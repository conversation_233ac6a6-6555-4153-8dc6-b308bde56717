// To parse this JSON data, do
//
//     final shiftControllerShiftModel = shiftControllerShiftModelFromMap(jsonString?);

import 'dart:convert';

class ShiftControllerShiftModel {
  ShiftControllerShiftModel({
    this.agencies,
    this.agency,
    this.appliedDate,
    this.assignmentCode,
    this.authorizedDate,
    this.bookedDate,
    this.breakTime,
    this.cancelledDate,
    this.cancelledReason,
    this.client,
    this.createdBy,
    this.directorate,
    this.gender,
    this.hoursBeforeBroadcasting,
    this.id,
    this.notes,
    this.queriedDate,
    this.queriedReason,
    this.shiftDate,
    this.shiftEndTime,
    this.shiftLocation,
    this.shiftStartTime,
    this.shiftStatus,
    this.shiftType,
    this.showNoteToAgency,
    this.showNoteToFw,
    this.worker,
  });

  final List<Agency?>? agencies;
  final String? agency;
  final DateTime? appliedDate;
  final String? assignmentCode;
  final DateTime? authorizedDate;
  final DateTime? bookedDate;
  final String? breakTime;
  final DateTime? cancelledDate;
  final String? cancelledReason;
  final String? client;
  final String? createdBy;
  final String? directorate;
  final String? gender;
  final int? hoursBeforeBroadcasting;
  final int? id;
  final String? notes;
  final DateTime? queriedDate;
  final String? queriedReason;
  final String? shiftDate;
  final String? shiftEndTime;
  final String? shiftLocation;
  final String? shiftStartTime;
  final String? shiftStatus;
  final String? shiftType;
  final bool? showNoteToAgency;
  final bool? showNoteToFw;
  final String? worker;

  ShiftControllerShiftModel copyWith({
    List<Agency?>? agencies,
    String? agency,
    DateTime? appliedDate,
    String? assignmentCode,
    DateTime? authorizedDate,
    DateTime? bookedDate,
    String? breakTime,
    DateTime? cancelledDate,
    String? cancelledReason,
    String? client,
    String? createdBy,
    String? directorate,
    String? gender,
    int? hoursBeforeBroadcasting,
    int? id,
    String? notes,
    DateTime? queriedDate,
    String? queriedReason,
    String? shiftDate,
    String? shiftEndTime,
    String? shiftLocation,
    String? shiftStartTime,
    String? shiftStatus,
    String? shiftType,
    bool? showNoteToAgency,
    bool? showNoteToFw,
    String? worker,
  }) =>
      ShiftControllerShiftModel(
        agencies: agencies ?? this.agencies,
        agency: agency ?? this.agency,
        appliedDate: appliedDate ?? this.appliedDate,
        assignmentCode: assignmentCode ?? this.assignmentCode,
        authorizedDate: authorizedDate ?? this.authorizedDate,
        bookedDate: bookedDate ?? this.bookedDate,
        breakTime: breakTime ?? this.breakTime,
        cancelledDate: cancelledDate ?? this.cancelledDate,
        cancelledReason: cancelledReason ?? this.cancelledReason,
        client: client ?? this.client,
        createdBy: createdBy ?? this.createdBy,
        directorate: directorate ?? this.directorate,
        gender: gender ?? this.gender,
        hoursBeforeBroadcasting:
            hoursBeforeBroadcasting ?? this.hoursBeforeBroadcasting,
        id: id ?? this.id,
        notes: notes ?? this.notes,
        queriedDate: queriedDate ?? this.queriedDate,
        queriedReason: queriedReason ?? this.queriedReason,
        shiftDate: shiftDate ?? this.shiftDate,
        shiftEndTime: shiftEndTime ?? this.shiftEndTime,
        shiftLocation: shiftLocation ?? this.shiftLocation,
        shiftStartTime: shiftStartTime ?? this.shiftStartTime,
        shiftStatus: shiftStatus ?? this.shiftStatus,
        shiftType: shiftType ?? this.shiftType,
        showNoteToAgency: showNoteToAgency ?? this.showNoteToAgency,
        showNoteToFw: showNoteToFw ?? this.showNoteToFw,
        worker: worker ?? this.worker,
      );

  factory ShiftControllerShiftModel.fromJson(String? str) =>
      ShiftControllerShiftModel.fromMap(json.decode(str!));

  String? toJson() => json.encode(toMap());

  factory ShiftControllerShiftModel.fromMap(Map<String?, dynamic>? json) =>
      ShiftControllerShiftModel(
        agencies:
            List<Agency?>.from(json!["agencies"].map((x) => Agency.fromMap(x))),
        agency: json["agency"],
        appliedDate: DateTime?.parse(json["appliedDate"]),
        assignmentCode: json["assignmentCode"],
        authorizedDate: DateTime?.parse(json["authorizedDate"]),
        bookedDate: DateTime?.parse(json["bookedDate"]),
        breakTime: json["breakTime"],
        cancelledDate: DateTime?.parse(json["cancelledDate"]),
        cancelledReason: json["cancelledReason"],
        client: json["client"],
        createdBy: json["createdBy"],
        directorate: json["directorate"],
        gender: json["gender"],
        hoursBeforeBroadcasting: json["hoursBeforeBroadcasting"],
        id: json["id"],
        notes: json["notes"],
        queriedDate: DateTime?.parse(json["queriedDate"]),
        queriedReason: json["queriedReason"],
        shiftDate: json["shiftDate"],
        shiftEndTime: json["shiftEndTime"],
        shiftLocation: json["shiftLocation"],
        shiftStartTime: json["shiftStartTime"],
        shiftStatus: json["shiftStatus"],
        shiftType: json["shiftType"],
        showNoteToAgency: json["showNoteToAgency"],
        showNoteToFw: json["showNoteToFw"],
        worker: json["worker"],
      );

  Map<String?, dynamic>? toMap() => {
        "agencies": List<dynamic>.from(agencies!.map((x) => x!.toMap())),
        "agency": agency,
        "appliedDate": appliedDate?.toIso8601String(),
        "assignmentCode": assignmentCode,
        "authorizedDate": authorizedDate?.toIso8601String(),
        "bookedDate": bookedDate?.toIso8601String(),
        "breakTime": breakTime,
        "cancelledDate": cancelledDate?.toIso8601String(),
        "cancelledReason": cancelledReason,
        "client": client,
        "createdBy": createdBy,
        "directorate": directorate,
        "gender": gender,
        "hoursBeforeBroadcasting": hoursBeforeBroadcasting,
        "id": id,
        "notes": notes,
        "queriedDate": queriedDate?.toIso8601String(),
        "queriedReason": queriedReason,
        "shiftDate": shiftDate,
        "shiftEndTime": shiftEndTime,
        "shiftLocation": shiftLocation,
        "shiftStartTime": shiftStartTime,
        "shiftStatus": shiftStatus,
        "shiftType": shiftType,
        "showNoteToAgency": showNoteToAgency,
        "showNoteToFw": showNoteToFw,
        "worker": worker,
      };
}

class Agency {
  Agency({
    this.address,
    this.billingEmail,
    this.clients,
    this.email,
    this.id,
    this.logo,
    this.name,
    this.service,
    this.status,
    this.telephone,
    this.version,
    this.agencys,
  });

  final Address? address;
  final String? billingEmail;
  final List<Agency?>? clients;
  final String? email;
  final int? id;
  final String? logo;
  final String? name;
  final String? service;
  // final Service? service;
  final String? status;
  final String? telephone;
  final int? version;
  final List<dynamic>? agencys;

  Agency copyWith({
    Address? address,
    String? billingEmail,
    List<Agency?>? clients,
    String? email,
    int? id,
    String? logo,
    String? name,
    String? service,
    // Service? service,
    String? status,
    String? telephone,
    int? version,
    List<dynamic>? agencys,
  }) =>
      Agency(
        address: address ?? this.address,
        billingEmail: billingEmail ?? this.billingEmail,
        clients: clients ?? this.clients,
        email: email ?? this.email,
        id: id ?? this.id,
        logo: logo ?? this.logo,
        name: name ?? this.name,
        service: service ?? this.service,
        status: status ?? this.status,
        telephone: telephone ?? this.telephone,
        version: version ?? this.version,
        agencys: agencys ?? this.agencys,
      );

  factory Agency.fromJson(String? str) => Agency.fromMap(json.decode(str!));

  String? toJson() => json.encode(toMap());

  factory Agency.fromMap(Map<String, dynamic> json) => Agency(
        billingEmail: json["billingEmail"],
        address: Address.fromMap(json["address"]),
        clients: json["clients"] == null
            ? null
            : List<Agency?>.from(json["clients"].map((x) => Agency.fromMap(x))),
        email: json["email"],
        id: json["id"],
        logo: json["logo"],
        name: json["name"],
        service: json["service"],
        status: json["status"],
        telephone: json["telephone"],
        version: json["version"],
        agencys: json["agencys"] == null
            ? null
            : List<dynamic>.from(json["agencys"].map((x) => x)),
      );

  Map<String, dynamic> toMap() => {
        "address": address?.toMap(),
        "billingEmail": billingEmail,
        "clients": clients == null
            ? null
            : List<dynamic>.from(clients!.map((x) => x?.toMap())),
        "email": email,
        "id": id,
        "logo": logo,
        "name": name,
        "service": service,
        "status": status,
        "telephone": telephone,
        "version": version,
        "agencys":
            agencys == null ? null : List<dynamic>.from(agencys!.map((x) => x)),
      };
}

class Address {
  Address({
    this.county,
    this.firstLine,
    this.postcode,
    this.secondLine,
    this.town,
  });

  final String? county;
  final String? firstLine;
  final String? postcode;
  final String? secondLine;
  final String? town;

  Address copyWith({
    String? county,
    String? firstLine,
    String? postcode,
    String? secondLine,
    String? town,
  }) =>
      Address(
        county: county ?? this.county,
        firstLine: firstLine ?? this.firstLine,
        postcode: postcode ?? this.postcode,
        secondLine: secondLine ?? this.secondLine,
        town: town ?? this.town,
      );

  factory Address.fromJson(String? str) => Address.fromMap(json.decode(str!));

  String? toJson() => json.encode(toMap());

  factory Address.fromMap(Map<String, dynamic> json) => Address(
        county: json["county"],
        firstLine: json["firstLine"],
        postcode: json["postcode"],
        secondLine: json["secondLine"],
        town: json["town"],
      );

  Map<String, dynamic> toMap() => {
        "county": county,
        "firstLine": firstLine,
        "postcode": postcode,
        "secondLine": secondLine,
        "town": town,
      };
}

class Service {
  Service({
    this.id,
    this.name,
    this.version,
  });

  final int? id;
  final String? name;
  final int? version;

  Service copyWith({
    int? id,
    String? name,
    int? version,
  }) =>
      Service(
        id: id ?? this.id,
        name: name ?? this.name,
        version: version ?? this.version,
      );

  factory Service.fromJson(String? str) => Service.fromMap(json.decode(str!));

  String? toJson() => json.encode(toMap());

  factory Service.fromMap(Map<String, dynamic> json) => Service(
        id: json["id"],
        name: json["name"],
        version: json["version"],
      );

  Map<String?, dynamic>? toMap() => {
        "id": id,
        "name": name,
        "version": version,
      };
}
