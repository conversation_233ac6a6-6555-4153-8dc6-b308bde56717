
import 'package:freezed_annotation/freezed_annotation.dart';

part 'Address.freezed.dart';
part 'Address.g.dart';

@freezed
abstract class Address with _$Address {
  factory Address({

     String? county,
     String? firstLine,
     String? postcode,
     String? secondLine,
     String? town,


  }) = _Address;

  factory Address.fromJson(Map<String, dynamic> json) =>
      _$AddressFromJson(json);
}
