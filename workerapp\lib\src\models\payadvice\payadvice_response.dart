// To parse this JSON data, do
//
//     final PayAdviceResponse = PayAdviceResponseFromJson(jsonString);

import 'dart:convert';

import 'package:work_link/src/models/profile/worker_profile.dart';

import '../shift_controller_shift_model.dart';

class PayAdviceResponse {


  PayAdviceResponse({
    this.id,
    this.date,
    this.worker,
    this.agency,
    this.workerId,
    this.agentId,
    this.totalHrs,
    this.totalAmount,
    this.payAdviceDate,
    this.payAdvicePDF,
    this.workerGross,
    this.payAdviceStatus,
    this.payAdviceItemResult,
    this.shifts,
  });

  int? id;
  String? date;
  int? workerId;
  int? agentId;
  double? totalHrs;
  double? totalAmount;
  String? payAdviceDate;
  String? payAdvicePDF;
  WorkerProfile? worker;
  Agency? agency;
  String? workerGross;
  String? payAdviceStatus;
  List<PayAdviceItemResult>? payAdviceItemResult;
  List<PayAdviceItemResult>? shifts;



  factory PayAdviceResponse.fromJson(Map<String, dynamic> json) =>
      PayAdviceResponse(
        payAdviceItemResult: List<PayAdviceItemResult>.from(json["payAdviceItemResult"].map((x) => PayAdviceItemResult.fromJson(x))),
        // shifts: List<PayAdviceItemResult>.from(json["payAdviceItemResult"].map((x) => PayAdviceItemResult.fromJson(x))),
        id: json["id"],
        date: json["date"],
        workerId: json["workerId"],
        agentId: json["agentId"],
        totalHrs: json["totalHrs"],
        totalAmount: json["totalAmount"],
        payAdviceDate: json ["payAdviceDate"],
        payAdvicePDF: json["payAdvicePDF"],
        workerGross: json["workerGross"],
        payAdviceStatus: json["payAdviceStatus"],
      );


}

List<PayAdviceItemResult> contentFromJson(String str) => List<PayAdviceItemResult>.from(json.decode(str).map((x) => PayAdviceItemResult.fromJson(x)));


class PayAdviceItemResult {

  PayAdviceItemResult({
    this.id,
    this.shiftId,
    this.dayOfTheWeek,
    this.startTime,
    this.endTime,
    this.startDate,
    this.endDate,
    this.directorate,
    this.numberOfHoursWorked,
    this.rate,
    this.total,
    this.getYearGross,
    this.getPayDate,
  });

  int? id;
  int? shiftId;
  String? dayOfTheWeek;
  String? startTime;
  String? endTime;
  String? startDate;
  String? endDate;
  String? directorate;
  double? numberOfHoursWorked;
  double? rate;
  double? total;
  double? getYearGross;
  String? getPayDate;

  factory PayAdviceItemResult.fromJson(Map<String, dynamic> json) => PayAdviceItemResult(
   id: json["id"],
   shiftId: json["shiftId"],
   dayOfTheWeek: json["dayOfTheWeek"],
   startTime: json["startTime"],
   endTime: json["endTime"],
   startDate: json["startDate"],
   endDate: json["endDate"],
   directorate: json["directorate"],
   numberOfHoursWorked: json["numberOfHoursWorked"],
   rate: json["rate"],
   total: json["total"],
   getYearGross: json["getYearGross"],
   getPayDate: json["getPayDate"]

  );


  String? getIndex(int index,) {
    switch (index) {
    // case 0:
    //   return id.toString();
      case 0:
        return shiftId.toString();
      case 1:
        return directorate;
      case 2:
        return total.toString();
      case 3:
        return "\$"+total!.toStringAsFixed(2);
    }
    return '';
  }

}
