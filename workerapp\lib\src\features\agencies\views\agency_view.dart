import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import '../../../widgets/ApiCallingWithoutProgressIndicator.dart';
import '../../../widgets/CustomProgressDialog.dart';
import '../../../utils/UserPreference.dart';
import '../../../utils/constants.dart';
import '../../../widgets/app_appbar.dart';
import '../model/AgencyModel.dart';

class AgencyView extends StatefulWidget {
  @override
  ClientViewNewState createState() => ClientViewNewState();
}

class ClientViewNewState extends State<AgencyView> {
  TextEditingController searchController = new TextEditingController();
  List<Content>? agencyClientList = [];
  bool isSearching = false;
  Timer? _timer;

  final _formKey = GlobalKey<FormState>();
  AgencyModel? wp;

  getProfileData(workerId) async {
    try {
      CustomProgressLoader.showLoader(context);
      print("api call wp+++++" + workerId);
      Response? response = await ApiCalling().apiCall(
          context,
          "$dataService/api/v1/worker-agency/" + workerId + "/0/300",
          "get");
      CustomProgressLoader.cancelLoader(context);
      print("response++" + response.toString());
      if (response != null) {
        if (response.statusCode == 200) {
          wp = AgencyModel.fromJson(response.data);
          setState(() {});
        }
      }
    } catch (e) {
      print("issue shubh" + e.toString());
      return null;
    }
  }

  SharedPreferences? prefs;

  init() async {
    prefs = await SharedPreferences.getInstance();
    String? workerId = await prefs!.getString(UserPreference.WORKER_ID);
    getProfileData(workerId);
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    init();
  }

  Widget build(BuildContext context) {
    MediaQueryData mediaQuery = MediaQuery.of(context);
    return Scaffold(
      appBar: AppBarDefault(context,"Agencies",
          leading: Container(
            height: 30,
            width: 30,
            child: Padding(
              padding: const EdgeInsets.only(left: 15.0, right: 15),
              child: InkWell(
                child: Icon(   Icons.arrow_back,
                  color: welcomeTextColor,
                  size: 20,
                ),
                onTap: () {
                  routeBack(context);
                },
              ),
            ),
          )),
      body: wp != null
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                // Expanded(
                //   child: Padding(
                //     padding: EdgeInsets.only(
                //         left: 10.0, right: 10, top: 5, bottom: 10),
                //     child: Container(
                //       width: 200,
                //       height: 45,
                //       child: TextField(
                //         onChanged: (value) {
                //           print("value+++" + value);
                //           if (value.toString().trim().length == 0) {
                //             setState(() {
                //               isSearching = false;
                //             });
                //           } else {
                //             setState(() {
                //               isSearching = true;
                //             });
                //             if (_timer != null) {
                //               _timer!.cancel();
                //             }
                //             print("value+++" + value);
                //             setState(() {
                //               agencyClientList = [];
                //             });
                //
                //             _timer = Timer(Duration(milliseconds: 1000), () {
                //               for (Content cli in wp!.content!) {
                //                 print("value+++" + cli.name!.toString());
                //                 if (cli.name!
                //                     .toLowerCase()
                //                     .contains(value.toLowerCase())) {
                //                   print("value2+++" + cli.name!.toString());
                //                   agencyClientList!.add(cli);
                //                 }
                //               }
                //               setState(() {});
                //             });
                //           }
                //         },
                //         controller: searchController,
                //         decoration: InputDecoration(
                //             labelText: "Search Agency",
                //             hintText: "",
                //             labelStyle: TextStyle(color: lightgreyColor),
                //             border: OutlineInputBorder(
                //                 borderSide: BorderSide(color: lightgreyColor),
                //                 borderRadius:
                //                     BorderRadius.all(Radius.circular(5.0))),
                //             focusedBorder: OutlineInputBorder(
                //                 borderSide: BorderSide(color: lightgreyColor),
                //                 borderRadius:
                //                     BorderRadius.all(Radius.circular(5.0))),
                //             enabledBorder: OutlineInputBorder(
                //                 borderSide: BorderSide(color: lightgreyColor),
                //                 borderRadius:
                //                     BorderRadius.all(Radius.circular(5.0)))),
                //       ),
                //     ),
                //   ),
                //   flex: 0,
                // ),
                Expanded(
                  child: ListView(
                    children: [
                      isSearching
                          ? Column(
                              children: List.generate(
                              agencyClientList!.length,
                              (int index) {
                                Content cli = agencyClientList![index];
                                return Padding(
                                  padding: const EdgeInsets.only(
                                      left: 10.0, right: 10, top: 10),
                                  child: Card(
                                    margin: const EdgeInsets.only(top: 0.0),
                                    color: tileColor,
                                    elevation: 1.0,
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 6.0),
                                      child: ListTile(
                                        title:  Column(
                                          mainAxisAlignment:
                                          MainAxisAlignment.start,
                                          crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: Image.asset(
                                                    "assets/images/agency.png",
                                                    height: 23.0,
                                                    width: 23.0,
                                                  ),
                                                  flex: 0,
                                                ),
                                                SizedBox(
                                                  width: 10.0,
                                                ),
                                                Expanded(
                                                  child: Text(
                                                    cli.name.toString(),
                                                    style: GoogleFonts.viga(
                                                        fontStyle:
                                                        FontStyle.normal,
                                                        fontSize: 15.0,
                                                        color: Colors.black87),
                                                  ),
                                                  flex: 1,
                                                ),
                                                Expanded(
                                                  child: InkWell(
                                                      onTap: () {
                                                        if (cli.isviewAll) {
                                                          cli.isviewAll = false;
                                                        } else {
                                                          cli.isviewAll = true;
                                                        }
                                                        setState(() {});
                                                      },
                                                      child: Image.asset(
                                                        "assets/drawer/down_arrow.png",
                                                        height: 23.0,
                                                        width: 23.0,
                                                      )),
                                                  flex: 0,
                                                ),
                                              ],
                                            ),
                                            SizedBox(
                                              height: 5.0,
                                            ),
                                            cli.isviewAll
                                                ? Column(
                                              children: [
                                                Row(
                                                  crossAxisAlignment:
                                                  CrossAxisAlignment
                                                      .start,
                                                  mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .start,
                                                  children: [
                                                    SizedBox(
                                                      width: 10.0,
                                                    ),
                                                    Expanded(
                                                      child: Column(
                                                        crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                        mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .start,
                                                        children: [
                                                          Row(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            mainAxisAlignment: MainAxisAlignment.start,
                                                            children: [
                                                              Expanded(
                                                                child:
                                                                Column(
                                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                                  children: [
                                                                    Text(
                                                                      "Address",
                                                                      style: GoogleFonts.lato(
                                                                          fontStyle: FontStyle.normal,
                                                                          fontSize: 11.0,
                                                                          color: Colors.black54),
                                                                    ),
                                                                    SizedBox(
                                                                      height:
                                                                      2.0,
                                                                    ),
                                                                    Text(
                                                                      cli.address!.firstLine.toString() +
                                                                          cli.address!.town.toString() +
                                                                          cli.address!.postcode.toString(),
                                                                      style: GoogleFonts.lato(
                                                                          fontStyle: FontStyle.normal,
                                                                          fontSize: 12.0,
                                                                          color: Colors.black87),
                                                                    ),
                                                                    SizedBox(
                                                                      height:
                                                                      5.0,
                                                                    ),
                                                                    Text(
                                                                      "Telephone",
                                                                      style: GoogleFonts.lato(
                                                                          fontStyle: FontStyle.normal,
                                                                          fontSize: 11.0,
                                                                          color: Colors.black54),
                                                                    ),
                                                                    SizedBox(
                                                                      height:
                                                                      2.0,
                                                                    ),
                                                                    Text(

                                                                      cli.telephone!.toString(),
                                                                      textAlign: TextAlign.start,
                                                                      style: GoogleFonts.lato(
                                                                          fontStyle: FontStyle.normal,
                                                                          fontSize: 12.0,
                                                                          color: Colors.black87),
                                                                    ),


                                                                  ],
                                                                ),
                                                                flex: 1,
                                                              ),
                                                              Expanded(
                                                                child:
                                                                Container(width: 1,height: 55,color: Colors.grey,),
                                                                flex: 0,
                                                              ),
                                                              Expanded(
                                                                child:
                                                                Padding(
                                                                  padding: const EdgeInsets.only(left: 10.0),
                                                                  child: Column(
                                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                                    mainAxisAlignment: MainAxisAlignment.start,
                                                                    children: [
                                                                      Text(
                                                                        "Assignment Code ",
                                                                        style: GoogleFonts.lato(
                                                                            fontStyle: FontStyle.normal,
                                                                            fontSize: 11.0,
                                                                            color: Colors.black54),
                                                                      ),
                                                                      SizedBox(
                                                                        height:
                                                                        2.0,
                                                                      ),
                                                                      Text(

                                                                        cli.agencyId.toString(), textAlign: TextAlign.start,
                                                                        style: GoogleFonts.lato(
                                                                            fontStyle: FontStyle.normal,
                                                                            fontSize: 12.0,
                                                                            color: Colors.black87),
                                                                      ),
                                                                      SizedBox(
                                                                        height:
                                                                        5.0,
                                                                      ),
                                                                      Text(
                                                                        "Email",
                                                                        style: GoogleFonts.lato(
                                                                            fontStyle: FontStyle.normal,
                                                                            fontSize: 11.0,
                                                                            color: Colors.black54),
                                                                      ),
                                                                      SizedBox(
                                                                        height:
                                                                        2.0,
                                                                      ),
                                                                      Text(

                                                                        cli.email!.toString(),
                                                                        textAlign: TextAlign.start,
                                                                        style: GoogleFonts.lato(
                                                                            fontStyle: FontStyle.normal,
                                                                            fontSize: 12.0,
                                                                            color: Colors.black87),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                                flex: 1,
                                                              ),
                                                            ],
                                                          ),


                                                        ],
                                                      ),
                                                      flex: 1,
                                                    ),


                                                  ],
                                                ),
                                              ],
                                            )
                                                : SizedBox(),
                                            SizedBox(
                                              height: 10.0,
                                            ),
                                          ],
                                        ),
                                        onTap: () {},
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ))
                          : Column(
                              children: List.generate(
                              wp!.content!.length,
                              (int index) {
                                Content cli = wp!.content![index];
                                return Padding(
                                  padding: const EdgeInsets.only(
                                      left: 10.0, right: 10, top: 10),
                                  child: Card(
                                    margin: const EdgeInsets.only(top: 0.0),
                                    color: accentPrimaryColor,
                                    elevation: 1.0,
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 6.0),
                                      child: ListTile(
                                        title: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: Image.asset(
                                                    "assets/images/agency.png",
                                                    height: 23.0,
                                                    width: 23.0,
                                                  ),
                                                  flex: 0,
                                                ),
                                                SizedBox(
                                                  width: 10.0,
                                                ),
                                                Expanded(
                                                  child: Text(
                                                    cli.name.toString(),
                                                    style: GoogleFonts.viga(
                                                        fontStyle:
                                                            FontStyle.normal,
                                                        fontSize: 15.0,
                                                        color: Colors.black87),
                                                  ),
                                                  flex: 1,
                                                ),
                                                cli.status == "APPROVED" ? Expanded(
                                                  child: Text(
                                                    'ACTIVE',
                                                    style: GoogleFonts.viga(
                                                        fontStyle:
                                                            FontStyle.normal,
                                                        fontSize: 15.0,
                                                        color: Colors.green),
                                                  ),
                                                  flex: 1,
                                                ):
                                                Expanded(
                                                  child: Text(
                                                    'INACTIVE',
                                                    style: GoogleFonts.viga(
                                                        fontStyle:
                                                        FontStyle.normal,
                                                        fontSize: 15.0,
                                                        color: Colors.red),
                                                  ),
                                                  flex: 1,
                                                ),
                                                Expanded(
                                                  child: InkWell(
                                                      onTap: () {
                                                        if (cli.isviewAll) {
                                                          cli.isviewAll = false;
                                                        } else {
                                                          cli.isviewAll = true;
                                                        }
                                                        setState(() {});
                                                      },
                                                      child: Image.asset(
                                                        "assets/drawer/down_arrow.png",
                                                        height: 23.0,
                                                        width: 23.0,
                                                      )),
                                                  flex: 0,
                                                ),
                                              ],
                                            ),
                                            SizedBox(
                                              height: 5.0,
                                            ),
                                            cli.isviewAll
                                                ? Column(
                                                    children: [
                                                      Row(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .start,
                                                        children: [
                                                          SizedBox(
                                                            width: 10.0,
                                                          ),
                                                          Expanded(
                                                            child: Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              mainAxisAlignment:
                                                                  MainAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Row(
                                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                                  children: [
                                                                    Expanded(
                                                                      child:
                                                                          Column(
                                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                                        children: [
                                                                          Text(
                                                                            "Address",
                                                                            style: GoogleFonts.lato(
                                                                                fontStyle: FontStyle.normal,
                                                                                fontSize: 12.0,
                                                                                color: Colors.black54),
                                                                          ),
                                                                          SizedBox(
                                                                            height:
                                                                            2.0,
                                                                          ),
                                                                          Text(
                                                                           cli.address!.firstLine.toString() ,
                                                                            style: GoogleFonts.lato(
                                                                                fontStyle: FontStyle.normal,
                                                                                fontSize: 14.0,
                                                                                color: Colors.black87),
                                                                          ),
                                                                          SizedBox(
                                                                            height:
                                                                            1.0,
                                                                          ),
                                                                          Text(  cli.address!.town.toString() ,
                                                                            style: GoogleFonts.lato(
                                                                                fontStyle: FontStyle.normal,
                                                                                fontSize: 14.0,
                                                                                color: Colors.black87),
                                                                          ),
                                                                          SizedBox(
                                                                            height:
                                                                            1.0,
                                                                          ),
                                                                          Text(

                                                                                cli.address!.postcode.toString(),
                                                                            style: GoogleFonts.lato(
                                                                                fontStyle: FontStyle.normal,
                                                                                fontSize: 14.0,
                                                                                color: Colors.black87),
                                                                          ),
                                                                          SizedBox(
                                                                            height:
                                                                                5.0,
                                                                          ),
                                                                          Text(
                                                                            "Telephone",
                                                                            style: GoogleFonts.lato(
                                                                                fontStyle: FontStyle.normal,
                                                                                fontSize: 12.0,
                                                                                color: Colors.black54),
                                                                          ),
                                                                          SizedBox(
                                                                            height:
                                                                            2.0,
                                                                          ),
                                                                          Text(

                                                                                cli.telephone!.toString(),
                                                                            textAlign: TextAlign.start,
                                                                            style: GoogleFonts.lato(
                                                                                fontStyle: FontStyle.normal,
                                                                                fontSize: 14.0,
                                                                                color: Colors.black87),
                                                                          ),
                                                                          SizedBox(
                                                                            height:
                                                                            5.0,
                                                                          ),
                                                                          Text(
                                                                            "Email",
                                                                            style: GoogleFonts.lato(
                                                                                fontStyle: FontStyle.normal,
                                                                                fontSize: 12.0,
                                                                                color: Colors.black54),
                                                                          ),
                                                                          SizedBox(
                                                                            height:
                                                                            2.0,
                                                                          ),
                                                                          Text(

                                                                            cli.email!.toString(),
                                                                            textAlign: TextAlign.start,
                                                                            style: GoogleFonts.lato(
                                                                                fontStyle: FontStyle.normal,
                                                                                fontSize: 14.0,
                                                                                color: Colors.black87),
                                                                          ),

                                                                        ],
                                                                      ),
                                                                      flex: 1,
                                                                    ),

                                                                  ],
                                                                ),


                                                              ],
                                                            ),
                                                            flex: 1,
                                                          ),


                                                        ],
                                                      ),
                                                    ],
                                                  )
                                                : SizedBox(),
                                            SizedBox(
                                              height: 10.0,
                                            ),
                                          ],
                                        ),
                                        onTap: () {},
                                      ),
                                    ),
                                  ),
                                );
                              },
                            )),
                    ],
                  ),
                  flex: 1,
                ),
              ],
            )
          : SizedBox(),
    );
  }
}
