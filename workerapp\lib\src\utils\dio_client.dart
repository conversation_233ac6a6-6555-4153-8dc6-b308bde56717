import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';


import '../utils/UserPreference.dart';
import 'constants.dart';
import 'exception_handler.dart';

/// Create a singleton class to contain all Dio methods and helper functions
class DioClient {
  DioClient._();

  static final instance = DioClient._();



  final Dio _dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 60),
        receiveTimeout: const Duration(seconds: 60),
        responseType: ResponseType.json,
      )
  );

  final Options options = Options(
      headers: {
        'user-agent': 'dio',
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
  );


  ///Get Method
  Future <dynamic> get(
      String path, {
        data,
        Map<String, dynamic>? queryParameters,
        CancelToken? cancelToken,
        ProgressCallback? onReceiveProgress
      }) async{
    // try{

    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? accessToken=  prefs.getString(UserPreference.accessToken);
    options.headers!.addAll({'Authorization': 'Bearer $accessToken'});

    try{
      final Response response = await _dio.get(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      if (response.data != null) {
        return response.data;
      }
    }catch(e) {
      throw exceptionHandler(e, 'request', StackTrace.current );
    }
    // } catch(e){
    //   rethrow;
    // }
  }

  ///Post Method
  Future<dynamic> post(
      String path, {
        data,
        Map<String, dynamic>? queryParameters,
        CancelToken? cancelToken,
        bool? ignoreAuthToken,
        ProgressCallback? onSendProgress,
        ProgressCallback? onReceiveProgress
      }) async{
    // try{
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? accessToken=  prefs.getString(UserPreference.accessToken);
    if(accessToken!=null && ignoreAuthToken!=true) {
      options.headers!.addAll({'Authorization': 'Bearer $accessToken'});
    }

    try{
      final Response response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      if (response.data != null) {
        return response.data;
      }
    }catch(e) {
      throw exceptionHandler(e, 'request', StackTrace.current);
      ;
    }
  }///Post Method
  Future<dynamic> postRaw(
      String path, {
        data,
        Map<String, dynamic>? queryParameters,
        CancelToken? cancelToken,
        bool? ignoreAuthToken,
        ProgressCallback? onSendProgress,
        ProgressCallback? onReceiveProgress
      }) async{

    try{
      final Response response = await _dio.post(
        path,
        data: data,
      );
      if (response.data != null) {
        return response.data;
      }
    }catch(e) {
      throw exceptionHandler(e, 'request', StackTrace.current);
    }
  }

  ///Put Method
  Future<dynamic> put(
      String path, {
        data,
        Map<String, dynamic>? queryParameters,
        CancelToken? cancelToken,
        ProgressCallback? onSendProgress,
        ProgressCallback? onReceiveProgress
      }) async{
    // try{

    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? accessToken=  prefs.getString(UserPreference.accessToken);
    options.headers!.addAll({'Authorization': 'Bearer $accessToken'});

    try{
      final Response response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        // options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      if(response.statusCode == 200){
        return response.data;
      }
      if (response.data != null) {
        return response.data;
      }
    }catch(e) {
      throw exceptionHandler(e, ' request',  StackTrace.current);
      ;
    }
  }

  ///Delete Method
  Future<dynamic> delete(
      String path, {
        data,
        Map<String, dynamic>? queryParameters,
        CancelToken? cancelToken,
        ProgressCallback? onSendProgress,
        ProgressCallback? onReceiveProgress
      }) async{
    // try{

    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? accessToken=  prefs.getString(UserPreference.accessToken);
    options.headers!.addAll({'Authorization': 'Bearer $accessToken'});

    try{

      final Response response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,

      );
      if (response.data != null) {
        return response.data;
      }
    }catch(e) {
      throw exceptionHandler(e, ' request', StackTrace.current);
      ;
    }
  }

}