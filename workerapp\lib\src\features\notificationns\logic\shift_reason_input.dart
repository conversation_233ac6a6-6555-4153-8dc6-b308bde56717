import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:ndialog/ndialog.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/models/shift/ShiftsPageResp.dart';

import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../../models/shift/Shift.dart';

void shiftReasonInput(
    BuildContext context, var formKey, String title, Shift shift,
    {bool isQuery = false}) {
  showMaterialModalBottomSheet(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(30),
        topRight: Radius.circular(30),
      ),
    ),
    context: context,
    isDismissible: false,
    enableDrag: false,
    useRootNavigator: true,
    builder: (context) => Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: SingleChildScrollView(
        controller: ModalScrollController.of(context),
        child: Consumer(
          builder: (context1, watch, child) {
            final dialog = watch.watch(dialogProvider);
            final shiftRepo = watch.watch(shiftRepoProvider);
            final _profile = watch.watch(loginResponseProvider);

            return RelativeBuilder(builder: (context, height, width, sy, sx) {
              return Container(
                // height: 420,
                width: width,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: const Radius.circular(20.0),
                    topRight: const Radius.circular(20.0),
                  ),
                ),
                child: SingleChildScrollView(
                  child: FormBuilder(
                    key: formKey,
                    child: Column(
                      //  mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: Container(
                              height: 7,
                              width: 70,
                              decoration: BoxDecoration(
                                color: Colors.grey,
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                          ),
                        ),
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.all(15.0),
                            child: Text('$title Reason')
                                .textColor(Colors.black54)
                                .fontSize(sx(27)),
                          ),
                        ),
                        formEntryField(
                          context: context,
                          formName: 'reason',
                          maxLines: 3,
                          unfocus: true,
                          title: 'Reason',
                          // keyboardType: TextInputType.multiline,
                          validator: FormBuilderValidators.compose(
                            [
                              FormBuilderValidators.required(
                                  errorText: ''),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Center(
                            child: Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton(
                                    style: ElevatedButton.styleFrom(
                                      elevation: 8,
                                      // fixedSize: Size(width * 0.9, sy(40)),
                                    ),
                                    onPressed: () async {
                                      if (formKey.currentState!.validate()) {
                                        formKey.currentState?.save();
                                        final _data =
                                            formKey.currentState?.value;

                                        // submit reason
                                        // Cancel a shift
                                        final result =
                                            await ProgressDialog.future(
                                          context,
                                          dismissable: false,
                                          future: isQuery
                                              ? shiftRepo.queryAShift(
                                                  shiftId: shift.id!,
                                                  workerId: _profile!.workerId ??
                                                      1,
                                                  reason: _data['reason'],
                                                )
                                              : shiftRepo.cancelAShift(
                                                  shiftId: shift.id!,
                                                  workerId:
                                                      _profile!.workerId!,
                                                  reason: _data['reason'],
                                                ),
                                          message: Text("$title a shift..")
                                              .textColor(textColor),
                                          title: Text("$title Shift")
                                              .textColor(textColor),
                                          //backgroundColor: Colors.white70,
                                          onProgressError: (err) {
                                            print(err);

                                            Navigator.pop(context);
                                          },
                                          onProgressCancel: () =>
                                              Navigator.pop(context),
                                        );

                                        // check result
                                        if (result is bool) {
                                          Navigator.pop(context);

                                          // added ok
                                          dialog.showFloatingFlushbar(
                                            context: context,
                                            title: '$title Shift',
                                            message:
                                                'Shift has been $title successfully.',
                                          );
                                        }

                                        // err
                                        else {
                                          // added ok
                                          dialog.showFloatingFlushbar(
                                            context: context,
                                            title: '$title Shift',
                                            message: result.message,
                                            warning: true,
                                          );
                                        }
                                      }
                                    },
                                    child: Text(
                                      '$title Shift',
                                      style: TextStyle(
                                        color: Colors.white,
                                        // fontSize: sx(16),
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(width: sx(20)),
                                Expanded(
                                  child: ElevatedButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                    },
                                    child: Text(
                                      'Abort',
                                      style: TextStyle(
                                        color: Colors.white,
                                        // fontSize: sx(16),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            });
          },
        ),
      ),
    ),
  );
}
