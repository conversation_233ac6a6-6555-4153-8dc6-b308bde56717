import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:ndialog/ndialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smart_calendar/controller/smart_calendar_controller.dart'; // Temporarily disabled
// import 'package:smart_calendar/smart_calendar.dart'; // Temporarily disabled
import 'package:styled_widget/styled_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:work_link/src/widgets/ApiCallingWithoutProgressIndicator.dart';
import 'package:work_link/src/widgets/CustomProgressDialog.dart';
import 'package:work_link/src/features/invoices/view_invoice.dart';
import 'package:work_link/src/features/trainings/physical_trainings_list_widget.dart';
import 'package:work_link/src/models/invoice/InvoiceResult/invoice_result.dart';
import 'package:work_link/src/utils/colors.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../utils/UserPreference.dart';
import '../../utils/color_constants.dart';
import '../../utils/constants.dart';
import '../../providers/data_repositories/data_repository.dart';
import '../../utils/dialog_service.dart';
import '../../widgets/appbar_default.dart';
import '../drawer/model/training_model.dart';
import '../payrol/view/view_pay_advice.dart';

class InvoicesHomeScreen extends StatefulWidget {
  @override
  InvoicesHomeScreenState createState() => InvoicesHomeScreenState();
}

class InvoicesHomeScreenState extends State<InvoicesHomeScreen> {

  InvoiceResult? wp;
  SharedPreferences? prefs;
  bool registeredOnline = false;

  init() async {
    prefs = await SharedPreferences.getInstance();
    String? workerId = await prefs!.getString(UserPreference.WORKER_ID);
    //getBookedDates(workerId);

    registeredOnline =  prefs!.getString(UserPreference.hascoId)!=null?true:false;

    Future.delayed(Duration.zero, () {
      getTrainingData(workerId);
    });
  }

  getTrainingData(workerId) async {
    try {
      CustomProgressLoader.showLoader(context);
      print("log+++"+"$dataService/api/v1/worker-training/"+workerId+"/0/300");
      Response? response = await ApiCalling()

          .apiCall(context, "$dataService/api/v1/invoice/worker/${workerId}/{page}/{size}?page=0&size=200", "get");
      CustomProgressLoader.cancelLoader(context);
      print("response++++++" + response.toString());
      if (response != null) {
        if (response.statusCode == 200) {
          wp = InvoiceResult.fromJson(response.data);
          print("wii");
          setState(() {

          });
        }
      }
    } catch (e) {

      print("issue shubh" + e.toString());
      return null;
    }
  }


  @override
  void initState() {
    // TODO: implement initState
    init();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {

    final monthLater =  DateTime.now().add(Duration(days: 30));
    final today =  DateTime.now();



    return Scaffold(
      appBar: AppBarDefault(context, "Invoices",
          leading: Container(
            height: 5,
            width: 30,
            child: Padding(
              padding: const EdgeInsets.only(left: 15.0, right: 15),
              child: InkWell(
                child: Icon(
                  Icons.arrow_back,
                  color: welcomeTextColor,
                  size: 20,
                ),
                onTap: () {
                  routeBack(context);
                },
              ),
            ),
          )),
      body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(defaultPadding),
            child: SingleChildScrollView( child:Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding:  const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: welcomeTextColor,
                    borderRadius: BorderRadius.only(
                      topRight:  Radius.circular(defaultBorderRadius),
                      topLeft:  Radius.circular(defaultBorderRadius),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          "REF",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 13,
                              fontWeight: FontWeight.bold),
                        ),
                        flex: 1,
                      ),
                      Expanded(
                        child: Text(
                          "DATE",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 13,
                              fontWeight: FontWeight.bold),
                        ),
                        flex: 2,
                      ),
                      Expanded(
                        child: Text(
                          "STATUS",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 13,
                              fontWeight: FontWeight.bold),
                        ),
                        flex: 2,
                      ),
                      Expanded(
                        child: Text(
                          "TOTAL (£)",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 13,
                              fontWeight: FontWeight.bold),
                        ),
                        flex: 1,
                      ),
                      Expanded(
                        child: Text(
                          "ACTION",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 13,
                              fontWeight: FontWeight.bold),
                        ),
                        flex: 1,
                      ),

                    ],
                  ),
                ),

                if(wp!=null&&wp!.content!=null&&wp!.content!.length>0)
                  Column(
                      children: List.generate(wp!.content!.length, (int index) {
                        return Container(
                          color: index%2==0?Color(0xffE6E6E6):Colors.white,
                          child: Padding(
                            padding:
                            const EdgeInsets.all( 5),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(wp!.content![index]!.id.toString(),
                                      // textAlign: TextAlign.center,
                                      style: TextStyle(
                                          color: Colors.black,
                                          fontSize: 13,
                                          fontWeight: FontWeight.normal)),

                                  flex: 1,
                                ),Expanded(
                                  child:  Text(wp!.content![index]!.invoiceDate!,
                                      style: TextStyle(
                                          color: Colors.black,
                                          fontSize: 13,
                                          fontWeight: FontWeight.normal)),

                                  flex: 2,
                                ),
                                Expanded(
                                  child:  Row(
                                      children: [
                                        SizedBox(width: 9,),
                                        Text(wp!.content![index]!.invoiceStatus??"",
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                                color: Colors.black,
                                                fontSize: 13,
                                                fontWeight: FontWeight.normal)),

                                      ]),

                                  flex: 2,
                                ),
                                Expanded(
                                  child:  Row(
                                      children: [
                                        SizedBox(width: 9,),
                                        Text(wp!.content![index]!.invoiceItemResult?[0].total?.toStringAsFixed(2)??"0",
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                                color: Colors.black,
                                                fontSize: 13,
                                                fontWeight: FontWeight.normal)),

                                      ]),

                                  flex: 1,
                                ),

                                Expanded(
                                    flex: 1,
                                    child:Row(
                                      children: [
                                        InkWell(
                                            onTap: () async {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(builder: (context) =>  ViewInvoice(wp!.content![index]!.id!)),
                                              );


                                            },
                                            child: Icon(
                                              Icons.remove_red_eye,
                                              color: welcomeTextColor,
                                            )),
                                        SizedBox(width: 0,),
                                      ],
                                    )

                                ),


                              ],
                            ),
                          ),
                        );
                      })),


              ],
            ),
            ),
          )),
    );
  }
}
