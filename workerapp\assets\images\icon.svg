<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 68.06 68.06">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
      }

      .cls-2 {
        fill: #ff008c;
      }

      .cls-3 {
        fill: #04459c;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <rect class="cls-1" width="68.06" height="68.06"/>
    <g>
      <path class="cls-2" d="m58.12,34.03c0,9.64-7.82,17.46-17.46,17.46-2.36,0-4.62-.46-6.66-1.32-2.68-1.1-5.05-2.88-6.88-5.08-2.44-3.01-3.92-6.85-3.92-11.07,0-3.35.93-6.46,2.54-9.11.42-.07.89-.12,1.4-.13h.21c1.89,0,3.71.56,5.27,1.62-1.88,1.98-3.04,4.67-3.04,7.62,0,3.63,1.75,6.85,4.43,8.86,1.85,1.4,4.14,2.2,6.63,2.2h.24c6.02-.13,10.82-5.02,10.82-11.07,0-5.18-3.54-9.52-8.34-10.73-.36-.53-.71-1.01-1.03-1.44l-.03-.04-.03-.03c-1.64-1.95-3.62-3.56-5.79-4.73,1.33-.33,2.72-.49,4.15-.49,9.64,0,17.46,7.82,17.46,17.46Z"/>
      <path class="cls-3" d="m48.21,34.03c0,3.01-1.77,5.61-4.32,6.79-.97.51-2.07.75-3.22.75-1.53,0-2.98-.46-4.16-1.26-2.04-1.34-3.38-3.65-3.38-6.28s1.34-4.94,3.38-6.28c-.3-.46-.64-.86-1.02-1.26-.43-.48-.91-.91-1.45-1.32-1.85-1.37-4.16-2.2-6.66-2.2h-.24c-.91.03-1.8.13-2.63.38-4.7,1.26-8.19,5.56-8.19,10.69s3.49,9.43,8.19,10.69c.35.54.75,1.07,1.15,1.58,1.61,1.93,3.57,3.54,5.72,4.7-1.29.32-2.63.48-4,.48-9.64,0-17.43-7.82-17.43-17.46s7.79-17.46,17.43-17.46c1.32,0,2.6.13,3.84.43.97.21,1.91.51,2.79.89,2.71,1.1,5.05,2.87,6.9,5.08.51.67.99,1.34,1.42,2.07.38.62.7,1.29.99,1.96.19.05.38.13.56.24,2.55,1.18,4.32,3.79,4.32,6.79Z"/>
    </g>
  </g>
</svg>