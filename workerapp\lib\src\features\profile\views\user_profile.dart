
import 'dart:developer';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:image_cropper/image_cropper.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ndialog/ndialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/widgets/edit_text.dart';
import 'package:work_link/src/utils/styles.dart';
import 'package:work_link/src/features/profile/model/WorkerModel.dart';

import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';

import '../../../widgets/ApiCallingWithoutProgressIndicator.dart';
import '../../../widgets/CustomProgressDialog.dart';
import '../../../utils/UserPreference.dart';
import '../../../utils/constants.dart';


import '../../../utils/dialog_service.dart';
import 'avatar_hero.dart';

enum AppState {
  free,
  picked,
  cropped,
}

class UserProfile extends StatefulWidget {
  @override
  userProfileState createState() => userProfileState();

}

class userProfileState extends State<UserProfile> {
  AppState? state; 


  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _serNamePasswordController =
      TextEditingController();
  final TextEditingController _genderController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _postcodeController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  File?  userImage;
  String?  userImageName;
  String?  userImagePath;



  final _formKey = GlobalKey<FormState>();
   WorkerModel? wp;


  _getFromGallery(int type) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(type: FileType.image);
    userImage= File(result!.files.single.path!);
    userImageName = result.files.single.name;
    userImagePath = result.files.single.path.toString();

  }

  getProfileData(workerId) async {
    try {
      CustomProgressLoader.showLoader(context);
      Response? response = await ApiCalling()
          .apiCall(context, "$dataService/api/v1/worker/"+workerId, "get");

      CustomProgressLoader.cancelLoader(context);
      print("response++" + response.toString());
      if (response != null) {
        if (response.statusCode == 200) {
          wp = WorkerModel.fromJson(response.data);
          setState(() {
            _firstNameController.text = wp!.firstname!.toString();
            _lastNameController.text = wp!.lastname!.toString();
            _serNamePasswordController.text = wp!.lastname!.toString();
            _addressController.text = wp!.address!.toString();
            _phoneController.text = wp!.phoneNumber!.toString();
            _postcodeController.text = wp!.postcode!.toString();
            print("email++++"+wp!.email!.toString());
            _emailController.text = wp!.email!.toString();
            _genderController.text = wp!.gender!.toString();
          });
        }
      }
    } catch (e) {

      print("issue shubh" + e.toString());
      return null;
    }
  }


  Future<XFile> _cropImage(XFile imageFile) async {
    CroppedFile? croppedFile = await ImageCropper().cropImage(
        sourcePath: imageFile.path,
        uiSettings: [
          AndroidUiSettings(
              toolbarTitle: 'Crop Image',
              lockAspectRatio: true,
              toolbarColor: welcomeTextColor,
              activeControlsWidgetColor: welcomeTextColor,
              toolbarWidgetColor: Colors.white,
              initAspectRatio: CropAspectRatioPreset.square,),
          IOSUiSettings(
            title: 'Crop Image',
          ),
        ]);

      return XFile(croppedFile?.path??"");

  }

  getProfileUpdate() async {
    try {
      CustomProgressLoader.showLoader(context);

      Map map =  {
        "address": _addressController.text,
        "agencyId": agencyId,
        "assignmentCodeId": wp!.assignmentCodeId,
        "cv": "",
        "dob": "",
        "firstname": wp!.firstname!.toString(),
        "gender": wp!.gender!.toString(),
        "id": wp!.id,
        "lastname": wp!.lastname!.toString(),
        "nationality": wp!.nationality!.toString(),
        "phoneNumber": _phoneController.text,
        "postcode": _postcodeController.text,
        "profilePic": "",
        "status":wp!.status!.toString(),
        "username": wp!.username!.toString()
      };
print("map+++"+map.toString());
      var response = await ApiCalling().apiCallPutWithMapData(
          context, "$dataService/api/v1/worker", map);
      print("response+++"+response.toString());
      CustomProgressLoader.cancelLoader(context);
      if (response != null) {
        if (response.statusCode == 200) {
          getProfileData(prefs!.getString(UserPreference.WORKER_ID));
          setState(() {
            isEditable=false;
          });
        }
      }
    } catch (e) {
      CustomProgressLoader.cancelLoader(context);
      print("issue shubh" + e.toString());
      return null;
    }
  }
  SharedPreferences? prefs;
  String? agencyId;
  init() async {
     prefs = await SharedPreferences.getInstance();
   String? workerId= prefs!.getString(UserPreference.WORKER_ID);
   agencyId= prefs!.getString(UserPreference.agentId);
   await getProfileData(workerId);
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    init();
    state = AppState.free;
  }


  var _imageUrl;
  get imageUrl => _imageUrl;

  File? image;
  bool isEditable =false;


  XFile? imageFile;

//   _openGallery() async {
//     imageFile = await ImagePicker().pickImage(source: ImageSource.gallery);
//     var res = await uploadImage(imageFile!);
// k
//     setState(() {});
//   }


  // void addImageByGallery() async {
  //
  //     // var file = await _openGallery();
  //     // var res = await uploadImage(imageFile!);
  //     // setState(() {
  //     //   // state = res;
  //     //   print(res);
  //     // });
  // }



  Widget build(BuildContext context) {

    Future<XFile?> compressFile(File file) async {
      final filePath = file.absolute.path;

      // Create output file path
      // eg:- "Volume/VM/abcd_out.jpeg"
      final lastIndex = filePath.lastIndexOf(new RegExp(r'.jp'));
      final splitted = filePath.substring(0, (lastIndex));
      final outPath = "${splitted}_out${filePath.substring(lastIndex)}";
      var result = await FlutterImageCompress.compressAndGetFile(
        file.absolute.path, outPath,
        quality: 70,
      );

      print(file.lengthSync());
      // print(result.lengthSync());

      return result;
    }

    MediaQueryData mediaQuery = MediaQuery.of(context);
    return Form(
      key: _formKey,
      child: Scaffold(
        appBar: AppBarDefault(
          context,"My Details",
          leading:Container(
            height: 5,
            width: 30,
            child: Padding(
              padding: const EdgeInsets.only(left: 15.0, right: 15),
              child: InkWell(
                child: Icon(
                  Icons.arrow_back,
                  color: welcomeTextColor,
                  size: 20,
                ),
                onTap: () {
                  routeBack(context);
                },
              ),
            ),
          ),
          actions:
            [isEditable
                ?  SizedBox()
                :InkWell(
              child: Container(
                child: Padding(
                  padding: const EdgeInsets.only(right:10.0),
                  child: Center(
                      child: Text("Edit", style: TextStyle(color: welcomeTextColor))
                  ),
                ),
            ),
              onTap: (){

              setState(() {

                if(isEditable){
                  isEditable=false;
                }else{
                  isEditable=true;
                }
              });

            },),]
        ),
        body: Container(
          height: double.infinity,
          width: double.infinity,

          /// Set Background image in splash screen layout (Click to open code)
          decoration: const BoxDecoration(
            color: Colors.white,
          ),
          child: ListView(
            children: <Widget>[
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Consumer(
                      builder: (context1, watch, child) {


                        return Expanded(child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 15),
                              child: Center(child:

                              InkWell(
                                  onTap: () async {
                                    if(wp==null){
                                      DialogService().showFloatingFlushbar(
                                        context: context,
                                        title: 'Profile Image',
                                        message: "Please wait.",
                                        warning: false,
                                      );
                                      return null;
                                    }
                                    var res;
                                    XFile? xfile = await ImagePicker().pickImage(source: ImageSource.gallery);

                                    if(xfile!=null)
                                      xfile = await _cropImage(xfile);
                                    else return;
                                    
                                    File file = File(xfile!.path);


                                    if(wp!=null && file!=null){
                                      // res = await upload(file!, wp!.id!);
                                      final fileBytes = await file.readAsBytes();
                                      var len = fileBytes.lengthInBytes/1000000;

                                      if(len>=1) {

                                        while(len>=1) {
                                          xfile = await compressFile(file);
                                          file = File(xfile!.path);
                                          final fileBytes = await file.readAsBytes();
                                          len = fileBytes.lengthInBytes/1000000;
                                        }
                                      }

                                      // print(file?.length());

                                      XFile finalfile = new XFile(file.path);
                                      final result = await ProgressDialog.future(
                                        context,
                                        dismissable: false,
                                        future:watch.watch(dataRepoProvider).upload(finalfile!, wp!.id!),
                                        message: Text("Uploading profile image")
                                            .textColor(textColor),
                                        title: Text("Profile Update").textColor(textColor),
                                        //backgroundColor: Colors.white70,
                                        onProgressError: (err) {
                                          print(err);

                                          Navigator.pop(context);
                                        },
                                        onProgressCancel: () => Navigator.pop(context),
                                      );

                                      if (result is bool) {
                                        DialogService().showFloatingFlushbar(
                                          context: context,
                                          title: 'Upload Profile',
                                          message: "Successfully uploaded",
                                        );
                                      }else{
                                        DialogService().showFloatingFlushbar(
                                          context: context,
                                          title: 'Upload Profile',
                                          message: "An error occured, try again later.",
                                          warning: true,
                                        );
                                      }

                                      await init();
                                      setState(() {
                                      });

                                    };



                                    setState(() {
                                      // state = res;
                                      print(res);
                                    });
                                  },
                                  child:
                                  wp?.profilePic!=null&&wp!.profilePic!="null"&&wp!.profilePic!="" ?
                                  CircleAvatar(
                                      radius: 75,
                                      backgroundImage:NetworkImage(wp!.profilePic!)):
                                  Container(
                                    height: 150,
                                    width: 150,
                                    child: Image.asset('assets/images/uploadpic.png') ,
                                  )
                              )

                              ),


                            ),




                            isEditable?Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // Icon(Icons.edit,size: 25,color: welcomeTextColor,),
                                  // Padding(
                                  //   padding: const EdgeInsets.only(left:10.0),
                                  //   child: Icon(Icons.delete,size: 25,color: welcomeTextColor,),
                                  // ),
                                ],),
                            ):SizedBox()

                          ],
                        ),flex:2);
                      }
                  ),


                Expanded(child:Column(children: [
                  Padding(
                    padding:
                    const EdgeInsets.only(left: 15.0, right: 15.0, top: 20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        EditText(
                          hint: 'First Name',
                          textEditingController: _firstNameController,
                          keyboardType: TextInputType.text,
                          validationMessage: "Please enter first name",
                          maxLength: 30,
                          color: Colors.transparent,textColor: Colors.black,
                          borderColor:Colors.transparent,
                          hgt: 60.0,
                          isHide: false,
                          isValidation: true,
                          isReadOnly: true,
                        ),

                    /*    EditText(
                          hint: 'Last Name',
                          textEditingController: _lastNameController,
                          keyboardType: TextInputType.text,
                          validationMessage: "Please enter last name",
                          maxLength: 30,
                          color: Colors.transparent,textColor: Colors.black,
                          borderColor:Colors.transparent,
                          hgt: 60.0,
                          isHide: false,
                          isValidation: true,
                          isReadOnly: true,
                        ),*/

                        EditText(
                          hint: 'Surname',
                          textEditingController: _serNamePasswordController,
                          keyboardType: TextInputType.text,
                          validationMessage: "",
                          maxLength: 30,
                          color: Colors.transparent,textColor: Colors.black,
                          borderColor:Colors.transparent,
                          hgt: 60.0,
                          isHide: false,
                          isValidation: false,
                          isReadOnly: true,
                        ),

                        EditText(
                          hint: 'Gender',
                          textEditingController: _genderController,
                          keyboardType: TextInputType.text,
                          validationMessage: "",
                          maxLength: 30,
                          color: Colors.transparent,textColor: Colors.black,
                          borderColor:Colors.transparent,
                          hgt: 60.0,
                          isHide: false,
                          isValidation: false,
                          isReadOnly: true,
                        ),


                      ],
                    ),
                  ),
                ],) ,flex:3),
              ],),


              Padding(
                padding: const EdgeInsets.only(top:20.0),
                child: Container(
                  color: background,
                  child: Padding(
                    padding:
                        const EdgeInsets.only(left: 0.0, right: 0.0, top: 10.0,bottom: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[

                        Padding(
                          padding: const EdgeInsets.only(left: 10.0,bottom: 10),
                          child: Text(
                            'Contact Detail',
                            style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontSize: 18,
                                letterSpacing: 1.5),
                          ),
                        ),
                        EditText(
                          hint: 'Address',
                          textEditingController: _addressController,
                          keyboardType: TextInputType.text,
                          validationMessage: "Please enter address",
                          maxLength: 30,
                          color: Colors.white,
                          textColor:  isEditable?welcomeTextColor:Colors.black,
                          borderColor:isEditable? Colors.black:Colors.transparent,
                          hgt: 60.0,
                          isHide: false,
                          isValidation: true,
                          isReadOnly: isEditable?  false:true,
                        ),
                        SizedBox(
                          height: 15.0,
                        ),
                        EditText(
                          hint: 'Postcode',
                          textEditingController: _postcodeController,
                          keyboardType: TextInputType.text,
                          validationMessage: "Please enter postcode",
                          maxLength: 30,
                          color: Colors.white,textColor:  isEditable?welcomeTextColor:Colors.black,
                          borderColor:isEditable? Colors.black:Colors.transparent,
                          hgt: 60.0,
                          isHide: false,
                          isValidation: true,
                          isReadOnly:isEditable?  false:true,
                        ),
                        SizedBox(
                          height: 15.0,
                        ),
                        EditText(
                          hint: 'Phone',
                          textEditingController: _phoneController,
                          keyboardType: TextInputType.text,
                          validationMessage: "Please enter phone number",
                          maxLength: 30,
                          color: Colors.white,textColor:  isEditable?welcomeTextColor:Colors.black,
                          borderColor:isEditable? Colors.black:Colors.transparent,
                          hgt: 60.0,
                          isHide: false,
                          isValidation: true,
                          isReadOnly:isEditable?  false:true,
                        ),
                        SizedBox(
                          height: 15.0,
                        ),

                        EditText(
                          hint: 'Email',
                          textEditingController: _emailController,
                          keyboardType: TextInputType.text,textColor: Colors.black,
                          validationMessage: "Please enter email",
                          maxLength: 30,
                          color: Colors.white,
                          borderColor:isEditable? Colors.black:Colors.transparent,
                          hgt: 60.0,
                          isHide: false,
                          isReadOnly: true,
                          isValidation: true,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              isEditable? Padding(
                padding:
                    const EdgeInsets.only(left: 10.0, right: 10.0, top: 40.0),
                child: GestureDetector(
                  onTap: () {
                    print("click++++");
                    final formState = _formKey.currentState;
                    if (formState!.validate()) {
                      formState.save();
                      getProfileUpdate();
                    }


                  },
                  child: Padding(
                    padding: const EdgeInsets.only(left: 70.0, right: 70.0),
                    child: TextButton(
                      style: Styles.flatButtonStyle,
                      onPressed: () {
                        print("click++++");
                        final formState = _formKey.currentState;
                        if (formState!.validate()) {
                          formState.save();
                          getProfileUpdate();
                        }
                      },
                      child: const Text(
                        'Save',
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 18,
                            letterSpacing: 1.5),
                      ),
                    ),
                  ),
                ),
              ):SizedBox(),
            ],
          ),
        ),
      ),
    );
  }

  void _submit() {
    final isValid = _formKey.currentState!.validate();
    if (!isValid) {
      return;
    } else {
      getProfileUpdate();
    }
    _formKey.currentState!.save();
  }
}



