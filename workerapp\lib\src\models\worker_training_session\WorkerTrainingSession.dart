
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:work_link/src/models/profile2/WorkerProfile2.dart';
import 'package:work_link/src/models/training-session/training_session.dart';

import '../agency/Agency.dart';
import '../profile/worker_profile.dart';
import '../training/Training.dart';

part 'WorkerTrainingSession.freezed.dart';
part 'WorkerTrainingSession.g.dart';

@freezed
abstract class WorkerTrainingSession with _$WorkerTrainingSession {
  factory WorkerTrainingSession({

    int? id,
    WorkerProfile2? worker,
    Training? training,
    Agency? agency,
    TrainingSession? trainingSession,
    bool? isAdminBilled,
    bool? feedbackSkipped,
    String? trainingStatus,
    bool? skippedTraining,
    bool? isAgencyBilled,
    bool? showCertificate,
    int? trainingScore,
    String? trainingExpiryDate,
    String? dateUploaded,
    bool? passedTraining,


  }) = _WorkerTrainingSession;

  factory WorkerTrainingSession.fromJson(Map<String, dynamic> json) =>
      _$WorkerTrainingSessionFromJson(json);
}
