import 'package:flutter/material.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:work_link/src/features/shift-filter/views/shifts_home_view.dart';
import 'package:work_link/src/features/shifts/data/status_shifts.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import 'package:work_link/src/widgets/index.dart';

import 'notification_row_view.dart';

class NotificationsHomeView extends StatelessWidget {
  const NotificationsHomeView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBarDefault(context,"Notifications",
        leading: Container(
          height: 30,
          width: 30,
          child: Padding(
            padding: const EdgeInsets.only(left: 15.0, right: 15),
            child: InkWell(
              child: Icon(   Icons.arrow_back,
                color: welcomeTextColor,
                size: 20,
              ),
              onTap: () {
                routeBack(context);
              },
            ),
          ),
        )
      ),

      // floatingActionButton: FloatingActionButton(
      //   tooltip: 'filter shifts',
      //   child: Icon(Icons.filter_alt),
      //   onPressed: () {
      //     routeTo(context, ShiftsHomeFilterView());
      //   },
      // ),
      body: RelativeBuilder(builder: (context, height, width, sy, sx) {
        return DefaultTabController(
          length: 1,
          child: Scaffold(
            body: NotificationRowView( ),
          ),
        );
      }),
    );
  }
}
