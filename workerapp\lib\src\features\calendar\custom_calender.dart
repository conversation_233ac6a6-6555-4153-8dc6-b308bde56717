import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smart_calendar/controller/smart_calendar_controller.dart'; // Temporarily disabled
// import 'package:smart_calendar/smart_calendar.dart'; // Temporarily disabled
import 'package:work_link/src/widgets/ApiCallingWithoutProgressIndicator.dart';
import 'package:work_link/src/widgets/CustomProgressDialog.dart';
import 'package:work_link/src/utils/colors.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/appbar_default.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../utils/UserPreference.dart';
import '../../utils/constants.dart';
import 'components/calendart_widget.dart';
import 'modal/calendar_model.dart';

final today = DateUtils.dateOnly(DateTime.now());

class Calendr extends StatefulWidget {
  @override
  CalendrState createState() => CalendrState();
}

class CalendrState extends State<Calendr> {
  List<DateTime> bookedDates=[];
  List<DateTime> unavailableDates=[];
  // SmartCalendarController? controller; // Temporarily disabled
  CalendarModel? wp;
  SharedPreferences? prefs;
  init() async {
    prefs = await SharedPreferences.getInstance();
    String? workerId = await prefs!.getString(UserPreference.WORKER_ID);
    //getBookedDates(workerId);
    Future.delayed(Duration.zero, () {
      getBookedDates(workerId);
      getUnavailableDates(workerId);
    });
  }
  getBookedDates(workerId) async {
    try {
      CustomProgressLoader.showLoader(context);
      Response? response = await ApiCalling()
          .apiCall(context, "$dataService/api/v1/worker-agency-shift-status/"+workerId+"/0/300/booked", "get");
      CustomProgressLoader.cancelLoader(context);
      // print("response++++++" + response.toString());
      if (response != null) {
        if (response.statusCode == 200) {
          if(response.data!=null){
            //response.data[0]["shiftDate"]
            for(var model in response.data){
              String date =model["shiftDate"];

              String year,month,day;
              if (date.contains("-")) {
                List<String> dateArray = date!.split("-");
                try {
                  year = dateArray[2];
                  month = dateArray[1];
                  day = dateArray[0];
                  if (year.length == 2) {
                    year = "20" + year;
                  }
                  date = year + "-" + month + "-" + day;
                } catch (e) {
                  print("errr0r____" + e.toString());
                }
              }
              else  if (date.contains("/")) {
                List<String> dateArray = date!.split("/");
                try {
                  year = dateArray[2];
                  month = dateArray[1];
                  day = dateArray[0];
                  if (year.length == 2) {
                    year = "20" + year;
                  }
                  date = year + "-" + month + "-" + day;

                } catch (e) {
                  print("errr0r____" + e.toString());
                }
              }


              bookedDates.add(DateTime.parse(date),
              );
            }

            setState(() {});
          }
        }
      }
    } catch (e) {

      print("issue shubh" + e.toString());
      return null;
    }
  }
  getUnavailableDates(workerId) async {
    try {
      // CustomProgressLoader.showLoader(context);
      Response? response = await ApiCalling()
          .apiCall(context, "$dataService/api/v1/availability/"+workerId+"/0/300", "get");
      // CustomProgressLoader.cancelLoader(context);
      if (response != null) {
        if (response.statusCode == 200) {
          if(response.data!=null){
            var dates = response.data;
            for(var model in dates["content"]){
              String date =model["date"];

              unavailableDates.add(DateTime.parse(date),
              );
            }

            setState(() {});
          }
        }
      }
    } catch (e) {

      print("issue shubh" + e.toString());
      return null;
    }
  }

  @override
  void initState() {
    // TODO: implement initState

    super.initState();
    init();
  }


  @override
  Widget build(BuildContext context) {


    return Scaffold(
        appBar: AppBarDefault(context,"Availability" ,leading:
        Container(
          height: 30,
          width: 30,
          child: Padding(
            padding: const EdgeInsets.only(left: 15.0, right: 15),
            child: InkWell(
              child: Icon(   Icons.arrow_back,
                color: welcomeTextColor,
                size: 20,
              ),
              onTap: () {
                routeBack(context);
              },
            ),
          ),
        )),
        body: SafeArea(
            child: Container(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Padding(
                  //     padding: const EdgeInsets.only(right: 13,top: 20),
                  //     child: Row(children: [
                  //       Padding(
                  //           padding: const EdgeInsets.only(left: 13.0,right: 15),
                  //           child:Image.asset(
                  //             "assets/drawer/availibality.png",
                  //             height: 23.0,
                  //             width: 23.0,
                  //           )), Text('Availability',style: TextStyle(color: Colors.black,fontSize: 20,fontWeight: FontWeight.bold),
                  //       )
                  //     ],)),
                  // Padding(
                  //     padding: const EdgeInsets.only(left: 13.0,right: 13,top: 8),
                  //     child: Container(height: 1.0,color: Colors.black,)),

                  Padding(
                      padding: const EdgeInsets.all(20),
                      child: Text('Set your availability on the calendar',style: TextStyle(color: Colors.black,fontSize: 14,fontWeight: FontWeight.normal),
                      )),

                  Padding(
                      padding: const EdgeInsets.only(left: 15.0,right: 30),
                      child:  Container(
                          decoration: BoxDecoration(
                            color: Color(0xffE6E6E6),
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                          ),

                          child:Padding(
                            padding: const EdgeInsets.only(left: 10.0,right: 20,top: 10,bottom: 10),
                            child: Container(

                              child:
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                Container(height: 25,width: 25,color: Color(0xffffffff)),
                                Padding(
                                  padding: const EdgeInsets.only(left:10.0,right: 10),
                                  child: Text("Available",style: TextStyle(fontSize: 14),),
                                ),
                                Container(height: 25,width: 25,color: Color(0xffFF5858),),
                                Padding(
                                    padding: const EdgeInsets.only(left:10.0,right: 10),
                                    child:Text("Unavailable",style: TextStyle(fontSize: 14),)),
                                Container(height: 25,width: 25,color: Colors.blue,),
                                Padding(
                                    padding: const EdgeInsets.only(left:10.0,right: 10),
                                    child:Text("Booked",style: TextStyle(fontSize: 14),)),



                              ],),

                            ),
                          ))),

                  Padding(
                    padding: const EdgeInsets.only(left: 20.0,right: 20,top: 10,bottom: 10),
                    child: Container(

                      child:
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(Icons.info,color:Color(0xff436EF9) ,),
                          SizedBox(width: 10,),
                          Expanded(child:   Padding(
                              padding: const EdgeInsets.only(top:5),
                              child:Text("Tap on a date to change availability status or view details",
                                style: TextStyle(fontSize: 14,color: Color(0xff436EF9)),))
                            ,flex: 1,),
                        ],),

                    ),
                  ),
                  Center(
                    child: Padding(
                      padding: EdgeInsets.all( 20),
                      child: CalendarWidget(unavailableDates: unavailableDates, bookedDates: bookedDates,),
                    )
                  ),
                ],
              ),
            )),
      );
  }







}