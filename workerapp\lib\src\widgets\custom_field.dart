import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:styled_widget/styled_widget.dart';

import 'package:relative_scale/relative_scale.dart';
import 'package:work_link/src/utils/index.dart';

Widget formEntryField({
  required title,
  required BuildContext context,
  required String? formName,
  TextEditingController? controller,
  String validateError = '',
  FormFieldValidator<String>? validator,
  String hintText = '',
  String? initialText,
  bool autoFocus = false,
  int maxLines = 1,
  List<String>? autoFillHints,
  bool obscureText = false,
  bool isPhoneField = false,
  bool readOnly = false,
  bool enforceLength = false,
  bool unfocus = false,
  TextInputType? keyboardType = TextInputType.text,
  Color? titleColor,
  Function? customOnChangeCallback,

  /// determine if this is a custom phone input field or general textfield. Text by default
  String labelText = '',
  Widget suffixIcon = const SizedBox.shrink(),
}) {
  return RelativeBuilder(
    builder: (context, height, width, sy, sx) {
      return Padding(
        padding: const EdgeInsets.all(5.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            /*    Text(title ?? '')
                .textColor(titleColor ?? Colors.black54)
                .fontSize(sx(25)),*/
            customTextField(
              formName: formName,
              context: context,
              autoFillHints: autoFillHints,
              controller: controller,
              hintText: title,
              maxLines: maxLines,
              autoFocus: autoFocus,
              obscureText: obscureText,
              readOnly: readOnly,
              errorString: validateError,
              validator: validator,
              labelText: labelText,
              enforceLength: enforceLength,
              unfocus: unfocus,
              suffixIcon: suffixIcon,
              keyboardType: keyboardType,
              initialText: initialText,
              customOnChangeCallback: customOnChangeCallback,
            ),
          ],
        ),
      );
    },
  );
}

Widget formDDEntryField({
  String? title,
  required BuildContext context,
  required String? formName,
  TextEditingController? controller,
  String validateError = '',
  required List<DropdownMenuItem<Object?>> items,
  FormFieldValidator<Object>? validator,
  String hintText = '',
  String? initialText,
  bool autoFocus = false,
  int maxLines = 1,
  bool obscureText = false,
  bool isPhoneField = false,
  bool readOnly = false,
  bool enforceLength = false,
  bool unfocus = true,
  Widget hint = const Text(
    '-- select --',
    style: TextStyle(fontStyle: FontStyle.italic, color: Colors.grey),
  ),
  TextInputType? keyboardType = TextInputType.text,
  Color? titleColor,

  /// determine if this is a custom phone input field or general textfield. Text by default
  String labelText = '',
  Widget suffixIcon = const SizedBox.shrink(),
}) {
  return RelativeBuilder(
    builder: (context, height, width, sy, sx) {
      return Padding(
        padding: const EdgeInsets.all(5.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(title ?? '')
                .textColor(titleColor ?? Colors.black54)
                .fontSize(sx(25)),
            customDropDownField(
              formName: formName,
              context: context,
              controller: controller,
              hintText: hintText,
              maxLines: maxLines,
              autoFocus: autoFocus,
              obscureText: obscureText,
              readOnly: readOnly,
              errorString: validateError,
              validator: validator,
              labelText: labelText,
              enforceLength: enforceLength,
              unfocus: unfocus,
              suffixIcon: suffixIcon,
              hint: hint,
              keyboardType: keyboardType,
              initialText: initialText,
              items: items,
            ),
          ],
        ),
      );
    },
  );
}

Widget customTextField({
  TextEditingController? controller,
  BuildContext? context,
  TextInputType? keyboardType,
  String? initialText,
  String hintText = '',
  String? formName,
  int maxLines = 1,
  List<String>? autoFillHints,
  bool readOnly = false,
  bool obscureText = false,
  bool unfocus = false,
  bool enforceLength = false,
  String labelText = '',
  bool autoFocus = false,
  var customOnChangeCallback,
  FormFieldValidator<String>? validator,
  String errorString = 'this field is required',
  Widget suffixIcon = const SizedBox.shrink(),
}) {
  const double _radius = 8.0;

  return Padding(
    padding: EdgeInsets.all(12),
    child: Container(

      decoration: BoxDecoration(
        color: welcomeTextColor,
        borderRadius: BorderRadius.circular(_radius),
      ),
      child: FormBuilderTextField(
          name: formName!,

          readOnly: readOnly,
          initialValue: initialText,
          controller: controller,
          autofillHints: autoFillHints,
          autofocus: autoFocus,
          style: TextStyle(
            color: textColor,
            fontWeight: FontWeight.w400,
            fontSize: 15,
            fontStyle: FontStyle.normal,
          ),
          maxLines: maxLines,
          keyboardType: keyboardType,
          obscureText: obscureText,
          validator: validator,
          onEditingComplete: () => unfocus
              ? FocusScope.of(context!).unfocus()
              : FocusScope.of(context!).nextFocus(),
          onChanged: customOnChangeCallback,
          decoration: InputDecoration(
              filled: true,
fillColor: Colors.white,
            floatingLabelBehavior: FloatingLabelBehavior.always,
            labelText: labelText,
            //    fillColor: authFieldColor,
            focusedBorder: OutlineInputBorder(

              borderRadius: BorderRadius.circular(_radius),
              borderSide: BorderSide(
                color: Colors.white,
              ),
            ),
            hintText: hintText,
            labelStyle: TextStyle(
              color: Colors.black,
              fontSize: 13,
              height: 3,
            ),
            suffixIcon: suffixIcon,
            hintStyle: TextStyle(
              color: Colors.grey,
              // fontStyle: FontStyle.italic,
            ),
            errorStyle:  TextStyle(
              color: Colors.grey,
              // fontStyle: FontStyle.italic,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(_radius),
              borderSide: BorderSide(
                color: Colors.white,
              ),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(_radius),
              borderSide: BorderSide(
                color: Colors.white,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(_radius),
              borderSide: BorderSide(
                color: Colors.red,
              ),
            ),
          ),

      ),
    ),
  );
}

Widget customDropDownField({
  TextEditingController? controller,
  BuildContext? context,
  TextInputType? keyboardType,
  String? initialText,
  String hintText = '',
  String? formName,
  int maxLines = 1,
  bool readOnly = false,
  bool obscureText = false,
  bool unfocus = false,
  bool enforceLength = false,
  String labelText = '',
  bool autoFocus = false,
  required List<DropdownMenuItem<Object?>> items,
  var customOnChangeCallback,
  FormFieldValidator<Object>? validator,
  String errorString = 'this field is required',
  Widget suffixIcon = const SizedBox.shrink(),
  Widget hint = const Text('-- select --'),
}) {
  const double _radius = 12.0;

  return Padding(
    padding: EdgeInsets.all(12),
    child: Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(_radius),
      ),
      child: FormBuilderDropdown<Object?>(
        name: formName!,
        hint: hint,
        initialValue: initialText,
        autofocus: autoFocus,
        style: TextStyle(
          color: textColor,
          fontWeight: FontWeight.w400,
          fontSize: 15,
          fontStyle: FontStyle.normal,
        ),
        validator: validator,
        items: items,
        decoration: InputDecoration(
          //   filled: true,
          labelText: labelText,
          //    fillColor: authFieldColor,
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(_radius),
            borderSide: BorderSide(
              color: Colors.grey,
            ),
          ),
          hintText: hintText,
          labelStyle: TextStyle(
            color: Colors.black,
            fontSize: 13,
            height: 3,
          ),
          suffixIcon: suffixIcon,
          hintStyle: TextStyle(
            color: textColor,
            // fontStyle: FontStyle.italic,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(_radius),
            borderSide: BorderSide(
              color: Colors.grey.withOpacity(0.3),
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(_radius),
            borderSide: BorderSide(
              color: Colors.red,
            ),
          ),
        ),
      ),
    ),
  );
}

