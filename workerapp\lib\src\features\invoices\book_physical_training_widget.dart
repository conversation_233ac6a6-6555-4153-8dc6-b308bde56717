import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:ndialog/ndialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smart_calendar/controller/smart_calendar_controller.dart';
import 'package:smart_calendar/smart_calendar.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:work_link/src/widgets/ApiCallingWithoutProgressIndicator.dart';
import 'package:work_link/src/widgets/CustomProgressDialog.dart';
import 'package:work_link/src/utils/colors.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';

import '../../utils/color_constants.dart';
import '../../utils/UserPreference.dart';
import '../../widgets/edit_text.dart';
import '../../utils/constants.dart';
import '../../models/training-session/training_session.dart';
import '../../providers/data_repositories/data_repository.dart';
import '../../utils/dialog_service.dart';
import '../drawer/model/training_model.dart';

class BookPhysicalTrainingWidget extends StatefulWidget {

  BookPhysicalTrainingWidget(this.trainingSessionId);
  int trainingSessionId;

  @override
  BookPhysicalTrainingWidgetState createState() => BookPhysicalTrainingWidgetState();
}

class BookPhysicalTrainingWidgetState extends State<BookPhysicalTrainingWidget> {

  TrainingSession? wp;
  SharedPreferences? prefs;
  bool registeredOnline = false;

  init() async {
    prefs = await SharedPreferences.getInstance();
    String? workerId = await prefs!.getString(UserPreference.WORKER_ID);
    //getBookedDates(workerId);

    registeredOnline =  prefs!.getString(UserPreference.hascoId)!=null?true:false;

    getTrainingData();
  }

  getTrainingData() async {
    try {
      CustomProgressLoader.showLoader(context);
      Response? response = await ApiCalling()

          .apiCall(context, "$dataService/api/v1/training_session/find_training_sessions_by/${widget.trainingSessionId}", "get");
      CustomProgressLoader.cancelLoader(context);
      print("response++++++" + response.toString());
      if (response != null) {
        if (response.statusCode == 200) {
          wp = TrainingSession.fromJson(response.data);
          setState(() {

          });
        }
      }
    } catch (e) {

      print("issue shubh" + e.toString());
      return null;
    }
  }


  @override
  void initState() {
    // TODO: implement initState
    init();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {

    final monthLater =  DateTime.now().add(Duration(days: 30));
    final today =  DateTime.now();




    return Scaffold(
      appBar: AppAppBar(context,
          leading: Container(
            height: 30,
            width: 30,
            child: Padding(
              padding: const EdgeInsets.only(left: 15.0, right: 15),
              child: InkWell(
                child: Icon(   Icons.arrow_back,
                  color: welcomeTextColor,
                  size: 20,
                ),
                onTap: () {
                  routeBack(context);
                },
              ),
            ),
          )),
      body: SafeArea(
          child: Container(
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                    padding: const EdgeInsets.only(right: 13, top: 20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Padding(
                            padding: const EdgeInsets.only(left: 13.0, right: 15),
                            child: Image.asset(
                              "assets/drawer/training.png",
                              height: 23.0,
                              width: 23.0,
                            )),
                        Text(
                          'Book Fire Safety',
                          style: TextStyle(
                              color: Colors.black,
                              fontSize: 22,
                              fontWeight: FontWeight.bold),
                        )
                      ],
                    )),

                Padding(
                    padding: const EdgeInsets.only(left: 13.0, right: 13, top: 13),
                    child: Container(
                      height: 1.0,
                      color: Color(0xffA2A2A2),
                    )),

                Expanded(
                    flex: 1,
                    child:  Padding(
                      padding: const EdgeInsets.only(),
                      child: SingleChildScrollView( child:Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 13.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                EditText(
                                  hint: 'LOCATION',
                                  textEditingController: TextEditingController(text:wp?.shiftLocationName??""),
                                  keyboardType: TextInputType.text,
                                  validationMessage: "Please enter first name",
                                  maxLength: 30,
                                  color: inputBgColor,
                                  textColor: Colors.black,
                                  borderColor:Colors.transparent,
                                  hgt: 60.0,
                                  isHide: false,
                                  isValidation: true,
                                  isReadOnly: true,
                                ),
                                SizedBox(height: 10,),
                                Row(
                                  children: [

                                    Expanded(child:  EditText(
                                      hint: 'FIRST LINE OF ADDRESS',
                                      textEditingController: TextEditingController(text: wp?.address??""),
                                      keyboardType: TextInputType.text,
                                      validationMessage: "Manchester",
                                      maxLength: 30,
                                      color: inputBgColor,
                                      textColor: Colors.black,
                                      borderColor:Colors.transparent,
                                      hgt: 60.0,
                                      isHide: false,
                                      isValidation: true,
                                      isReadOnly: true,
                                    ),),

                                    Expanded(child:  EditText(
                                      hint: 'POST CODE',
                                      textEditingController: TextEditingController(text: wp?.postCode),
                                      keyboardType: TextInputType.text,
                                      validationMessage: "Please enter first name",
                                      maxLength: 30,
                                      color: inputBgColor,
                                      textColor: Colors.black,
                                      borderColor:Colors.transparent,
                                      hgt: 60.0,
                                      isHide: false,
                                      isValidation: true,
                                      isReadOnly: true,
                                    ),),

                                  ],
                                ),
                                SizedBox(height: 10,),
                                Row(
                                  children: [


                                    Expanded(child:  EditText(
                                      hint: 'START DATE',
                                      textEditingController: TextEditingController(text: wp?.startDateTime?.split(" ")[0]??""),
                                      keyboardType: TextInputType.text,
                                      validationMessage: "Manchester",
                                      maxLength: 30,
                                      color: inputBgColor,
                                      textColor: Colors.black,
                                      borderColor:Colors.transparent,
                                      hgt: 60.0,
                                      isHide: false,
                                      isValidation: true,
                                      isReadOnly: true,
                                    ),),
                                    Icon(
                                        Icons.arrow_forward
                                    ),

                                    Expanded(child:  EditText(
                                      hint: 'END DATE',
                                      textEditingController: TextEditingController(text: wp?.endDateTime?.split(" ")[0]??""),
                                      keyboardType: TextInputType.text,
                                      validationMessage: "Please enter first name",
                                      maxLength: 30,
                                      color: inputBgColor,
                                      textColor: Colors.black,
                                      borderColor:Colors.transparent,
                                      hgt: 60.0,
                                      isHide: false,
                                      isValidation: true,
                                      isReadOnly: true,
                                    ),),


                                  ],
                                ),
                                SizedBox(height: 10,),
                                Row(
                                  children: [


                                    Expanded(child:  EditText(
                                      hint: 'START TIME',
                                      textEditingController: TextEditingController(text:wp?.startDateTime?.split(" ")[1]??""),
                                      keyboardType: TextInputType.text,
                                      validationMessage: "Manchester",
                                      maxLength: 30,
                                      color: inputBgColor,
                                      textColor: Colors.black,
                                      borderColor:Colors.transparent,
                                      hgt: 60.0,
                                      isHide: false,
                                      isValidation: true,
                                      isReadOnly: true,
                                    ),),
                                    Icon(
                                        Icons.arrow_forward
                                    ),
                                    Expanded(child:  EditText(
                                      hint: 'END TIME',
                                      textEditingController: TextEditingController(text: wp?.endDateTime?.split(" ")[1]??""),
                                      keyboardType: TextInputType.text,
                                      validationMessage: "Manchester",
                                      maxLength: 30,
                                      color: inputBgColor,
                                      textColor: Colors.black,
                                      borderColor:Colors.transparent,
                                      hgt: 60.0,
                                      isHide: false,
                                      isValidation: true,
                                      isReadOnly: true,
                                    ),),

                                    Expanded(child:  EditText(
                                      hint: 'BREAK TIME(hrs)',
                                      textEditingController: TextEditingController(text: wp?.breakTime?.toString()??""),
                                      keyboardType: TextInputType.text,
                                      validationMessage: "Manchester",
                                      maxLength: 30,
                                      color: inputBgColor,
                                      textColor: Colors.black,
                                      borderColor:Colors.transparent,
                                      hgt: 60.0,
                                      isHide: false,
                                      isValidation: true,
                                      isReadOnly: true,
                                    ),),

                                  ],
                                ),
                                SizedBox(height: 10,),
                                Row(
                                  children: [


                                    Expanded(child:  EditText(
                                      hint: 'COST',
                                      textEditingController: TextEditingController(text: "£"+(wp?.trainingCost?.toString()??"")),
                                      keyboardType: TextInputType.text,
                                      validationMessage: "Manchester",
                                      maxLength: 30,
                                      color: inputBgColor,
                                      textColor: Colors.black,
                                      borderColor:Colors.transparent,
                                      hgt: 60.0,
                                      isHide: false,
                                      isValidation: true,
                                      isReadOnly: true,
                                    ),),
                                    Expanded(child:  EditText(
                                      hint: 'PAYER',
                                      textEditingController: TextEditingController(text: (wp?.isAgencyPaying??true)?"AGENCY":"WORKER"),
                                      keyboardType: TextInputType.text,
                                      validationMessage: "Manchester",
                                      maxLength: 30,
                                      color: inputBgColor,
                                      textColor: Colors.black,
                                      borderColor:Colors.transparent,
                                      hgt: 60.0,
                                      isHide: false,
                                      isValidation: true,
                                      isReadOnly: true,
                                    ),),



                                  ],
                                ),
                                SizedBox(height: 10,),
                                EditText(
                                  hint: 'NOTES',
                                  textEditingController: TextEditingController(text: wp?.notes),
                                  keyboardType: TextInputType.text,
                                  validationMessage: "Manchester",
                                  maxLength: 30,
                                  color: inputBgColor,
                                  textColor: Colors.black,
                                  borderColor:Colors.transparent,
                                  hgt: 60.0,
                                  isHide: false,
                                  isValidation: true,
                                  isReadOnly: false,
                                ),
                                SizedBox(height: 10,),
                                Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 8),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                          shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.all( Radius.circular(4),),
                                              side: BorderSide(color: welcomeTextColor)
                                          ),
                                          backgroundColor:  Colors.white,
                                        ),
                                        onPressed: () => routeBack(context),
                                        // icon: Icon(Icons.arrow_back_ios_new, color: welcomeTextColor,),
                                        child: Text('Cancel', style: TextStyle(color: welcomeTextColor),),
                                      ),
                                      SizedBox(width: 5,),
                                      ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                            elevation: 8,
                                            backgroundColor: welcomeTextColor
                                          // fixedSize: Size(width * 0.9, sy(40)),
                                        ),
                                        onPressed: () async {


                                          DialogService().showFloatingFlushbar(
                                            context: context,
                                            title: 'Book training',
                                            message:
                                            'Training was booked successfully, you can check under booked shifts',
                                          );

                                          Navigator.pop(context);
                                          Navigator.pop(context);
                                          Navigator.pop(context);
                                        },
                                        child: Row(children: [
                                          Text(
                                            'Book',
                                            style: TextStyle(
                                              color: Colors.white,
                                              // fontSize: sx(16),
                                            ),
                                          ),
                                          SizedBox(width: 5),
                                          Icon(Icons.add_box)
                                        ],),

                                      )
                                    ],
                                  ),)
                              ],
                            ),
                          ),




                        ],
                      ),),
                    ))
              ],
            ),
          )),
    );
  }
}
