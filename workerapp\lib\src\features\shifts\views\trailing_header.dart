import 'package:flutter/material.dart';
import 'package:work_link/src/features/shifts/data/shift_category.dart';

Widget trailingHeading(ShiftCategoryStatus status) {
  var w;

  switch (status.status) {
    case 'NEW':

      w = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(child: Center(
              child:  Text('View',  textAlign: TextAlign.center,)
          ),
              flex: 4),

          Expanded(child:  Center(
              child:Text('Book', textAlign: TextAlign.center,)
          ),
              flex: 4),
          Expanded(child:  Center(
              child:Text('Apply', textAlign: TextAlign.center,)
          ),
              flex: 4),

        ],
      );
      break;

    case 'CANCELLED':
      w = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(child: Center(
              child:  Text('View',  textAlign: TextAlign.center,)
          ),
              flex: 6),

          Expanded(child:  Center(
              child:Text('Status', textAlign: TextAlign.center,)
          ),
              flex: 6),

        ],
      );
      break;

    case 'AWAITING_AUTHORIZATION':
      w = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(child: Center(
              child:  Text('View',  textAlign: TextAlign.center,)
          ),
              flex: 6),

          Expanded(child:  Center(
              child:Text('Send\nReminder', textAlign: TextAlign.center,)
          ),
              flex: 6),

        ],
      );
      break;

    case 'BOOKED':
      w = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(child: Center(
              child:  Text('Carpooling',  textAlign: TextAlign.center,)
          ), flex: 6),
          Expanded(child: Center(
              child:  Text('View',  textAlign: TextAlign.center,)
          ), flex: 6),
          // Expanded(child:  Center(
          //     child:Text('Cancel', textAlign: TextAlign.center,)
          // ), flex: 6),

        ],
      );
      break;

    case 'APPLIED':
      w = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(child: Center(
              child:  Text('View',  textAlign: TextAlign.center,)
          ),
              flex: 6),

          Expanded(child:  Center(
              child:Text('Cancel', textAlign: TextAlign.center,)
          ),
              flex: 6),
        ],
      );
      break;

    case 'AUTHORIZED':
      w = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(child: Center(
              child:  Text('View',  textAlign: TextAlign.center,)
          ),
              flex: 4),

          Expanded(child:  Center(
              child:Text('Query', textAlign: TextAlign.center,)
          ),
              flex: 4),
          Expanded(child:  Center(
              child:Text('Release', textAlign: TextAlign.center,)
          ),
              flex: 4),

        ],
      );
      break;

    case 'IN_QUERY':
      w = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(child: Center(
              child:  Text('View',  textAlign: TextAlign.center,)
          ),
              flex: 12),

        ],
      );
      break;

    default:
      w = SizedBox.shrink();
  }

  return w;
}
