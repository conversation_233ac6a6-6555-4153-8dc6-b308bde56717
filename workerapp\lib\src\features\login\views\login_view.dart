// import 'package:alan_voice/alan_voice.dart';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:relative_scale/relative_scale.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:styled_widget/styled_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:work_link/src/providers/app_providers.dart';
import 'package:work_link/src/features/auth/logic/auth_provider.dart';
import 'package:work_link/src/features/home/<USER>/home_view.dart';
import 'package:work_link/src/features/splash/views/splash_view.dart';
import 'package:work_link/src/models/custom_exception.dart';
import 'package:work_link/src/models/profile/worker_profile.dart';
import 'package:work_link/src/utils/index.dart';
import 'package:work_link/src/widgets/index.dart';
import '../../../utils/UserPreference.dart';

import 'forgot_password.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

final rememberMeProvider = StateProvider<bool>((_) => false);

class LoginView extends ConsumerWidget {
  LoginView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context,   watch) {
    final authProvider = watch.watch(authNotifierProvider);
    final dialog = watch.watch(dialogProvider);
    final sharedPref = watch.watch(sharedPreferencesServiceProvider);
    var t = AppLocalizations.of(context);

    return SafeArea(
      child: Scaffold(
        body: RelativeBuilder(builder: (context, height, width, sy, sx) {
          return Center(
            child: Container(
              height: height,
              width: width,
              constraints: BoxConstraints(minWidth: 200, maxWidth: 600),
              child: sharedPref.getCachedUserCredentials() != null
                  ? FutureBuilder(
                future: watch.watch(authRepositoryProvider).login(sharedPref.getCachedUserCredentials()),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    final wp = snapshot.data;

                    if (wp is WorkerProfile) {

                      //watch.watch(_shiftProvider("tesyt"));

                      SchedulerBinding.instance!.addPostFrameCallback((_) {
                        return routeToWithClear(context, HomeView());
                      });
                    }
                  } else if (snapshot.hasError) {
                    return Center(
                      child: Column(
                        children: [
                          Text('Failed to login'),
                          SizedBox(height: 20),
                          ElevatedButton(
                              onPressed: () async {
                                await sharedPref.resetUserCredentials();

                                SchedulerBinding.instance!
                                    .addPostFrameCallback((_) {
                                  return routeToWithClear(
                                      context, LoginView());
                                });
                              },
                              child: Text('Retry')),
                        ],
                      ),
                    );
                  }

                  return Center(
                      child: Container(child: CircularProgressIndicator()));
                },
              )
                  : SingleChildScrollView(
                child: Column(
                  children: [
                    Padding(
                        padding: EdgeInsets.only(right: 100, left: 100, top: 150, bottom: 20),
                        child: Image.asset('assets/images/logo.png')
                    ),
                    GestureDetector(
                      onTap: (){
                        watch.read(authNotifierProvider.notifier).resetState();
                      },
                      child: Text(t?.appTitle??'')
                          .textColor(welcomeTextColor)
                          .fontWeight(FontWeight.w400)
                          .fontSize(sx(27)
                      ),
                    ),
                    SizedBox(height: 10),
                    authProvider.when(
                      initial: () => _Login(),
                      loading: () =>
                          Center(child: CircularProgressIndicator()),
                      data: (data)  {
                        // print(data);


                        SchedulerBinding.instance!
                            .addPostFrameCallback((_) {
                          dialog.showFloatingFlushbar(
                            context: context,
                            title: 'Login',
                            message: 'Login successful',
                          );
                        });



                        SchedulerBinding.instance!
                            .addPostFrameCallback((_) {
                          return routeToWithClear(context, HomeView());
                        });

                        return _Login();
                      },

                      loaded: (loaded) => Text(loaded.toString()),
                      // loaded: (e) {
                      //   SchedulerBinding.instance!
                      //       .addPostFrameCallback((_) {
                      //     dialog.showFloatingFlushbar(
                      //       context: context,
                      //       title: 'Login',
                      //       message: e.toString(),
                      //       warning: false,
                      //     );
                      //   });
                      // dialog.showFloatingFlushbar(context: context, title: 'Login', message: e.toString());

                      //   return _Login();
                      // },
                      error: (e) {

                        if(e=='PASSWORD_NEEDS_RESET'){

                          SchedulerBinding.instance!
                              .addPostFrameCallback((_) {
                            dialog.showFloatingFlushbar(
                              context: context,
                              title: 'Expired Password',
                              message: 'Your password has expired. You need to reset it.',
                              warning: true,
                            );
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) =>  ForgotPasswordView()),
                            );
                          });



                        }else {
                          SchedulerBinding.instance!
                              .addPostFrameCallback((_) {
                            dialog.showFloatingFlushbar(
                              context: context,
                              title: 'Login',
                              message: e.toString(),
                              warning: true,
                            );
                          });
                        }

                        return _Login();
                      },
                    ),
                    SizedBox(height: 40,),
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 20),
                        child: InkWell(
                          onTap: () => {},
                          child:RichText(
                            textAlign: TextAlign.center,
                            text: TextSpan(
                              text: 'By signing in, you accept our ',
                              style: TextStyle(color: textColor,fontSize: 16.0),
                              children: <TextSpan>[
                                TextSpan(
                                  // text: ' 5hrs 27mins',
                                  text: 'Terms and Conditions ',
                                  style: TextStyle(color: welcomeTextColor,fontSize: 16.0),
                                  recognizer:
                                  TapGestureRecognizer()
                                    ..onTap = () async{
                                      await launch("https://myworklink.uk/legal-information/");
                                    },
                                ), TextSpan(
                                  // text: ' 5hrs 27mins',
                                  text: '&',
                                  style: TextStyle(color: textColor,fontSize: 16.0),
                                  recognizer:
                                  TapGestureRecognizer()
                                    ..onTap = () async{
                                      await launch("https://myworklink.uk/legal-information/");
                                    },
                                ), TextSpan(
                                  // text: ' 5hrs 27mins',
                                  text: ' Privacy Policy',
                                  style: TextStyle(color: welcomeTextColor,fontSize: 16.0),
                                  recognizer:
                                  TapGestureRecognizer()
                                    ..onTap = () async{
                                      await launch("https://myworklink.uk/privacy-policy/");
                                    },
                                ),
                              ],
                            ),
                          )


                          /*Container(
                                    child: Row(
                                      children: [
                                        Text('By signing in, you accept our ')
                                            .textColor(textColor),
                                    InkWell(
                                        onTap: ()async{
                                          await launch("https://myworklink.uk/privacy-policy/");
                                        },
                                        child:Text(' Terms and Conditions')
                                            .textColor(logoBlue)),
                                      ],
                                    ),
                                  )*/,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}

class _Login extends ConsumerWidget {
  _Login({Key? key}) : super(key: key);

  @override
  void initState() {
    // TODO: implement initState

    // AlanVoice.addButton("7b078269080cd1a2d6432d3ff250c1f02e956eca572e1d8b807a3e2338fdd0dc/stage");
    // /// Handle commands from Alan Studio
    // AlanVoice.onCommand.add((command) {
    //   debugPrint("got new command ${command.toString()}");
    // });
  }

  final formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context,   watch) {
    final authProvider = watch.watch(authNotifierProvider);
    final rememberMe = watch.watch(rememberMeProvider);
    var t = AppLocalizations.of(context);

    return Container(
      padding: EdgeInsets.all(10),
      child: FormBuilder(
        key: formKey,
        child: AutofillGroup(
          child: Column(
            children: [
              formEntryField(
                context: context,
                autoFillHints: [AutofillHints.email],
                keyboardType: TextInputType.emailAddress,
                validator: FormBuilderValidators.compose(
                  [
                    FormBuilderValidators.required(errorText: 'required'),
                    FormBuilderValidators.email(errorText: 'Please enter valid email.'),
                  ],
                ),
                title: 'Email',
                formName: 'email',
              ),
              formEntryField(
                context: context,
                unfocus: true,
                formName: 'password',
                autoFillHints: [AutofillHints.password],
                keyboardType: TextInputType.visiblePassword,
                obscureText: true,
                title: 'Password',
                validator: FormBuilderValidators.compose(
                  [
                    FormBuilderValidators.required(),
                  ],
                ),
              ),

              SizedBox(height: 20),

              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                        padding: EdgeInsets.only(),
                        child: GestureDetector(
                          child: Text("Forgot Password", style: TextStyle(color: welcomeTextColor),),
                          onTap: (){
                            watch.read(authNotifierProvider.notifier).resetState();

                            Navigator.push(context, MaterialPageRoute(builder: (context) =>  ForgotPasswordView()),);
                          },

                        )
                    ),
                    SizedBox(width: 20),
                    Container(
                      width: 163,height: 47,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: welcomeTextColor,
                          elevation: 5,
                    
                          // fixedSize: Size(width * 0.9, sy(30)),
                        ),
                        onPressed: () async {
                          // watch.read(rememberMeProvider).state=true;
                          if (formKey.currentState!.validate()) {
                            formKey.currentState?.save();

                            SharedPreferences prefs = await SharedPreferences.getInstance();
                            prefs.setBool(UserPreference.afterLogin, true);

                            final _data = formKey.currentState?.value;

                            final payload = {
                              'username': _data!['email'],
                              'password': _data['password'],
                            };

                            if (!authProvider.isLoading) {
                              await watch.read(authNotifierProvider.notifier)
                                  .loginUser(
                                payload,
                                rememberMe: rememberMe,
                              );
                            }

                            TextInput.finishAutofillContext();
                          }
                        },
                        child: Text(
                          t?.login??'Login',
                          style: TextStyle(
                            color: Colors.white,
                            // fontSize: sx(25),
                          ),
                        ),
                      ),
                    ),


                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
