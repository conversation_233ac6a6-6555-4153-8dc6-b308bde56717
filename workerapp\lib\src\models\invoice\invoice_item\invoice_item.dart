
import 'package:freezed_annotation/freezed_annotation.dart';

import '../invoice.dart';

part 'invoice_item.freezed.dart';
part 'invoice_item.g.dart';

@freezed
abstract class InvoiceItem with _$InvoiceItem {
  factory InvoiceItem({

    int? shiftId,
    int? id,
    String? dayOfTheWeek,
    String? startTime,
    String? endTime,
    String? startDate,
    String? description,
    String? endDate,
    String? assignmentCode,
    String? client,
    int? clientId,
    double? numberOfHoursWorked,
    double? rate,
    double? total,
    String? directorate,
    String? worker,
    String? shiftType,


  }) = _InvoiceItem;

  factory InvoiceItem.fromJson(Map<String, dynamic> json) =>
      _$InvoiceItemFromJson(json);

}
